package com.ruoyi.storage.dataobtainconvert.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;


/**
 * <AUTHOR>
 * @ClassName
 * @Description 文件批次解析映射记录表
 * @date 2025/05/14 11:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "batch_analysis_mapping")
public class BatchAnalysisMapping extends BaseEntity3 {

    /**
     * 主键id
     */
    @Query(type = QueryType.IN)
    @TableId(type = IdType.AUTO)
    private String id;

    // ********************************* 独有字段 ************************************

    /**
     * 当前解析规则id
     */
    private String analysisRuleId;

    /**
     * 任务状态（处理中、已完成）
     */
    @Query(type = QueryType.EQ)
    private String taskStatus;

    /**
     * 原篇级数量
     */
    private Integer originalNum;

    /**
     * 映射篇级数量(总数==>成功+失败)
     */
    private Integer analysisTotalNum;

    /**
     * 映射篇级成功数量
     */
    private Integer analysisSuccessNum;

    /**
     * 映射篇级失败数量
     */
    private Integer analysisFailNum;

    /**
     * 审核状态（待审核、拒绝、通过）
     */
    @Query(type = QueryType.EQ)
    private String auditStatus;

    /**
     * 数据类型（期刊/会议等）
     */
    @Query(type = QueryType.EQ)
    private String dataType;

    /**
     * 任务开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date taskStartTime;

    /**
     * 任务结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date taskEndTime;

    // ********************************* 关联数据字段 ************************************

    /**
     * 批次ID
     */
    @Query(type = QueryType.LIKE)
    private String batchId;

    /**
     * 数据源ID（关联data_source.data_id）
     */
    @Query(type = QueryType.EQ)
    private String sourceId;

    /**
     * 数据源名称（OA/pubmed等）
     */
    private String source;

    /**
     * 收割类型（FTP/接口/线下导入）
     */
    private String type;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件数量
     */
    private Integer fileNum;

    /**
     * 拒绝理由
     */
    private String rejectedReason;

    /**
     * 是否重新处理 0-否 1-是
     */
    private Integer reprocessFlag;

    /**
     * 处理失败原因
     */
    private String failMsg;

}
