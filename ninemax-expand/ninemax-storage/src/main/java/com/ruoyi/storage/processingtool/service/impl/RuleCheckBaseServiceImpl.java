package com.ruoyi.storage.processingtool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.*;
import com.ruoyi.storage.processingtool.mapper.RuleCheckBaseMapper;
import com.ruoyi.storage.processingtool.service.IRuleCheckBaseService;
import com.ruoyi.storage.processingtool.service.IRuleCheckService;
import com.ruoyi.storage.processingtool.service.IRuleCheckValidationService;
import com.ruoyi.storage.processingtool.vo.AddRuleBaseReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleCheckBaseServiceImpl extends BaseServiceImpl<RuleCheckBaseMapper, RuleBase> implements IRuleCheckBaseService {

    @Autowired
    private IRuleCheckValidationService ruleCheckValidationService;
    @Autowired
    private IRuleCheckService ruleCheckService;

    // 在类中添加线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 新增解析规则
     *
     * @param addRuleBaseReqVO 新增参数
     */
    @Override
    public void addRuleCheckBase(AddRuleBaseReqVO addRuleBaseReqVO) {
        log.info("新增校验规则规则数据:{}", addRuleBaseReqVO);

        RuleBase ruleBase = RuleBase.convertToRuleBase(addRuleBaseReqVO);

        //  1、智能校验新增参数效验
        validationRuleCheckBase(ruleBase, true);

        // 2、解析规则保存
        this.save(ruleBase);

        // 3、创建规则文件
//        String sampleFile = addRuleBaseReqVO.getSampleFile();
//        // 处理sampleFile样例文件信息
//        String fileName = sampleFile.substring(sampleFile.lastIndexOf("/") + 1);
//        RuleCheckValidation ruleCheckValidation = RuleCheckValidation.builder()
//                .ruleId(ruleBase.getId())
//                .name(ruleBase.getName())
//                .fileName(fileName)
//                .filePath(sampleFile)
//                .build();
//        ruleCheckValidationService.save(ruleCheckValidation);
//
//        // todo 后期提取到统一的接口调用部分
//        // 3、根据规则文件进行工具接口调用 ==> 异步调用
//        log.info("智能校验规则数据：{} | 智能校验规则文件效验数据:{}", ruleBase, ruleCheckValidation);
    }

    @Override
    public void resendRuleCheckBase(String ruleId) {
        RuleBase ruleBase = this.getById(ruleId);
        RuleCheckValidation ruleCheckValidation = ruleCheckValidationService
                .getOne(new LambdaQueryWrapper<RuleCheckValidation>()
                        .eq(RuleCheckValidation::getRuleId, ruleId)
                        .eq(RuleCheckValidation::getType, 0)
                        .last("LIMIT 1"));

        // todo 后续完善校验规则进行文件效验操作逻辑

        log.info("重新发送：智能校验规则数据：{} | 智能校验规则文件效验数据:{}", ruleBase, ruleCheckValidation);
        // 使用线程池异步执行
//        executorService.execute(() -> toolInterfaceHandleService.toolInterfaceCall(HandleRuleAnalysisToolDTO.builder().ruleBase(ruleBase).ruleAnalysisValidation(ruleCheckValidation).initFlag(true).build()));
    }

    @Override
    public void updateRuleCheckBase(RuleBase ruleBase) {
        //  1、智能校验工具规则参数效验
        validationRuleCheckBase(ruleBase, false);
        this.updateById(ruleBase);
    }

    /**
     * 智能校验新增参数
     *
     * @param ruleBase 新增参数
     * @param isAdd    是否新增
     */
    private void validationRuleCheckBase(RuleBase ruleBase, boolean isAdd) {
        String ruleBaseName = ruleBase.getName();
        Assert.isFalse(StringUtils.isEmpty(ruleBaseName), "校验规则名称不能为空");
        LambdaQueryWrapper<RuleBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleBase::getName, ruleBaseName)
                .eq(RuleBase::getRuleType, "VALIDATE").last("LIMIT 1");
        RuleBase dbRuleBase = this.getOne(queryWrapper);
        if (isAdd) {
            Assert.isFalse(dbRuleBase != null, "校验规则名称已存在");
        } else {
            Assert.isFalse(dbRuleBase != null && !StringUtils.endsWithIgnoreCase(dbRuleBase.getId(), ruleBase.getId()), "校验规则名称已存在");
            if (dbRuleBase != null && StringUtils.equals(ruleBase.getUseStatus(), "Y")) {
                String checkStatus = ruleBase.getCheckStatus();
                Assert.isFalse(StringUtils.equals(checkStatus, "PENDING"), "校验规则启用失败，规则待处理");
                Assert.isFalse(StringUtils.equals(checkStatus, "REJECT"), "校验规则启用失败，规则已拒绝");
            }
        }
    }
}
