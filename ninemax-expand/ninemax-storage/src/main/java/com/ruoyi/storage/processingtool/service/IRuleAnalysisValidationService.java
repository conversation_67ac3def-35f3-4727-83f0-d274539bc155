package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface IRuleAnalysisValidationService extends IBaseService<RuleAnalysisValidation> {
    void validateRule(@Valid ValidateRuleReqVO validateRuleReqVO);
}
