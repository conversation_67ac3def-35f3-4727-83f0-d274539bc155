package com.ruoyi.storage.mongocommon.controller;

import com.anwen.mongo.model.PageParam;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.mongocommon.param.QuoteArticleParamVo;
import com.ruoyi.storage.mongocommon.service.IQuoteArticleMOService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 施引被引管理
 * @date 2025/7/23 15:35
 */
@RestController
@RequestMapping("/quoteArticle")
public class QuoteArticleMOController {

    @Resource
    private IQuoteArticleMOService quoteArticleMoService;

    /**
     * 查询施引被引列表
     * @param quoteArticleParamVo   查询参数
     * @return
     */
    @PostMapping("/quoteArticleList")
    public R<?> queryQuoteArticleList(@RequestBody QuoteArticleParamVo quoteArticleParamVo) {

        return R.ok(quoteArticleMoService.getQuoteArticlesByPage(quoteArticleParamVo, new PageParam(quoteArticleParamVo.getPageNum(), quoteArticleParamVo.getPageSize())));
    }

     /**
      * 修改施引被引
      * @param quoteArticleParamVo   修改参数
      * @return
      */
    @PostMapping("/updateQuoteArticle")
    public R<?> updateQuoteArticle(@RequestBody QuoteArticleParamVo quoteArticleParamVo) {
        quoteArticleMoService.updateQuoteArticle(quoteArticleParamVo);
        return R.ok();
    }

    /**
      * 删除施引被引
      * @param quoteArticleParamVo   删除参数
      * @return
      */
    @PostMapping("/deleteQuoteArticle")
    public R<?> deleteQuoteArticle(@RequestBody QuoteArticleParamVo quoteArticleParamVo) {
        quoteArticleMoService.deleteQuoteArticleByArticleId(quoteArticleParamVo.getRemoveData());
        return R.ok();
    }

    /**
      * 通过施引文献id获取施引列表
      * @param quoteArticleParamVo   获取参数
      * @return
      */
    @GetMapping("/getRefArticleList")
    public R<?> getRefArticleList(@RequestBody QuoteArticleParamVo quoteArticleParamVo) {

        return R.ok(quoteArticleMoService.getQuoteArticleListByRefIds(quoteArticleParamVo));
    }

    /**
      * 获取被引列表
      * @param quoteArticleParamVo   获取参数
      * @return
      */
    @GetMapping("/getCitedArticleList")
    public R<?> getCitedArticleList(@RequestBody QuoteArticleParamVo quoteArticleParamVo) {
        // quoteArticleMoService.getQuoteArticleListByCitedIds(quoteArticleParamVo)
        return R.ok();
    }

    @GetMapping("/getQuoteArticleDetail/{id}")
    public R<?> getQuoteArticleDetail(@PathVariable String id) {
        return R.ok(quoteArticleMoService.getQuoteArticleDetail(id));
    }
}
