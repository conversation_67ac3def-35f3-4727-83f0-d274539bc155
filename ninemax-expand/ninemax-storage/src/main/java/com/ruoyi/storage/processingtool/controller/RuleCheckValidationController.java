package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleCheckValidation;
import com.ruoyi.storage.processingtool.mapper.RuleCheckValidationMapper;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisValidationService;
import com.ruoyi.storage.processingtool.service.IRuleCheckValidationService;
import com.ruoyi.storage.processingtool.service.impl.RuleCheckValidationServiceImpl;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleCheckValidation")
@Validated
public class RuleCheckValidationController extends BaseController<RuleCheckValidationMapper, RuleCheckValidation> {
    RuleCheckValidationController(RuleCheckValidationServiceImpl service) {
        super(service, RuleCheckValidation.class);
    }

    @Autowired
    private IRuleCheckValidationService ruleCheckValidationService;

    /**
     * 校验规则文件效验
     *
     * @param validateRuleReqVO 实体对象
     * @return 操作结果
     */
    @PostMapping("/validateRule")
    public R<?> updateUseStatus(@Valid @RequestBody ValidateRuleReqVO validateRuleReqVO) {
        ruleCheckValidationService.validateRule(validateRuleReqVO);
        return R.ok();
    }

}
