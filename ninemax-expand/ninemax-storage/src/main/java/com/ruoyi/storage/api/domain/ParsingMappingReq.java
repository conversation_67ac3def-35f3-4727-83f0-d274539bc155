package com.ruoyi.storage.api.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ParsingMappingReq {

    private List<File> files;

    private List<AnalysisMappingRule> rules;

    private List<FileChecks> checks;

    private RuleInfo info;

    private String callbackUrl;
}
