package com.ruoyi.storage.processingtool.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.storage.api.domain.AnalysisMappingRule;
import com.ruoyi.storage.api.domain.ParsingMappingResp;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleAnalysis;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.mapper.RuleAnalysisMapper;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisService;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisValidationService;
import com.ruoyi.storage.processingtool.service.IRuleBaseService;
import com.ruoyi.storage.processingtool.vo.AddRuleAnalysisReqVO;
import com.ruoyi.storage.processingtool.vo.UpdateRuleAnalysisStatusReqVO;
import com.ruoyi.storage.utils.QueryCommon;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleAnalysisServiceImpl extends BaseServiceImpl<RuleAnalysisMapper, RuleAnalysis> implements IRuleAnalysisService {

    @Autowired
    private IRuleAnalysisValidationService ruleAnalysisValidationService;

    @Autowired
    private IRuleBaseService ruleBaseService;

    @Override
    public IPage<RuleAnalysis> queryList(RuleAnalysis ruleAnalysis, Page<RuleAnalysis> page) {
        QueryWrapper<RuleAnalysis> queryWrapper = QueryCommon.buildQueryWrapper(ruleAnalysis);
        return this.page(page, queryWrapper);
    }

    /**
     * 处理结果数据
     *
     * @param resp 工具接口返回的数据结果
     * @param ruleId                    规则id
     * @param ruleName                  规则名称
     * @param ruleValidId               规则效验id
     */
    @Override
    public void handleResultData(ParsingMappingResp resp, String ruleId, String ruleName, String ruleValidId) {
        log.info("处理结果数据：{}", resp);
        List<RuleAnalysis> ruleAnalysisList = new ArrayList<>();
        List<AnalysisMappingRule> rules = resp.getRules();
        for (AnalysisMappingRule ruleAnalysisDTO : rules) {
            // 处理规则对应标签和属性
            String imiPath = ruleAnalysisDTO.getImiPath();
            String attribute = imiPath.substring(imiPath.lastIndexOf('/') + 1);
            RuleAnalysis ruleAnalysis = RuleAnalysis.builder()
                    .imiPath(ruleAnalysisDTO.getImiPath())
                    .status(ruleAnalysisDTO.getStatus())
                    .pathId(ruleAnalysisDTO.getPathId())
                    .iskey(ruleAnalysisDTO.getIskey())
                    .sort(ruleAnalysisDTO.getSort())
                    .sampleValue(StrUtil.join(",",ruleAnalysisDTO.getSampleValue()))
                    .samplePath(StrUtil.join(",",ruleAnalysisDTO.getSamplePath()))
                    .frequency(ruleAnalysisDTO.getFrequency())
                    .srcPath(ruleAnalysisDTO.getSrcPath())
                    .ruleId(ruleId)
                    .ruleValidId(ruleValidId)
                    .imiId("test")
                    .imiLabel(attribute)
                    .imiType(attribute.contains("@") ? "属性" : "元素")
                    .build();
            ruleAnalysisList.add(ruleAnalysis);
        }

        // 进行路径ID\排序码的生成
        if (!ruleAnalysisList.isEmpty()) {
            // 批量生成路径ID和排序码
            generateBatchPathIdAndSort(ruleAnalysisList, ruleId, ruleValidId);
            // todo 后续完善IMI标签和属性字段处理
            this.saveOrUpdateBatch(ruleAnalysisList);
        }
    }

    /**
     * 更新规则分析状态
     *
     * @param updateAnalysisStatusReqVO 更新规则分析状态请求对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAnalysisStatus(UpdateRuleAnalysisStatusReqVO updateAnalysisStatusReqVO) {
        List<String> ids = updateAnalysisStatusReqVO.getIds();
        String status = updateAnalysisStatusReqVO.getStatus();
        // 1、进行数据状态的更新
        if (ids != null && !ids.isEmpty() && StringUtils.isNotBlank(status)) {
            LambdaUpdateWrapper<RuleAnalysis> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(RuleAnalysis::getId, ids);
            updateWrapper.set(RuleAnalysis::getAnalysisStatus, status);
            this.update(null, updateWrapper);
        }
        // 2、判断当前是否存在待处理节点
        judgeExistPendingNode(updateAnalysisStatusReqVO.getRuleId(), updateAnalysisStatusReqVO.getRuleValidId());

    }

    /**
     * 判断当前是否存在待处理节点
     *
     * @param ruleId      规则ID
     * @param ruleValidId 规则效验ID
     */
    private void judgeExistPendingNode(@NotBlank(message = "规则ID不能为空") String ruleId, String ruleValidId) {
        RuleAnalysisValidation ruleAnalysisValidation = ruleAnalysisValidationService.getById(ruleValidId);
        if (ruleAnalysisValidation != null && ruleAnalysisValidation.getType() == 0) {
            LambdaQueryWrapper<RuleAnalysis> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RuleAnalysis::getRuleValidId, ruleValidId);
            queryWrapper.eq(RuleAnalysis::getAnalysisStatus, "H");
            long count = this.count(queryWrapper);
            if (count == 0) {
                // 更新当前解析规则的状态
                ruleBaseService.updateById(RuleBase.builder().id(ruleId).ruleStatus("COMPLETED").build());
            }
        }
    }

    /**
     * 新增规则分析
     *
     * @param addRuleAnalysisReqVO 新增规则分析请求对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addRuleAnalysis(AddRuleAnalysisReqVO addRuleAnalysisReqVO) {
        String ruleId = addRuleAnalysisReqVO.getRuleId();
        String ruleValidId = addRuleAnalysisReqVO.getRuleValidId();

        // 获取规则基础信息
        RuleBase ruleBase = ruleBaseService.getById(ruleId);
        if (ruleBase == null) {
            throw new RuntimeException("规则不存在，ruleId: " + ruleId);
        }

        // 创建新的规则分析数据
        RuleAnalysis newRuleAnalysis = RuleAnalysis.builder()
                .ruleId(ruleId)
                .ruleValidId(ruleValidId)
                .remark("手动新增")
                .build();

        // 生成路径ID和排序码（参考handleResultData的逻辑）
        generatePathIdAndSort(newRuleAnalysis, ruleId, ruleValidId);

        // 保存新数据
        newRuleAnalysis.setSort(0);
        this.save(newRuleAnalysis);

        log.info("成功新增规则分析数据，ruleId: {}, pathId: {}, sort: {}",
                ruleId, newRuleAnalysis.getPathId(), newRuleAnalysis.getSort());

        // 更新当前解析规则的状态
        ruleBaseService.updateById(RuleBase.builder().id(ruleId).ruleStatus("PENDING").build());
        log.info("更新规则状态为PENDING，ruleId: {}", ruleId);
    }

    /**
     * 处理新增的规则分析数据
     *
     * @param rules                  新增的规则分析数据
     * @param ruleAnalysisValidation 规则验证对象
     */
    @Override
    public void handleNewRuleAnalysis(List<AnalysisMappingRule> rules, RuleAnalysisValidation ruleAnalysisValidation) {
        // 处理发现的对则节点信息
        log.info("处理发现的新节点信息：解析效验文件信息==>{}, 新规则节点==>{}", ruleAnalysisValidation, rules);
        // 1、查询出当前对应规则节点的规则集合
        String ruleValidId = ruleAnalysisValidation.getId();
        String ruleId = ruleAnalysisValidation.getRuleId();
        LambdaQueryWrapper<RuleAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleAnalysis::getRuleValidId, ruleValidId)
                .eq(RuleAnalysis::getRuleId, ruleId);
        List<RuleAnalysis> list = this.list(queryWrapper);

        // 2、对当前新规则进行去重
        // 将现有规则转换为srcPath+imiPath的组合Set
        Set<String> existingPaths = list.stream()
                .map(rule -> rule.getSrcPath() + "|" + rule.getImiPath())
                .collect(Collectors.toSet());

        // 过滤出新的规则数据
        List<AnalysisMappingRule> newRules = rules.stream()
                .filter(rule -> !existingPaths.contains(rule.getSrcPath() + "|" + rule.getImiPath()))
                .toList();

        // 3、对新的负责数据进行保存
        if (!newRules.isEmpty()) {
            // 这里可以添加保存新规则的逻辑
            log.info("保存新的规则分析数据：{}", newRules);
            List<RuleAnalysis> ruleAnalysisList = new ArrayList<>();
            for (AnalysisMappingRule ruleAnalysisDTO : rules) {
                // 处理规则对应标签和属性
                String imiPath = ruleAnalysisDTO.getImiPath();
                String attribute = imiPath.substring(imiPath.lastIndexOf('/') + 1);
                RuleAnalysis ruleAnalysis = RuleAnalysis.builder()
                        .imiPath(ruleAnalysisDTO.getImiPath())
                        .status(ruleAnalysisDTO.getStatus())
                        .pathId(ruleAnalysisDTO.getPathId())
                        .iskey(ruleAnalysisDTO.getIskey())
                        .sort(ruleAnalysisDTO.getSort())
                        .sampleValue(StrUtil.join(",",ruleAnalysisDTO.getSampleValue()))
                        .samplePath(StrUtil.join(",",ruleAnalysisDTO.getSamplePath()))
                        .frequency(ruleAnalysisDTO.getFrequency())
                        .srcPath(ruleAnalysisDTO.getSrcPath())
                        .ruleId(ruleId)
                        .ruleValidId(ruleValidId)
                        .imiId("test")
                        .imiLabel(attribute)
                        .imiType(attribute.contains("@") ? "属性" : "元素")
                        .build();
                ruleAnalysisList.add(ruleAnalysis);
            }
            // 批量生成路径ID和排序码
            generateBatchPathIdAndSort(ruleAnalysisList, ruleId, ruleValidId);
            // todo 后续完善IMI标签和属性字段处理
            ruleAnalysisList.forEach(ruleAnalysis -> ruleAnalysis.setSort(0));
            this.saveBatch(ruleAnalysisList);

            if (!ruleAnalysisList.isEmpty()) {
                // 4、更新当前解析规则的状态
                ruleBaseService.updateById(RuleBase.builder().id(ruleId).ruleStatus("PENDING").build());
            }
        }
        // 3、对新的负责数据进行保存
    }

    @Override
    public List<RuleAnalysis> getRuleAnalysisListByAnalysisRuleId(String analysisRuleId, String analysisValidationId) {
        return this.baseMapper.queryRuleAnalysisListByAnalysisRuleId(analysisRuleId, analysisValidationId);
    }

    /**
     * 生成路径ID和排序码的通用方法
     *
     * @param ruleAnalysis 规则分析对象
     * @param ruleId       规则ID
     * @param ruleValidId  规则验证ID
     */
    private void generatePathIdAndSort(RuleAnalysis ruleAnalysis, String ruleId, String ruleValidId) {
        // 1. 生成路径ID前缀
        String today = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
        String pathIdPrefix = "P" + today;

        // 2. 查询数据库中今天已存在的最大路径ID
        LambdaQueryWrapper<RuleAnalysis> pathIdQuery = new LambdaQueryWrapper<>();
        pathIdQuery.likeRight(RuleAnalysis::getPathId, pathIdPrefix);
        pathIdQuery.orderByDesc(RuleAnalysis::getPathId);
        pathIdQuery.last("limit 1");
        RuleAnalysis maxRuleAnalysis = this.getOne(pathIdQuery);

        int startIndex = 1;
        if (maxRuleAnalysis != null && maxRuleAnalysis.getPathId() != null) {
            String maxPathId = maxRuleAnalysis.getPathId();
            String numStr = maxPathId.substring(pathIdPrefix.length());
            try {
                startIndex = Integer.parseInt(numStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析路径ID序号失败，使用默认值1，maxPathId: {}", maxPathId);
                startIndex = 1;
            }
        }

        // 3. 查询数据库中当前ruleId和ruleValidId下的最大排序码
        LambdaQueryWrapper<RuleAnalysis> sortQuery = new LambdaQueryWrapper<>();
        sortQuery.eq(RuleAnalysis::getRuleId, ruleId);
        if (StringUtils.isNotBlank(ruleValidId)) {
            sortQuery.eq(RuleAnalysis::getRuleValidId, ruleValidId);
        }
        sortQuery.orderByDesc(RuleAnalysis::getSort);
        sortQuery.last("limit 1");
        RuleAnalysis maxSortAnalysis = this.getOne(sortQuery);

        int sort = 1;
        if (maxSortAnalysis != null && maxSortAnalysis.getSort() != null) {
            sort = maxSortAnalysis.getSort() + 1;
        }

        // 4. 设置生成的路径ID和排序码
        String pathId = pathIdPrefix + String.format("%03d", startIndex);
        ruleAnalysis.setPathId(pathId);
        ruleAnalysis.setSort(sort);

        log.debug("生成路径ID: {}, 排序码: {}", pathId, sort);
    }

    /**
     * 批量生成路径ID和排序码的方法
     *
     * @param ruleAnalysisList 规则分析对象列表
     * @param ruleId           规则ID
     * @param ruleValidId      规则验证ID
     */
    private void generateBatchPathIdAndSort(List<RuleAnalysis> ruleAnalysisList, String ruleId, String ruleValidId) {
        if (ruleAnalysisList == null || ruleAnalysisList.isEmpty()) {
            return;
        }

        // 1. 生成路径ID前缀
        String today = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
        String pathIdPrefix = "P" + today;

        // 2. 查询数据库中今天已存在的最大路径ID
        LambdaQueryWrapper<RuleAnalysis> pathIdQuery = new LambdaQueryWrapper<>();
        pathIdQuery.likeRight(RuleAnalysis::getPathId, pathIdPrefix);
        pathIdQuery.orderByDesc(RuleAnalysis::getPathId);
        pathIdQuery.last("limit 1");
        RuleAnalysis maxRuleAnalysis = this.getOne(pathIdQuery);

        int startIndex = 1;
        if (maxRuleAnalysis != null && maxRuleAnalysis.getPathId() != null) {
            String maxPathId = maxRuleAnalysis.getPathId();
            String numStr = maxPathId.substring(pathIdPrefix.length());
            try {
                startIndex = Integer.parseInt(numStr) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析路径ID序号失败，使用默认值1，maxPathId: {}", maxPathId);
                startIndex = 1;
            }
        }

        // 3. 查询数据库中当前ruleId和ruleValidId下的最大排序码
        LambdaQueryWrapper<RuleAnalysis> sortQuery = new LambdaQueryWrapper<>();
        sortQuery.eq(RuleAnalysis::getRuleId, ruleId);
        if (StringUtils.isNotBlank(ruleValidId)) {
            sortQuery.eq(RuleAnalysis::getRuleValidId, ruleValidId);
        }
        sortQuery.orderByDesc(RuleAnalysis::getSort);
        sortQuery.last("limit 1");
        RuleAnalysis maxSortAnalysis = this.getOne(sortQuery);

        int sort = 1;
        if (maxSortAnalysis != null && maxSortAnalysis.getSort() != null) {
            sort = maxSortAnalysis.getSort() + 1;
        }

        // 4. 为新数据批量分配路径ID和排序码
        for (RuleAnalysis ruleAnalysis : ruleAnalysisList) {
            String pathId = pathIdPrefix + String.format("%03d", startIndex++);
            ruleAnalysis.setPathId(pathId);
            ruleAnalysis.setSort(sort++);
        }

        log.debug("批量生成路径ID和排序码完成，共处理 {} 条数据", ruleAnalysisList.size());
    }
}
