package com.ruoyi.storage.datapublish.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseQueryEntity;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 数据发布记录表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "data_publish")
public class DataPublish extends BaseQueryEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 名称
     */
    @Query(type = QueryType.LIKE)
    private String name;

}
