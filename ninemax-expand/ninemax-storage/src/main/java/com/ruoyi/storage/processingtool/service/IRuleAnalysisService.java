package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.api.domain.AnalysisMappingRule;
import com.ruoyi.storage.api.domain.ParsingMappingResp;
import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleAnalysis;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.vo.AddRuleAnalysisReqVO;
import com.ruoyi.storage.processingtool.vo.UpdateRuleAnalysisStatusReqVO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IRuleAnalysisService extends IBaseService<RuleAnalysis> {

    /**
     * 处理结果数据
     *
     * @param resp 工具接口返回的数据结果
     * @param ruleId                    规则id
     * @param ruleName                  规则名称
     * @param ruleValidId               规则效验id
     */
    void handleResultData(ParsingMappingResp resp, String ruleId, String ruleName, String ruleValidId);

    /**
     * 更新规则分析状态
     *
     * @param updateAnalysisStatusReqVO 更新规则分析状态对象
     */
    void updateAnalysisStatus(@Valid UpdateRuleAnalysisStatusReqVO updateAnalysisStatusReqVO);

    /**
     * 新增规则分析
     *
     * @param addRuleAnalysisReqVO 新增规则分析对象
     */
    void addRuleAnalysis(@Valid AddRuleAnalysisReqVO addRuleAnalysisReqVO);

    /**
     * 处理新增规则分析
     *
     * @param rules                  规则分析集合
     * @param ruleAnalysisValidation 规则分析效验对象
     */
    void handleNewRuleAnalysis(List<AnalysisMappingRule> rules, RuleAnalysisValidation ruleAnalysisValidation);

    /**
     * 根据规则分析id获取规则分析列表
     *
     * @param analysisRuleId       规则分析id
     * @param analysisValidationId 规则效验id
     * @return 规则分析列表
     */
    List<RuleAnalysis> getRuleAnalysisListByAnalysisRuleId(String analysisRuleId, String analysisValidationId);
}
