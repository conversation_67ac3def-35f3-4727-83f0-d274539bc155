package com.ruoyi.storage.datapublish.service;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.datapublish.domain.DataPublish;
import com.ruoyi.storage.datapublish.vo.StartPublishDataReqVO;
import org.bson.Document;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface IDataPublishService extends IBaseService<DataPublish>{

    /**
     * 启动数据发布
     * @param startPublishDataReqVO 请求参数
     */
    R<?> startPublishData(@Valid StartPublishDataReqVO startPublishDataReqVO);

    R<?> publishPubmedData(@Valid StartPublishDataReqVO startPublishDataReqVO);

    R<?> publishNstlData(@Valid StartPublishDataReqVO startPublishDataReqVO);

    R<?> pubmedData(@Valid StartPublishDataReqVO startPublishDataReqVO);

    R<?> publishAnnotationData(@Valid StartPublishDataReqVO startPublishDataReqVO);

    /**
     * 发布IMI格式数据到ES
     * @param document MongoDB文档对象
     * @param indexName ES索引名称
     * @param esDocumentIdField 文档中用作ES唯一标识的顶级字段名称（可为null，系统将自动生成ID）
     * @return 发布结果
     */
    R<?> publishImiData(Document document, String indexName, String esDocumentIdField);

    /**
     * 发布基础数据到ES
     * @param document MongoDB文档对象
     * @param indexName ES索引名称
     * @param esDocumentIdField 文档中用作ES唯一标识的顶级字段名称（可为null，系统将自动生成ID）
     * @return 发布结果
     */
    R<?> publishBasicData(Document document, String indexName, String esDocumentIdField);
}
