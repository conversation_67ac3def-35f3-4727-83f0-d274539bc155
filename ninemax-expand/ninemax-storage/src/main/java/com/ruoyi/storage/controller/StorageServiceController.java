package com.ruoyi.storage.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.storage.config.ElasticSearchRestClient;
import com.ruoyi.storage.config.MongoUtils;
import com.ruoyi.storage.utils.MongoConstants;
import com.ruoyi.storage.utils.Constants;
import com.ruoyi.storage.utils.ParseRecordUtil;
import com.ruoyi.storage.utils.QueryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.minio.MinioClient;
import io.minio.GetObjectArgs;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据源Controller
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@RestController
@RequestMapping("/storageService")
@Slf4j
public class StorageServiceController extends BaseController {

    @Autowired
    private ElasticSearchRestClient esClient;

    @Autowired
    private MongoUtils mongoUtils;

    @Autowired
    private MinioClient minioClient;

    @Value("${minio.bucketName}")
    private String bucketName;

    /**
     * 获取单源品种列表
     */
    @GetMapping("/singleJournal/list")
    public TableDataInfo listSingleJournals(
            @RequestParam(required = false) String singleJournalId,
            @RequestParam(required = false) String sourceTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(singleJournalId)) {
                boolQuery.must(QueryBuilders.termQuery("singleJournalId.keyword", singleJournalId));
            }
            if (StringUtils.isNotBlank(sourceTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("sourceTitle", sourceTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }

            // 执行普通查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_JOURNAL_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();

                // 处理年卷期区间字段
                QueryUtil.processIntervalField(sourceMap);

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 计算关联的篇级数量
                QueryUtil.countArticles(sourceMap, esClient);

                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_JOURNAL_PRODUCT);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (IOException e) {
            log.error("多源品种ES查询异常", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取单源品种列表(适用于深度翻页)
     */
    @GetMapping("/singleJournal/scroll")
    public TableDataInfo scrollSingleJournals(
            @RequestParam(required = false) String singleJournalId,
            @RequestParam(required = false) String sourceTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String scrollId,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(singleJournalId)) {
                boolQuery.must(QueryBuilders.termQuery("singleJournalId.keyword", singleJournalId));
            }
            if (StringUtils.isNotBlank(sourceTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("sourceTitle", sourceTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }

            // 执行scroll查询
            SearchHits hits = esClient.scrollSearch(boolQuery, Constants.SINGLE_JOURNAL_PRODUCT, scrollId, pageSize).getHits();
            String newScrollId = esClient.scrollSearch(boolQuery, Constants.SINGLE_JOURNAL_PRODUCT, scrollId, pageSize).getScrollId();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();

                // 处理年卷期区间字段
                QueryUtil.processIntervalField(sourceMap);

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 计算关联的篇级数量
                QueryUtil.countArticles(sourceMap, esClient);

                sourceMap.put("scrollId", newScrollId);  // 将scrollId添加到结果中
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_JOURNAL_PRODUCT);

            // 创建TableDataInfo对象并设置属性
            TableDataInfo rspData = QueryUtil.buildTableDataInfo(list, count);

            // 如果没有更多数据,清除scroll
            if (list.isEmpty() && StringUtils.isNotBlank(scrollId)) {
                esClient.clearScroll(scrollId);
            }

            return rspData;

        } catch (IOException e) {
            log.error("多源品种ES scroll查询异常", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取单源品种详情
     */
    @GetMapping("/singleJournal/{journalId}")
    public AjaxResult getSingleJournalInfo(@PathVariable("journalId") String journalId) {
        try {
            BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            boolQuery.must(QueryBuilders.termQuery("singleJournalId.keyword", journalId));

            log.info("从ES查询多源品种详情，journalId: {}", journalId);

            // 执行查询
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_JOURNAL_PRODUCT, null, 1, 1).getHits();

            if (hits.getHits().length > 0) {
                Map<String, Object> sourceMap = hits.getHits()[0].getSourceAsMap();

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 处理年卷期区间字段
                QueryUtil.processIntervalField(sourceMap);

                // 计算关联的篇级数量
                QueryUtil.countArticles(sourceMap, esClient);

                return success(sourceMap);
            }
        } catch (Exception e) {
            log.error("获取多源品种详情失败: {}", e.getMessage());
        }
        return AjaxResult.error("获取多源品种详情失败");
    }

    /**
     * 获取单源品种卷期列表
     */
    @GetMapping("/singleJournal/volumes/{journalId}")
    public TableDataInfo getSingleJournalVolumes(
            @PathVariable("journalId") String journalId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String year,
            @RequestParam(required = false) String volume,
            @RequestParam(required = false) String issue
    ) {
        try {
            // 从ES中查询关联的卷期信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("singleJournalId.keyword", journalId));

            // 添加年份、卷号、期号的筛选条件
            if (StringUtils.isNotBlank(year)) {
                boolQuery.must(QueryBuilders.termQuery("year", year));
            }
            if (StringUtils.isNotBlank(volume)) {
                boolQuery.must(QueryBuilders.termQuery("volume", volume));
            }
            if (StringUtils.isNotBlank(issue)) {
                boolQuery.must(QueryBuilders.termQuery("issue", issue));
            }

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("year", SortOrder.DESC);
            sort.put("volume", SortOrder.DESC);
            sort.put("issue", SortOrder.DESC);

            log.info("查询多源品种卷期数据，查询条件: {}", boolQuery);

            // 执行分页查询
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ISSUE_PRODUCT, null, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> volumes = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                Map<String, Object> volumeInfo = new HashMap<>();
                volumeInfo.put("singleIssueId", sourceMap.get("singleIssueId"));
                volumeInfo.put("volume", sourceMap.get("volume"));
                volumeInfo.put("issue", sourceMap.get("issue"));
                volumeInfo.put("year", sourceMap.get("year"));

                // 如果有文章ID列表，添加文章数量
                Object articleIds = sourceMap.get("articleIds");
                if (articleIds instanceof List) {
                    volumeInfo.put("articleCount", ((List<?>) articleIds).size());
                } else {
                    volumeInfo.put("articleCount", 0);
                }

                // 添加更新时间
                volumeInfo.put("updateTime", sourceMap.get("updateTime"));
                formatUpdateTime(volumeInfo);

                volumes.add(volumeInfo);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ISSUE_PRODUCT);
            log.info("查询到卷期总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            TableDataInfo rspData = new TableDataInfo();
            rspData.setCode(200);
            rspData.setRows(volumes);
            rspData.setTotal(count);
            return rspData;
        } catch (Exception e) {
            log.error("获取多源品种卷期列表失败", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取单源品种篇级列表
     */
    @GetMapping("/singleJournal/articles/{journalId}")
    public TableDataInfo getSingleJournalArticles(
            @PathVariable("journalId") String journalId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String year,
            @RequestParam(required = false) String volume,
            @RequestParam(required = false) String issue,
            @RequestParam(required = false) String articleTitle
    ) {
        try {
            // 从ES中查询关联的篇级信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("singleJournalId.keyword", journalId));

            // 添加年份、卷号、期号、文章标题的筛选条件
            if (StringUtils.isNotBlank(year)) {
                boolQuery.must(QueryBuilders.termQuery("year", year));
            }
            if (StringUtils.isNotBlank(volume)) {
                boolQuery.must(QueryBuilders.termQuery("volume", volume));
            }
            if (StringUtils.isNotBlank(issue)) {
                boolQuery.must(QueryBuilders.termQuery("issue", issue));
            }
            if (StringUtils.isNotBlank(articleTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("articleTitle", articleTitle));
            }

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            log.info("查询多源品种篇级数据，查询条件: {}", boolQuery);

            // 执行分页查询
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> articles = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();

                // 构建interval字段（年/卷/期）
                StringBuilder interval = new StringBuilder();
                Object year_obj = sourceMap.get("year");
                Object volume_obj = sourceMap.get("volume");
                Object issue_obj = sourceMap.get("issue");

                if (year_obj != null) {
                    interval.append(year_obj);
                    if (volume_obj != null) {
                        interval.append("/").append(volume_obj);
                        if (issue_obj != null) {
                            interval.append("/").append(issue_obj);
                        }
                    }
                }

                if (interval.length() > 0) {
                    sourceMap.put("interval", interval.toString());
                }

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                articles.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT);
            log.info("查询到篇级总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(articles, count);
        } catch (Exception e) {
            log.error("获取多源品种篇级列表失败", e);
            return getErrorDataTable(e.getMessage());
        }
    }


    /**
     * 获取单源卷期列表
     */
    @GetMapping("/singleIssue/list")
    public TableDataInfo listSingleIssues(
            @RequestParam(required = false) String singleIssueId,
            @RequestParam(required = false) String sourceTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(singleIssueId)) {
                boolQuery.must(QueryBuilders.termQuery("singleIssueId.keyword", singleIssueId));
            }
            if (StringUtils.isNotBlank(sourceTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("sourceTitle", sourceTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }

            // 如果页码超过阈值,需要切换到scroll模式
            if (pageNum > 100) {
                return listMutchIssuesByScroll(singleIssueId, sourceTitle, issn, state, null, pageSize);
            }

            // 执行普通查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ISSUE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 获取所属品种信息
                QueryUtil.enrichIssueWithJournalInfo(sourceMap, esClient);

                // 计算篇级数量
                QueryUtil.countIssueArticles(sourceMap, esClient);

                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ISSUE_PRODUCT);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);
        } catch (Exception e) {
            log.error("多源卷期列表查询异常", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取单源卷期列表(适用于深度翻页)
     */
    @GetMapping("/singleIssue/scroll")
    public TableDataInfo listSingleIssuesByScroll(
            @RequestParam(required = false) String singleIssueId,
            @RequestParam(required = false) String sourceTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String scrollId,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(singleIssueId)) {
                boolQuery.must(QueryBuilders.termQuery("singleIssueId.keyword", singleIssueId));
            }
            if (StringUtils.isNotBlank(sourceTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("sourceTitle", sourceTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }

            // 执行scroll查询
            SearchHits hits = esClient.scrollSearch(boolQuery, Constants.SINGLE_ISSUE_PRODUCT, scrollId, pageSize).getHits();
            String newScrollId = esClient.scrollSearch(boolQuery, Constants.SINGLE_ISSUE_PRODUCT, scrollId, pageSize).getScrollId();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 获取所属品种信息
                QueryUtil.enrichIssueWithJournalInfo(sourceMap, esClient);

                // 计算篇级数量
                QueryUtil.countIssueArticles(sourceMap, esClient);

                sourceMap.put("scrollId", newScrollId);  // 将scrollId添加到结果中
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ISSUE_PRODUCT);

            // 创建TableDataInfo对象并设置属性
            TableDataInfo rspData = QueryUtil.buildTableDataInfo(list, count);

            // 如果没有更多数据,清除scroll
            if (list.isEmpty() && StringUtils.isNotBlank(scrollId)) {
                esClient.clearScroll(scrollId);
            }

            return rspData;
        } catch (Exception e) {
            log.error("多源卷期列表scroll查询异常", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取单源卷期详情
     */
    @GetMapping("/singleIssue/{issueId}")
    public AjaxResult getSingleIssueInfo(@PathVariable("issueId") String issueId) {
        try {
            BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            boolQuery.must(QueryBuilders.termQuery("singleIssueId.keyword", issueId));

            log.info("从ES查询多源卷期详情，issueId: {}", issueId);

            // 执行查询
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ISSUE_PRODUCT, null, 1, 1).getHits();

            if (hits.getHits().length > 0) {
                Map<String, Object> sourceMap = hits.getHits()[0].getSourceAsMap();

                // 获取所属品种信息
                QueryUtil.enrichIssueWithJournalInfo(sourceMap, esClient);

                // 计算篇级数量
                QueryUtil.countIssueArticles(sourceMap, esClient);

                return success(sourceMap);
            } else {
                return AjaxResult.error("未找到指定的多源卷期数据");
            }
        } catch (Exception e) {
            log.error("获取多源卷期详情失败: {}", e.getMessage());
            return AjaxResult.error("获取多源卷期详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取单源卷期关联的篇级列表
     */
    @GetMapping("/singleIssue/articles/{issueId}")
    public TableDataInfo getSingleIssueArticles(
            @PathVariable("issueId") String issueId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String articleTitle
    ) {
        try {
            // 从ES中查询关联的篇级信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("singleIssueId.keyword", issueId));

            // 添加文章标题的筛选条件
            if (StringUtils.isNotBlank(articleTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("articleTitle", articleTitle));
            }

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            log.info("查询多源卷期关联篇级数据，查询条件: {}", boolQuery);

            // 执行分页查询
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> articles = QueryUtil.processSearchHits(hits, true);

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT);
            log.info("查询到篇级总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(articles, count);
        } catch (Exception e) {
            log.error("获取多源卷期关联篇级列表失败", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取单源篇级列表(适用于浅翻页)
     */
    @GetMapping("/singleArticle/list1")
    public TableDataInfo listSingleArticles1(
            @RequestParam(required = false) String singleArticleId,
            @RequestParam(required = false) String articleTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        List<Map<String, Object>> mockData = new ArrayList<>();
        Random random = new Random();
        
        // 生成20条模拟数据
        for (int i = 1; i <= 20; i++) {
            Map<String, Object> article = new HashMap<>();
            // 篇级ID
            article.put("singleArticleId", "SA" + String.format("%04d", i));
            // 篇级题名
            article.put("articleTitle", "模拟文章标题 " + i);
            // 期刊题名
            article.put("journalTitle", "测试期刊 " + (i % 5 + 1));
            // ISSN
            article.put("issn", "ISSN-" + String.format("%04d", random.nextInt(10000)));
            // 年/卷/期
            article.put("interval", String.format("%d年 第%d卷 第%d期", 
                2020 + random.nextInt(5),  // 年：2020-2024
                random.nextInt(10) + 1,    // 卷：1-10
                random.nextInt(12) + 1     // 期：1-12
            ));
            // 原始数据条数
            article.put("articleNum", random.nextInt(50) + 1);
            // 数据状态
            article.put("status", random.nextBoolean() ? "已处理" : "未处理");
            // 更新时间
            article.put("updateTime", LocalDateTime.now()
                .minusDays(random.nextInt(30))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            mockData.add(article);
        }

        // 手动实现分页
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, mockData.size());
        List<Map<String, Object>> pagedData = mockData.subList(start, end);

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
        rspData.setMsg("操作成功");
        rspData.setRows(pagedData);
        rspData.setTotal(mockData.size());
        
        return rspData;
    }

    @GetMapping("/singleArticle/list")
    public TableDataInfo listSingleArticles(
            @RequestParam(required = false) String singleArticleId,
            @RequestParam(required = false) String article_title,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(singleArticleId)) {
                boolQuery.must(QueryBuilders.termQuery("singleArticleId.keyword", singleArticleId));
            }
            if (StringUtils.isNotBlank(article_title)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("article_title", article_title));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }

            // 如果页码超过阈值,需要切换到scroll模式
            if (pageNum > 100) {
                return listMutchArticlesByScroll(singleArticleId, article_title, issn, state, null, pageSize);
            }

            // 执行普通查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("update_time", SortOrder.DESC);

            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 处理年卷期区间字段
                ParseRecordUtil.processArticleFields(sourceMap);

                // 获取所属品种信息
                QueryUtil.enrichArticleWithJournalInfo(sourceMap, esClient);

                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (Exception e) {
            log.error("多源篇级列表查询异常", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取单源篇级列表(适用于深度翻页)
     */
    @GetMapping("/singleArticle/scroll")
    public TableDataInfo listSingleArticlesByScroll(
            @RequestParam(required = false) String singleArticleId,
            @RequestParam(required = false) String article_title,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String scrollId,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(singleArticleId)) {
                boolQuery.must(QueryBuilders.termQuery("singleArticleId.keyword", singleArticleId));
            }
            if (StringUtils.isNotBlank(article_title)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("article_title", article_title));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }

            // 执行scroll查询
            SearchHits hits = esClient.scrollSearch(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, scrollId, pageSize).getHits();
            String newScrollId = esClient.scrollSearch(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, scrollId, pageSize).getScrollId();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 处理年卷期区间字段
                ParseRecordUtil.processArticleFields(sourceMap);

                // 获取所属品种信息
                QueryUtil.enrichArticleWithJournalInfo(sourceMap, esClient);

                sourceMap.put("scrollId", newScrollId);  // 将scrollId添加到结果中
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT);

            // 创建TableDataInfo对象并设置属性
            TableDataInfo rspData = QueryUtil.buildTableDataInfo(list, count);

            // 如果没有更多数据,清除scroll
            if (list.isEmpty() && StringUtils.isNotBlank(scrollId)) {
                esClient.clearScroll(scrollId);
            }

            return rspData;

        } catch (Exception e) {
            log.error("多源篇级列表scroll查询异常", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取单源篇级详情
     */
    @GetMapping("/singleArticle1/{articleId}")
    public AjaxResult getSingleArticleInfo1(@PathVariable("articleId") String articleId) {
        try {
            // 模拟数据
            Map<String, Object> mockData = new HashMap<>();
            mockData.put("sourceId", "SRC" + articleId.substring(2));
            mockData.put("sourceTitle", "测试期刊标题");
            mockData.put("issn", "ISSN-" + String.format("%04d", new Random().nextInt(10000)));
            mockData.put("publisherName", "测试出版社");
            mockData.put("pubYear", "2024");
            mockData.put("volume", String.valueOf(new Random().nextInt(20) + 1));
            mockData.put("issue", String.valueOf(new Random().nextInt(12) + 1));
            mockData.put("articleId", articleId);
            mockData.put("articleTitle", "测试文章标题 " + articleId);
            mockData.put("firstPage", String.valueOf(new Random().nextInt(50) + 1));
            mockData.put("lastPage", String.valueOf(new Random().nextInt(50) + 51));
            mockData.put("fullName", "作者A; 作者B; 作者C");
            mockData.put("abstract", "这是一段模拟的文章摘要内容，用于测试显示效果。这篇文章讨论了一些重要的研究发现和结论。");
            mockData.put("keyword", "关键词1; 关键词2; 关键词3; 关键词4");
            mockData.put("updateTime", LocalDateTime.now()
                .minusDays(new Random().nextInt(30))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            return AjaxResult.success(mockData);
        } catch (Exception e) {
            log.error("获取篇级详情异常", e);
            return AjaxResult.error("获取篇级详情失败：" + e.getMessage());
        }
    }

    @GetMapping("/singleArticle/{articleId}")
    public AjaxResult getSingleArticleInfo(@PathVariable("articleId") String articleId) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("singleArticleId.keyword", articleId));

            log.info("从ES查询单源篇级详情，articleId: {}", articleId);

            // 执行查询
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, null, 1, 1).getHits();

            if (hits.getHits().length > 0) {
                Map<String, Object> sourceMap = hits.getHits()[0].getSourceAsMap();

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 处理年卷期区间字段
                ParseRecordUtil.processArticleFields(sourceMap);

                // 获取所属品种信息
                QueryUtil.enrichArticleWithJournalInfo(sourceMap, esClient);

                // 构建完整的页码范围信息
                ParseRecordUtil.buildPageRangeInfo(sourceMap);

                return success(sourceMap);
            } else {
                return AjaxResult.error("未找到指定的单源篇级数据");
            }
        } catch (Exception e) {
            log.error("获取单源篇级详情失败: {}", e.getMessage());
            return AjaxResult.error("获取单源篇级详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取挂接篇级数据列表
     */
    @GetMapping("/singleArticle/related/{articleId}")
    public TableDataInfo listRelatedSingleArticles(
            @PathVariable("articleId") String articleId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("singleArticleId.keyword", articleId));
            log.info("从ES查询单源篇级详情，articleId: {}", articleId);
            // 执行查询
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, null, 1, 1).getHits();
            long count = 0;
            if (hits.getHits().length > 0) {
                Map<String, Object> sourceMap = hits.getHits()[0].getSourceAsMap();
                Object originalArticleId = sourceMap.get("originalArticleId");
                if(ObjectUtils.isNotEmpty(originalArticleId)) {
                    List<String> originalArticleIds = (List<String>) originalArticleId;
                    count = originalArticleIds.size();
                    // 构建ES查询，获取单源篇级信息
                    BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
                    boolQuery1.must(QueryBuilders.termsQuery("originalArticleId.keyword", originalArticleIds));

                    log.info("查询单源篇级ES数据，查询条件: {}", boolQuery);
                    SearchHits hits1 = esClient.search(boolQuery1, Constants.SINGLE_ARTICLE_PREPARE, null, pageNum, pageSize).getHits();

                    for (SearchHit hit : hits1.getHits()) {
                        Map<String, Object> sourceMap1 = hit.getSourceAsMap();
                        list.add(sourceMap1);
                    }
                }
            }
            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (Exception e) {
            log.error("获取篇级关联的单源篇级列表失败", e);
            return new TableDataInfo();
        }
    }

    @GetMapping("/singleArticle/related1/{articleId}")
    public TableDataInfo listRelatedSingleArticles1(
            @PathVariable("articleId") String articleId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 从MongoDB获取多源篇级的关联单源篇级ID列表
            List<String> singleArticleIds = QueryUtil.getRelatedIdsFromMongo(
                    mongoUtils,
                    MongoConstants.FUSION_ARTICLE_PRODUCT,
                    "fusionArticleId",
                    articleId,
                    "singleArticleIds"
            );

            if (singleArticleIds.isEmpty()) {
                log.warn("多源篇级{}没有关联的单源篇级ID", articleId);
                return new TableDataInfo();
            }

            // 构建ES查询，获取单源篇级信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("singleArticleId", singleArticleIds));

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            log.info("查询单源篇级ES数据，查询条件: {}", boolQuery);
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                Map<String, Object> related = new HashMap<>();

                // 设置基本字段
                related.put("fusionArticleId", articleId);
                related.put("singleArticleId", sourceMap.get("articleId"));
                related.put("batchId", sourceMap.get("batchId"));
                related.put("dataSource", sourceMap.get("dataSource"));
                related.put("articleTitle", sourceMap.get("articleTitle"));
                related.put("sourceTitle", sourceMap.get("sourceTitle"));
                related.put("issn", sourceMap.get("issn"));

                // 构建年/卷/期区间
                StringBuilder interval = new StringBuilder();
                Object year = sourceMap.get("year");
                Object volume = sourceMap.get("volume");
                Object issue = sourceMap.get("issue");

                if (year != null) {
                    interval.append(year);
                    if (volume != null) {
                        interval.append("/").append(volume);
                        if (issue != null) {
                            interval.append("/").append(issue);
                        }
                    }
                }

                if (interval.length() > 0) {
                    related.put("interval", interval.toString());
                }

                // 页码范围
                related.put("firstPage", sourceMap.get("firstPage"));
                related.put("lastPage", sourceMap.get("lastPage"));

                // 作者信息
                related.put("fullName", sourceMap.get("fullName"));

                // DOI信息
                related.put("doi", sourceMap.get("doi"));

                // 设置更新时间
                related.put("updateTime", sourceMap.get("updateTime"));

                // 格式化updateTime字段
                QueryUtil.formatUpdateTime(related);

                list.add(related);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT);
            log.info("查询到单源篇级总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (Exception e) {
            log.error("获取篇级关联的单源篇级列表失败", e);
            return new TableDataInfo();
        }
    }

    


    /**
     * 获取多源品种列表
     */
    @GetMapping("/mutchJournal/list")
    public TableDataInfo listMutchJournals(
            @RequestParam(required = false) String fusionJournalId,
            @RequestParam(required = false) String sourceTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(fusionJournalId)) {
                boolQuery.must(QueryBuilders.termQuery("fusionJournalId.keyword", fusionJournalId));
            }
            if (StringUtils.isNotBlank(sourceTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("sourceTitle", sourceTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }

            // 执行普通查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);
            
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_JOURNAL_PRODUCT, sort, pageNum, pageSize).getHits();
            
            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                
                // 处理年卷期区间字段
                QueryUtil.processIntervalField(sourceMap);
                
                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);
                
                // 计算关联的篇级数量
                QueryUtil.countArticles(sourceMap, esClient);
                
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_JOURNAL_PRODUCT);
            
            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (IOException e) {
            log.error("多源品种ES查询异常", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取多源品种列表(适用于深度翻页)
     */
    @GetMapping("/mutchJournal/scroll")
    public TableDataInfo scrollMutchJournals(
            @RequestParam(required = false) String fusionJournalId,
            @RequestParam(required = false) String sourceTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String scrollId,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(fusionJournalId)) {
                boolQuery.must(QueryBuilders.termQuery("fusionJournalId.keyword", fusionJournalId));
            }
            if (StringUtils.isNotBlank(sourceTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("sourceTitle", sourceTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }
            
            // 执行scroll查询
            SearchHits hits = esClient.scrollSearch(boolQuery, Constants.FUSION_JOURNAL_PRODUCT, scrollId, pageSize).getHits();
            String newScrollId = esClient.scrollSearch(boolQuery, Constants.FUSION_JOURNAL_PRODUCT, scrollId, pageSize).getScrollId();
            
            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                
                // 处理年卷期区间字段
                QueryUtil.processIntervalField(sourceMap);
                
                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);
                
                // 计算关联的篇级数量
                QueryUtil.countArticles(sourceMap, esClient);
                
                sourceMap.put("scrollId", newScrollId);  // 将scrollId添加到结果中
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_JOURNAL_PRODUCT);
            
            // 创建TableDataInfo对象并设置属性
            TableDataInfo rspData = QueryUtil.buildTableDataInfo(list, count);
            
            // 如果没有更多数据,清除scroll
            if (list.isEmpty() && StringUtils.isNotBlank(scrollId)) {
                esClient.clearScroll(scrollId);
            }
            
            return rspData;

        } catch (IOException e) {
            log.error("多源品种ES scroll查询异常", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取多源品种详情
     */
    @GetMapping("/mutchJournal/{journalId}")
    public AjaxResult getMutchJournalInfo(@PathVariable("journalId") String journalId) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("fusionArticleId.keyword", journalId));

            log.info("从ES查询多源品种详情，journalId: {}", journalId);

            // 执行查询
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_JOURNAL_PRODUCT, null, 1, 1).getHits();

            if (hits.getHits().length > 0) {
                Map<String, Object> sourceMap = hits.getHits()[0].getSourceAsMap();
                
                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);
                
                // 处理年卷期区间字段
                QueryUtil.processIntervalField(sourceMap);
                
                // 计算关联的篇级数量
                QueryUtil.countArticles(sourceMap, esClient);
                
                return success(sourceMap);
            }
        } catch (Exception e) {
            log.error("获取多源品种详情失败: {}", e.getMessage());
        }
        return AjaxResult.error("获取多源品种详情失败");
    }

    /**
     * 获取多源品种卷期列表
     */
    @GetMapping("/mutchJournal/volumes/{journalId}")
    public TableDataInfo getMutchJournalVolumes(
            @PathVariable("journalId") String journalId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String year,
            @RequestParam(required = false) String volume,
            @RequestParam(required = false) String issue
    ) {
        try {
            // 从ES中查询关联的卷期信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("fusionJournalId.keyword", journalId));

            // 添加年份、卷号、期号的筛选条件
            if (StringUtils.isNotBlank(year)) {
                boolQuery.must(QueryBuilders.termQuery("year", year));
            }
            if (StringUtils.isNotBlank(volume)) {
                boolQuery.must(QueryBuilders.termQuery("volume", volume));
            }
            if (StringUtils.isNotBlank(issue)) {
                boolQuery.must(QueryBuilders.termQuery("issue", issue));
            }
            
            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("year", SortOrder.DESC);
            sort.put("volume", SortOrder.DESC);
            sort.put("issue", SortOrder.DESC);
            
            log.info("查询多源品种卷期数据，查询条件: {}", boolQuery);
            
            // 执行分页查询
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_ISSUE_PRODUCT, null, pageNum, pageSize).getHits();
            
            // 处理查询结果
            List<Map<String, Object>> volumes = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                Map<String, Object> volumeInfo = new HashMap<>();
                volumeInfo.put("fusionIssueId", sourceMap.get("fusionIssueId"));
                volumeInfo.put("volume", sourceMap.get("volume"));
                volumeInfo.put("issue", sourceMap.get("issue"));
                volumeInfo.put("year", sourceMap.get("year"));
                
                // 如果有文章ID列表，添加文章数量
                Object articleIds = sourceMap.get("articleIds");
                if (articleIds instanceof List) {
                    volumeInfo.put("articleCount", ((List<?>) articleIds).size());
                } else {
                    volumeInfo.put("articleCount", 0);
                }
                
                // 添加更新时间
                volumeInfo.put("updateTime", sourceMap.get("updateTime"));
                formatUpdateTime(volumeInfo);
                
                volumes.add(volumeInfo);
            }
            
            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_ISSUE_PRODUCT);
            log.info("查询到卷期总数: {}", count);
            
            // 创建TableDataInfo对象并设置属性
            TableDataInfo rspData = new TableDataInfo();
            rspData.setCode(200);
            rspData.setRows(volumes);
            rspData.setTotal(count);
            return rspData;
        } catch (Exception e) {
            log.error("获取多源品种卷期列表失败", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    @GetMapping("/mutchJournal/related/{journalId}")
    public TableDataInfo listRelatedSingleJournals(
            @PathVariable("journalId") String journalId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 从MongoDB获取多源品种的关联单源品种ID列表
            List<String> singleJournalIds = QueryUtil.getRelatedIdsFromMongo(
                    mongoUtils,
                    MongoConstants.FUSION_JOURNAL_PRODUCT,
                    "fusionJournalId",
                    journalId,
                    "singleJournalIds"
            );

            if (singleJournalIds.isEmpty()) {
                log.warn("多源品种{}没有关联的单源品种ID", journalId);
                return new TableDataInfo();
            }

            // 构建ES查询，获取单源品种信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("singleJournalId", singleJournalIds));

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            log.info("查询单源品种ES数据，查询条件: {}", boolQuery);
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_JOURNAL_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                Map<String, Object> related = new HashMap<>();

                // 设置基本字段
                related.put("fusionJournalId", journalId);
                related.put("singleJournalId", sourceMap.get("singleJournalId"));
                related.put("batchId", sourceMap.get("batchId"));
                related.put("dataSource", sourceMap.get("dataSource"));
                related.put("sourceTitle", sourceMap.get("sourceTitle"));
                related.put("issn", sourceMap.get("issn"));

                // 构建年/卷/期区间
                StringBuilder interval = new StringBuilder();
                Object year = sourceMap.get("year");
                Object volume = sourceMap.get("volume");
                Object issue = sourceMap.get("issue");

                if (year != null) {
                    interval.append(year);
                    if (volume != null) {
                        interval.append("/").append(volume);
                        if (issue != null) {
                            interval.append("/").append(issue);
                        }
                    }
                }

                if (interval.length() > 0) {
                    related.put("interval", interval.toString());
                }

                // 页码范围
                related.put("firstPage", sourceMap.get("firstPage"));
                related.put("lastPage", sourceMap.get("lastPage"));

                // 作者信息
                related.put("fullName", sourceMap.get("fullName"));

                // DOI信息
                related.put("doi", sourceMap.get("doi"));

                // 设置更新时间
                related.put("updateTime", sourceMap.get("updateTime"));

                // 格式化updateTime字段
                QueryUtil.formatUpdateTime(related);

                list.add(related);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_JOURNAL_PRODUCT);
            log.info("查询到单源品种总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (Exception e) {
            log.error("获取多源品种关联的单源品种列表失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取多源品种篇级列表
     */
    @GetMapping("/mutchJournal/articles/{journalId}")
    public TableDataInfo getMutchJournalArticles(
            @PathVariable("journalId") String journalId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String year,
            @RequestParam(required = false) String volume,
            @RequestParam(required = false) String issue,
            @RequestParam(required = false) String articleTitle
    ) {
        try {
            // 从ES中查询关联的篇级信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("fusionJournalId.keyword", journalId));

            // 添加年份、卷号、期号、文章标题的筛选条件
            if (StringUtils.isNotBlank(year)) {
                boolQuery.must(QueryBuilders.termQuery("year", year));
            }
            if (StringUtils.isNotBlank(volume)) {
                boolQuery.must(QueryBuilders.termQuery("volume", volume));
            }
            if (StringUtils.isNotBlank(issue)) {
                boolQuery.must(QueryBuilders.termQuery("issue", issue));
            }
            if (StringUtils.isNotBlank(articleTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("articleTitle", articleTitle));
            }

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            log.info("查询多源品种篇级数据，查询条件: {}", boolQuery);

            // 执行分页查询
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_ARTICLE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> articles = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();

                // 构建interval字段（年/卷/期）
                StringBuilder interval = new StringBuilder();
                Object year_obj = sourceMap.get("year");
                Object volume_obj = sourceMap.get("volume");
                Object issue_obj = sourceMap.get("issue");

                if (year_obj != null) {
                    interval.append(year_obj);
                    if (volume_obj != null) {
                        interval.append("/").append(volume_obj);
                        if (issue_obj != null) {
                            interval.append("/").append(issue_obj);
                        }
                    }
                }

                if (interval.length() > 0) {
                    sourceMap.put("interval", interval.toString());
                }

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                articles.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_ARTICLE_PRODUCT);
            log.info("查询到篇级总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(articles, count);
        } catch (Exception e) {
            log.error("获取多源品种篇级列表失败", e);
            return getErrorDataTable(e.getMessage());
        }
    }


    /**
     * 获取多源卷期列表
     */
    @GetMapping("/mutchIssue/list")
    public TableDataInfo listMutchIssues(
            @RequestParam(required = false) String fusionIssueId,
            @RequestParam(required = false) String sourceTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(fusionIssueId)) {
                boolQuery.must(QueryBuilders.termQuery("fusionIssueId.keyword", fusionIssueId));
            }
            if (StringUtils.isNotBlank(sourceTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("sourceTitle", sourceTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }

            // 如果页码超过阈值,需要切换到scroll模式
            if (pageNum > 100) {
                return listMutchIssuesByScroll(fusionIssueId, sourceTitle, issn, state, null, pageSize);
            }

            // 执行普通查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);
            
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_ISSUE_PRODUCT, sort, pageNum, pageSize).getHits();
            
            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                
                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);
                
                // 获取所属品种信息
                QueryUtil.enrichIssueWithJournalInfo(sourceMap, esClient);
                
                // 计算篇级数量
                QueryUtil.countIssueArticles(sourceMap, esClient);
                
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_ISSUE_PRODUCT);
            
            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);
        } catch (Exception e) {
            log.error("多源卷期列表查询异常", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取多源卷期列表(适用于深度翻页)
     */
    @GetMapping("/mutchIssue/scroll")
    public TableDataInfo listMutchIssuesByScroll(
            @RequestParam(required = false) String fusionIssueId,
            @RequestParam(required = false) String sourceTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String scrollId,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(fusionIssueId)) {
                boolQuery.must(QueryBuilders.termQuery("fusionIssueId.keyword", fusionIssueId));
            }
            if (StringUtils.isNotBlank(sourceTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("sourceTitle", sourceTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }
            
            // 执行scroll查询
            SearchHits hits = esClient.scrollSearch(boolQuery, Constants.FUSION_ISSUE_PRODUCT, scrollId, pageSize).getHits();
            String newScrollId = esClient.scrollSearch(boolQuery, Constants.FUSION_ISSUE_PRODUCT, scrollId, pageSize).getScrollId();
            
            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                
                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);
                
                // 获取所属品种信息
                QueryUtil.enrichIssueWithJournalInfo(sourceMap, esClient);
                
                // 计算篇级数量
                QueryUtil.countIssueArticles(sourceMap, esClient);
                
                sourceMap.put("scrollId", newScrollId);  // 将scrollId添加到结果中
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_ISSUE_PRODUCT);
            
            // 创建TableDataInfo对象并设置属性
            TableDataInfo rspData = QueryUtil.buildTableDataInfo(list, count);
            
            // 如果没有更多数据,清除scroll
            if (list.isEmpty() && StringUtils.isNotBlank(scrollId)) {
                esClient.clearScroll(scrollId);
            }
            
            return rspData;
        } catch (Exception e) {
            log.error("多源卷期列表scroll查询异常", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取多源卷期详情
     */
    @GetMapping("/mutchIssue/{issueId}")
    public AjaxResult getMutchIssueInfo(@PathVariable("issueId") String issueId) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("fusionIssueId.keyword", issueId));

            log.info("从ES查询多源卷期详情，issueId: {}", issueId);

            // 执行查询
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_ISSUE_PRODUCT, null, 1, 1).getHits();

            if (hits.getHits().length > 0) {
                Map<String, Object> sourceMap = hits.getHits()[0].getSourceAsMap();

                // 获取所属品种信息
                QueryUtil.enrichIssueWithJournalInfo(sourceMap, esClient);

                // 计算篇级数量
                QueryUtil.countIssueArticles(sourceMap, esClient);

                return success(sourceMap);
            } else {
                return AjaxResult.error("未找到指定的多源卷期数据");
            }
        } catch (Exception e) {
            log.error("获取多源卷期详情失败: {}", e.getMessage());
            return AjaxResult.error("获取多源卷期详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取多源卷期关联的篇级列表
     */
    @GetMapping("/mutchIssue/articles/{issueId}")
    public TableDataInfo getMutchIssueArticles(
            @PathVariable("issueId") String issueId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String articleTitle
    ) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("fusionIssueId.keyword", issueId));

            // 添加文章标题的筛选条件
            if (StringUtils.isNotBlank(articleTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("articleTitle", articleTitle));
            }

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            log.info("查询多源卷期关联篇级数据，查询条件: {}", boolQuery);

            // 执行分页查询
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_ARTICLE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> articles = QueryUtil.processSearchHits(hits, true);

            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_ARTICLE_PRODUCT);
            log.info("查询到篇级总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(articles, count);
        } catch (Exception e) {
            log.error("获取多源卷期关联篇级列表失败", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取多源卷期关联的单源卷期列表
     */
    @GetMapping("/mutchIssue/related/{issueId}")
    public TableDataInfo listRelatedMutchIssues(
            @PathVariable("issueId") String issueId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 从MongoDB获取多源卷期的关联单源卷期ID列表
            List<String> singleIssueIds = QueryUtil.getRelatedIdsFromMongo(
                    mongoUtils,
                    MongoConstants.FUSION_ISSUE_PRODUCT,
                    "fusionIssueId",
                    issueId,
                    "singleIssueIds"
            );

            if (singleIssueIds.isEmpty()) {
                log.warn("多源卷期{}没有关联的单源卷期ID", issueId);
                return new TableDataInfo();
            }

            // 构建ES查询，获取单源卷期信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("singleIssueId", singleIssueIds));

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            log.info("查询单源卷期ES数据，查询条件: {}", boolQuery);
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ISSUE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                Map<String, Object> related = new HashMap<>();
                related.put("fusionIssueId", issueId);
                related.put("singleIssueId", sourceMap.get("singleIssueId"));
                related.put("dataSource", sourceMap.get("dataSource"));
                related.put("sourceTitle", sourceMap.get("sourceTitle"));
                related.put("issn", sourceMap.get("issn"));
                related.put("year", sourceMap.get("year"));
                related.put("volume", sourceMap.get("volume"));
                related.put("issue", sourceMap.get("issue"));
                related.put("updateTime", sourceMap.get("updateTime"));

                // 格式化updateTime字段
                QueryUtil.formatUpdateTime(related);

                list.add(related);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ISSUE_PRODUCT);
            log.info("查询到单源卷期总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (Exception e) {
            log.error("获取卷期关联的单源卷期列表失败", e);
            return new TableDataInfo();
        }
    }


    /**
     * 获取多源篇级列表(适用于浅翻页)
     */
    @GetMapping("/mutchArticle/list")
    public TableDataInfo listMutchArticles(
            @RequestParam(required = false) String fusionArticleId,
            @RequestParam(required = false) String articleTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(fusionArticleId)) {
                boolQuery.must(QueryBuilders.termQuery("fusionArticleId.keyword", fusionArticleId));
            }
            if (StringUtils.isNotBlank(articleTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("articleTitle", articleTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }


            // 如果页码超过阈值,需要切换到scroll模式
            if (pageNum > 100) {
                return listMutchArticlesByScroll(fusionArticleId, articleTitle, issn, state, null, pageSize);
            }

            // 执行普通查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);
            
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_ARTICLE_PRODUCT, sort, pageNum, pageSize).getHits();
            
            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                
                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);
                
                // 处理年卷期区间字段
                ParseRecordUtil.processArticleFields(sourceMap);
                
                // 获取所属品种信息
                QueryUtil.enrichArticleWithJournalInfo(sourceMap, esClient);
                
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_ARTICLE_PRODUCT);
            
            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (Exception e) {
            log.error("多源篇级列表查询异常", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取多源篇级列表(适用于深度翻页)
     */
    @GetMapping("/mutchArticle/scroll")
    public TableDataInfo listMutchArticlesByScroll(
            @RequestParam(required = false) String fusionArticleId,
            @RequestParam(required = false) String articleTitle,
            @RequestParam(required = false) String issn,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String scrollId,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (StringUtils.isNotBlank(fusionArticleId)) {
                boolQuery.must(QueryBuilders.termQuery("fusionArticleId.keyword", fusionArticleId));
            }
            if (StringUtils.isNotBlank(articleTitle)) {
                boolQuery.must(QueryBuilders.matchPhraseQuery("articleTitle", articleTitle));
            }
            if (StringUtils.isNotBlank(issn)) {
                boolQuery.must(QueryBuilders.termQuery("issn.keyword", issn));
            }
            if (StringUtils.isNotBlank(state)) {
                boolQuery.must(QueryBuilders.termQuery("status", state));
            }
            
            // 执行scroll查询
            SearchHits hits = esClient.scrollSearch(boolQuery, Constants.FUSION_ARTICLE_PRODUCT, scrollId, pageSize).getHits();
            String newScrollId = esClient.scrollSearch(boolQuery, Constants.FUSION_ARTICLE_PRODUCT, scrollId, pageSize).getScrollId();
            
            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                
                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);
                
                // 处理年卷期区间字段
                ParseRecordUtil.processArticleFields(sourceMap);
                
                // 获取所属品种信息
                QueryUtil.enrichArticleWithJournalInfo(sourceMap, esClient);
                
                sourceMap.put("scrollId", newScrollId);  // 将scrollId添加到结果中
                list.add(sourceMap);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.FUSION_ARTICLE_PRODUCT);
            
            // 创建TableDataInfo对象并设置属性
            TableDataInfo rspData = QueryUtil.buildTableDataInfo(list, count);
            
            // 如果没有更多数据,清除scroll
            if (list.isEmpty() && StringUtils.isNotBlank(scrollId)) {
                esClient.clearScroll(scrollId);
            }
            
            return rspData;

        } catch (Exception e) {
            log.error("多源篇级列表scroll查询异常", e);
            return getErrorDataTable(e.getMessage());
        }
    }

    /**
     * 获取多源篇级详情
     */
    @GetMapping("/mutchArticle/{articleId}")
    public AjaxResult getMutchArticleInfo(@PathVariable("articleId") String articleId) {
        try {
            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("fusionArticleId.keyword", articleId));

            log.info("从ES查询多源篇级详情，articleId: {}", articleId);

            // 执行查询
            SearchHits hits = esClient.search(boolQuery, Constants.FUSION_ARTICLE_PRODUCT, null, 1, 1).getHits();

            if (hits.getHits().length > 0) {
                Map<String, Object> sourceMap = hits.getHits()[0].getSourceAsMap();

                // 格式化更新时间
                QueryUtil.formatUpdateTime(sourceMap);

                // 处理年卷期区间字段
                ParseRecordUtil.processArticleFields(sourceMap);

                // 获取所属品种信息
                QueryUtil.enrichArticleWithJournalInfo(sourceMap, esClient);

                // 构建完整的页码范围信息
                ParseRecordUtil.buildPageRangeInfo(sourceMap);

                return success(sourceMap);
            } else {
                return AjaxResult.error("未找到指定的多源篇级数据");
            }
        } catch (Exception e) {
            log.error("获取多源篇级详情失败: {}", e.getMessage());
            return AjaxResult.error("获取多源篇级详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取挂接篇级数据列表
     */
    @GetMapping("/mutchArticle/related/{articleId}")
    public TableDataInfo listRelatedMutchArticles(
            @PathVariable("articleId") String articleId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        try {
            // 从MongoDB获取多源篇级的关联单源篇级ID列表
            List<String> singleArticleIds = QueryUtil.getRelatedIdsFromMongo(
                    mongoUtils,
                    MongoConstants.FUSION_ARTICLE_PRODUCT,
                    "fusionArticleId",
                    articleId,
                    "singleArticleIds"
            );

            if (singleArticleIds.isEmpty()) {
                log.warn("多源篇级{}没有关联的单源篇级ID", articleId);
                return new TableDataInfo();
            }

            // 构建ES查询，获取单源篇级信息
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("singleArticleId", singleArticleIds));

            // 执行查询
            Map<String, SortOrder> sort = new HashMap<>();
            sort.put("updateTime", SortOrder.DESC);

            log.info("查询单源篇级ES数据，查询条件: {}", boolQuery);
            SearchHits hits = esClient.search(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT, sort, pageNum, pageSize).getHits();

            // 处理查询结果
            List<Map<String, Object>> list = new ArrayList<>();
            for (SearchHit hit : hits.getHits()) {
                Map<String, Object> sourceMap = hit.getSourceAsMap();
                Map<String, Object> related = new HashMap<>();

                // 设置基本字段
                related.put("fusionArticleId", articleId);
                related.put("singleArticleId", sourceMap.get("articleId"));
                related.put("batchId", sourceMap.get("batchId"));
                related.put("dataSource", sourceMap.get("dataSource"));
                related.put("articleTitle", sourceMap.get("articleTitle"));
                related.put("sourceTitle", sourceMap.get("sourceTitle"));
                related.put("issn", sourceMap.get("issn"));

                // 构建年/卷/期区间
                StringBuilder interval = new StringBuilder();
                Object year = sourceMap.get("year");
                Object volume = sourceMap.get("volume");
                Object issue = sourceMap.get("issue");

                if (year != null) {
                    interval.append(year);
                    if (volume != null) {
                        interval.append("/").append(volume);
                        if (issue != null) {
                            interval.append("/").append(issue);
                        }
                    }
                }

                if (interval.length() > 0) {
                    related.put("interval", interval.toString());
                }

                // 页码范围
                related.put("firstPage", sourceMap.get("firstPage"));
                related.put("lastPage", sourceMap.get("lastPage"));

                // 作者信息
                related.put("fullName", sourceMap.get("fullName"));

                // DOI信息
                related.put("doi", sourceMap.get("doi"));

                // 设置更新时间
                related.put("updateTime", sourceMap.get("updateTime"));

                // 格式化updateTime字段
                QueryUtil.formatUpdateTime(related);

                list.add(related);
            }

            // 获取总数
            long count = esClient.count(boolQuery, Constants.SINGLE_ARTICLE_PRODUCT);
            log.info("查询到单源篇级总数: {}", count);

            // 创建TableDataInfo对象并设置属性
            return QueryUtil.buildTableDataInfo(list, count);

        } catch (Exception e) {
            log.error("获取篇级关联的单源篇级列表失败", e);
            return new TableDataInfo();
        }
    }


    /**
     * 创建错误数据表格响应
     */
    private TableDataInfo getErrorDataTable(String errorMsg) {
        return QueryUtil.getErrorDataTable(errorMsg);
    }

    /**
     * 获取多源品种关联的单源品种列表
     */


    /**
     * 格式化updateTime字段为yyyy-M-dd HH:mm:ss格式
     */
    private void formatUpdateTime(Map<String, Object> sourceMap) {
        QueryUtil.formatUpdateTime(sourceMap);
    }

    /**
     * 获取minio xml内容
     */
    @GetMapping("/minioXml")
    public AjaxResult getMinioXml(@RequestParam("xmlUrl") String xmlUrl) {
        try {
            // xmlUrl 形如 /bucketName/path/to/file.xml 或 path/to/file.xml
            String objectName = xmlUrl;
            if (objectName.startsWith("/")) {
                objectName = objectName.substring(1);
            }
            if (objectName.startsWith(bucketName + "/")) {
                objectName = objectName.substring(bucketName.length() + 1);
            }
            // 获取文件流
            try (InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            )) {
                String xml = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                return AjaxResult.success(xml);
            }
        } catch (Exception e) {
            log.error("获取minio xml内容失败", e);
            return AjaxResult.error("获取minio xml内容失败: " + e.getMessage());
        }
    }
}
