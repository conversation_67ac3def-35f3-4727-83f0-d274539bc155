# ParseImiDataUtil 使用指南

## 简介

`ParseImiDataUtil` 是一个用于解析MongoDB文档的工具类，特别适用于处理XML转换为JSON后的嵌套文档结构。该工具类提供了灵活的路径解析机制，支持属性过滤和数组节点处理，可以轻松地从复杂的文档结构中提取所需数据。

## 主要功能

1. **通用路径解析**：使用 `NodeDef` 定义路径，从MongoDB文档中提取数据
2. **属性过滤**：根据节点的属性条件筛选匹配的节点
3. **数组处理**：支持处理数组节点，可以获取第一个元素或收集所有匹配的元素
4. **_text节点处理**：自动处理文档中的 `_text` 子节点
5. **预定义解析方法**：提供常用的预定义解析方法，如获取文章标题、作者、摘要等

## 使用方法

### 1. 使用预定义解析方法

```java
// 获取文章标题
String title = ParseImiDataUtil.getArticleTitle(document);

// 获取文章作者列表（逗号分隔）
String authors = ParseImiDataUtil.getArticleAuthors(document);

// 获取文章摘要
String abstractText = ParseImiDataUtil.getArticleAbstract(document);

// 获取文章DOI
String doi = ParseImiDataUtil.getArticleDOI(document);

// 获取文章发布日期
String publishDate = ParseImiDataUtil.getArticlePublishDate(document);

// 获取文章关键词
String keywords = ParseImiDataUtil.getArticleKeywords(document);

// 获取特定类型的文章标题
String mainTitle = ParseImiDataUtil.getArticleTitleWithType(document, "main");
```

### 2. 使用通用路径解析方法

#### 2.1 定义路径

```java
// 创建路径定义
ParseImiDataUtil.NodeDef[] path = {
    new ParseImiDataUtil.NodeDef("record"),
    new ParseImiDataUtil.NodeDef("article-meta"),
    new ParseImiDataUtil.NodeDef("title-group"),
    new ParseImiDataUtil.NodeDef("article-title")
};

// 解析并获取结果
String result = ParseImiDataUtil.parseStringByPath(document, path);
```

#### 2.2 添加属性条件

```java
// 创建带属性条件的路径定义
ParseImiDataUtil.NodeDef[] path = {
    new ParseImiDataUtil.NodeDef("record"),
    new ParseImiDataUtil.NodeDef("article-meta"),
    new ParseImiDataUtil.NodeDef("article-id").addAttr("pub-id-type", "doi")
};

// 解析并获取结果
String result = ParseImiDataUtil.parseStringByPath(document, path);
```

#### 2.3 处理数组节点

```java
// 创建路径定义，收集所有匹配的数组元素
ParseImiDataUtil.NodeDef[] path = {
    new ParseImiDataUtil.NodeDef("record"),
    new ParseImiDataUtil.NodeDef("article-meta"),
    new ParseImiDataUtil.NodeDef("contrib-group"),
    new ParseImiDataUtil.NodeDef("contrib", true).addAttr("contrib-type", "author"),
    new ParseImiDataUtil.NodeDef("name"),
    new ParseImiDataUtil.NodeDef("full-name")
};

// 解析并获取结果（逗号分隔的字符串）
String result = ParseImiDataUtil.parseStringByPath(document, path);
```

## NodeDef 类说明

`NodeDef` 类用于定义路径中的节点，包含以下属性和方法：

- **name**: 节点名称
- **attrs**: 节点属性条件，key为属性名，value为属性值
- **collectAll**: 是否收集数组中的所有匹配元素

### 构造方法

```java
// 创建普通节点
new NodeDef("nodeName");

// 创建需要收集所有元素的节点
new NodeDef("nodeName", true);
```

### 添加属性条件

```java
// 添加单个属性条件
new NodeDef("nodeName").addAttr("attrName", "attrValue");

// 添加多个属性条件
new NodeDef("nodeName")
    .addAttr("attr1", "value1")
    .addAttr("attr2", "value2");
```

## 示例场景

### 场景1：获取文章基本信息

```java
// 获取文章基本信息
String title = ParseImiDataUtil.getArticleTitle(document);
String authors = ParseImiDataUtil.getArticleAuthors(document);
String doi = ParseImiDataUtil.getArticleDOI(document);
String publishDate = ParseImiDataUtil.getArticlePublishDate(document);

System.out.println("标题: " + title);
System.out.println("作者: " + authors);
System.out.println("DOI: " + doi);
System.out.println("发布日期: " + publishDate);
```

### 场景2：获取特定类型的作者

```java
// 定义路径，获取通讯作者
ParseImiDataUtil.NodeDef[] path = {
    new ParseImiDataUtil.NodeDef("record"),
    new ParseImiDataUtil.NodeDef("article-meta"),
    new ParseImiDataUtil.NodeDef("contrib-group"),
    new ParseImiDataUtil.NodeDef("contrib", true).addAttr("contrib-type", "corresponding"),
    new ParseImiDataUtil.NodeDef("name"),
    new ParseImiDataUtil.NodeDef("full-name")
};

// 解析并获取结果
String correspondingAuthors = ParseImiDataUtil.parseStringByPath(document, path);
System.out.println("通讯作者: " + correspondingAuthors);
```

### 场景3：获取参考文献列表

```java
// 定义路径，获取所有参考文献
ParseImiDataUtil.NodeDef[] path = {
    new ParseImiDataUtil.NodeDef("record"),
    new ParseImiDataUtil.NodeDef("back"),
    new ParseImiDataUtil.NodeDef("ref-list"),
    new ParseImiDataUtil.NodeDef("ref", true),
    new ParseImiDataUtil.NodeDef("mixed-citation")
};

// 解析并获取结果
String references = ParseImiDataUtil.parseStringByPath(document, path);
System.out.println("参考文献: " + references);
```

## 注意事项

1. 路径中的节点可能是数组或非数组，工具类会自动处理
2. 如果节点是数组且没有设置 `collectAll=true`，默认取第一个元素
3. 如果节点是数组且设置了 `collectAll=true`，会收集所有匹配的元素并用逗号分隔
4. 如果节点有 `_text` 子节点，会自动提取 `_text` 的值
5. 如果解析失败，`parseStringByPath` 方法会返回空字符串