package com.ruoyi.storage.processingtool.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UpdateRuleAnalysisStatusReqVO {

    @NotBlank(message = "规则ID不能为空")
    private String ruleId;

    private String ruleValidId;

    @NotEmpty(message = "ID不能为空")
    private List<String> ids;

    @NotBlank(message = "状态不能为空")
    private String status;

}
