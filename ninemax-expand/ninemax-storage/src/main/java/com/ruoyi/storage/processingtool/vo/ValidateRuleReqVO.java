package com.ruoyi.storage.processingtool.vo;

import com.ruoyi.storage.api.vo.ExFileVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ValidateRuleReqVO {

    /**
     * 规则ID
     */
    @NotBlank(message = "规则ID不能为空")
    private String ruleId;

    /**
     * 样例文件
     */
    @NotBlank(message = "效验文件不能为空")
    private String sampleFile;

    /**
     * 文件列表
     */
    @NotEmpty(message = "效验文件不能为空")
    private List<ExFileVO> fileList;

}
