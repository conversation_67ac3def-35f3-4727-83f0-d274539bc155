package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationValidation;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface IRuleNormalizationValidationService extends IBaseService<RuleNormalizationValidation> {

    /**
     * 验证规则
     *
     * @param validateRuleReqVO 请求参数
     */
    void validateRule(@Valid ValidateRuleReqVO validateRuleReqVO);
}
