package com.ruoyi.storage.processingtool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.storage.api.vo.ListSelect;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleAnalysis;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.domain.RuleCheck;
import com.ruoyi.storage.processingtool.mapper.RuleCheckMapper;
import com.ruoyi.storage.processingtool.service.IRuleCheckBaseService;
import com.ruoyi.storage.processingtool.service.IRuleCheckService;
import com.ruoyi.storage.processingtool.vo.QueryRuleCheckSelectReqVO;
import com.ruoyi.storage.processingtool.vo.UpdateRuleCheckStatusReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RuleCheckServiceImpl extends BaseServiceImpl<RuleCheckMapper, RuleCheck> implements IRuleCheckService {

    @Autowired
    @Lazy
    private IRuleCheckBaseService ruleCheckBaseService;

    @Override
    public List<RuleCheck> getByRuleId(String ruleId) {
        LambdaQueryWrapper<RuleCheck> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleCheck::getRuleId, ruleId);
        return list(queryWrapper);
    }

    @Override
    public List<RuleCheck> getByRuleValidId(String ruleValidId) {
        LambdaQueryWrapper<RuleCheck> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleCheck::getRuleValidId, ruleValidId);
        return list(queryWrapper);
    }

    @Override
    public void updateCheckStatus(String id, String checkStatus) {
        LambdaUpdateWrapper<RuleCheck> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RuleCheck::getId, id)
                .set(RuleCheck::getCheckStatus, checkStatus);
        update(updateWrapper);
    }

    @Override
    public void updateStatus(String id, String status) {
        LambdaUpdateWrapper<RuleCheck> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RuleCheck::getId, id)
                .set(RuleCheck::getStatus, status);
        update(updateWrapper);
    }

    /**
     * 处理规则检查状态
     *
     * @param updateRuleCheckStatusReqVO 请求参数
     */
    @Override
    public void handleCheckStatus(UpdateRuleCheckStatusReqVO updateRuleCheckStatusReqVO) {
        String ruleId = updateRuleCheckStatusReqVO.getRuleId();
        List<String> ids = updateRuleCheckStatusReqVO.getIds();
        String checkStatus = updateRuleCheckStatusReqVO.getCheckStatus();
        // 1、进行数据状态的更新
        RuleBase ruleBase = RuleBase.builder().id(ruleId).checkStatus(checkStatus).build();
        ruleCheckBaseService.updateById(ruleBase);
//        if (ids != null && !ids.isEmpty() && StringUtils.isNotBlank(checkStatus)) {
//            LambdaUpdateWrapper<RuleCheck> updateWrapper = new LambdaUpdateWrapper<>();
//            updateWrapper.in(RuleCheck::getId, ids);
//            updateWrapper.set(RuleCheck::getCheckStatus, checkStatus);
//            this.update(null, updateWrapper);
//        }
    }

    /**
     * 查询规则检查列表
     *
     * @param queryRuleCheckSelectReqVO 查询参数
     * @return 结果
     */
    @Override
    public List<ListSelect> querySelectList(QueryRuleCheckSelectReqVO queryRuleCheckSelectReqVO) {
        List<ListSelect> selectList = Collections.emptyList();
        if (queryRuleCheckSelectReqVO == null || StringUtils.isBlank(queryRuleCheckSelectReqVO.getRuleId())) {
            return selectList;
        }
        LambdaQueryWrapper<RuleCheck> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleCheck::getRuleId, queryRuleCheckSelectReqVO.getRuleId());
        if (StringUtils.isNotBlank(queryRuleCheckSelectReqVO.getStatus())) {
            queryWrapper.eq(RuleCheck::getStatus, queryRuleCheckSelectReqVO.getStatus());
        }
        List<RuleCheck> list = this.list(queryWrapper);
        if (list != null && !list.isEmpty()) {
            selectList = list.stream().map(ruleCheck -> ListSelect.builder()
                    .value(ruleCheck.getId())
                    .text(ruleCheck.getPath())
                    .status(ruleCheck.getStatus())
                    .build()).toList();
        }
        return selectList;
    }
}
