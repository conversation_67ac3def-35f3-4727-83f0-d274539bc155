package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationWordDetail;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationWordDetailMapper;
import com.ruoyi.storage.processingtool.service.IRuleNormalizationWordDetailService;
import com.ruoyi.storage.processingtool.service.impl.RuleNormalizationWordDetailServiceImpl;
import com.ruoyi.storage.processingtool.vo.AddRuleNormalizationWordDetailReqVO;
import com.ruoyi.storage.processingtool.vo.DeleteRuleNormalizationWordDetailReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleNormalizationWordDetail")
@Validated
public class RuleNormalizationWordDetailController extends BaseController<RuleNormalizationWordDetailMapper, RuleNormalizationWordDetail> {
    RuleNormalizationWordDetailController(RuleNormalizationWordDetailServiceImpl service) {
        super(service, RuleNormalizationWordDetail.class);
    }

    @Autowired
    private IRuleNormalizationWordDetailService ruleNormalizationWordDetailService;


    /**
     * 分页查询数据列表
     *
     * @param ruleNormalizationWordDetail 查询条件实体
     */
    @GetMapping("/list")
    public R<?> list(
            RuleNormalizationWordDetail ruleNormalizationWordDetail) {
        return R.ok(service.queryList(ruleNormalizationWordDetail));
    }

    /**
     * 新增数据
     *
     * @param addRuleNormalizationWordDetailReqVO 实体对象
     * @return 操作结果
     */
    @PostMapping("/addRuleNormalizationWordDetail")
    public R<?> addRuleNormalizationWordDetail(@Valid @RequestBody AddRuleNormalizationWordDetailReqVO addRuleNormalizationWordDetailReqVO) {
         return R.ok(ruleNormalizationWordDetailService.addRuleNormalizationWordDetail(addRuleNormalizationWordDetailReqVO));
    }

    /**
     * 修改数据
     *
     * @param ruleNormalizationWordDetail 实体对象
     * @return 操作结果
     */
    @PostMapping("/updateRuleNormalizationWordDetail")
    public R<?> updateRuleNormalizationWordDetail(@Valid @RequestBody RuleNormalizationWordDetail ruleNormalizationWordDetail) {
        service.updateById(ruleNormalizationWordDetail);
        return R.ok();
    }

    /**
     * 删除数据
     *
     * @param deleteRuleNormalizationWordDetailReqVO 删除条件实体
     * @return 操作结果
     */
    @PostMapping("/deleteRuleNormalizationWordDetail")
    public R<?> del(@Valid @RequestBody DeleteRuleNormalizationWordDetailReqVO deleteRuleNormalizationWordDetailReqVO) {
        return R.ok(ruleNormalizationWordDetailService.deleteRuleNormalizationWordDetail(deleteRuleNormalizationWordDetailReqVO));
    }

}
