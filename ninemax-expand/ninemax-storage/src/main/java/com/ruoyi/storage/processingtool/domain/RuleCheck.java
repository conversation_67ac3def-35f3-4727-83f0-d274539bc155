package com.ruoyi.storage.processingtool.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 智能校验规则表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "rule_check")
public class RuleCheck extends BaseEntity3 {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 规则ID
     */
    @Query(type = QueryType.EQ)
    private String ruleId;

    /**
     * 规则验证ID
     */
    @Query(type = QueryType.EQ)
    private String ruleValidId;

    /**
     * 处理状态
     */
    @Query(type = QueryType.EQ)
    private String checkStatus;

    /**
     * 失败原因
     */
    private String description;

    /**
     * 路径
     */
    @Query(type = QueryType.LIKE)
    private String path;

    /**
     * 元素集
     */
    @Query(type = QueryType.EQ)
    private String elementSet;

    /**
     * 元素名称
     */
    @Query(type = QueryType.LIKE)
    private String elementName;

    /**
     * 属性值
     */
    private String attributes;

    /**
     * 校验项数量
     */
    private Integer itemNum;

    /**
     * 应用状态
     */
    @Query(type = QueryType.EQ)
    private String status;

    /**
     * 排序号
     */
    private Integer sort;

}
