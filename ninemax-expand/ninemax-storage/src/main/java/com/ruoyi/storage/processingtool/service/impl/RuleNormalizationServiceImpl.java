package com.ruoyi.storage.processingtool.service.impl;

import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleNormalization;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationMapper;
import com.ruoyi.storage.processingtool.service.IRuleNormalizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleNormalizationServiceImpl extends BaseServiceImpl<RuleNormalizationMapper, RuleNormalization> implements IRuleNormalizationService {
}
