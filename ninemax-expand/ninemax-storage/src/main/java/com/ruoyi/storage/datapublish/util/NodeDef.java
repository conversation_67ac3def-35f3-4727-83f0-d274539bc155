package com.ruoyi.storage.datapublish.util;

public class NodeDef {
    // 节点名称
    private String name;
    // 节点属性，key为属性名，value为属性值
    private java.util.Map<String, String> attrs;
    // 是否需要获取所有数组元素
    private boolean collectAll;

    public NodeDef(String name) {
        this.name = name;
        this.attrs = new java.util.HashMap<>();
        this.collectAll = false;
    }

    public NodeDef(String name, boolean collectAll) {
        this.name = name;
        this.attrs = new java.util.HashMap<>();
        this.collectAll = collectAll;
    }

    public NodeDef addAttr(String key, String value) {
        this.attrs.put(key, value);
        return this;
    }

    public String getName() {
        return name;
    }

    public java.util.Map<String, String> getAttrs() {
        return attrs;
    }

    public boolean isCollectAll() {
        return collectAll;
    }
}
