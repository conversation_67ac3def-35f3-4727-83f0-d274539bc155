package com.ruoyi.storage.datapublish.util;

import org.bson.Document;

public class ParseImiToolUtil {
    
    /**
     * 检查Document的属性是否匹配指定的属性条件
     * 
     * @param doc Document对象
     * @param attrs 属性条件
     * @return 是否匹配
     */
    public static boolean matchAttributes(Document doc, java.util.Map<String, String> attrs) {
        if (attrs == null || attrs.isEmpty()) {
            return true;
        }
        
        for (java.util.Map.Entry<String, String> entry : attrs.entrySet()) {
            String attrName = entry.getKey();
            String attrValue = entry.getValue();
            
            // 检查Document是否包含该属性，并且值匹配
            Object docAttrValue = doc.get(attrName);
            if (docAttrValue == null) {
                return false;
            }
            
            String docAttrStr = docAttrValue.toString();
            if (!docAttrStr.equals(attrValue)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 通用的路径解析方法，根据指定的路径定义从MongoDB文档中提取数据
     * 
     * @param document MongoDB文档
     * @param path 路径定义数组
     * @return 解析结果，可能是String、Document、List或null
     */
    public static Object parseByPath(Document document, NodeDef[] path) {
        if (document == null || path == null || path.length == 0) {
            return null;
        }
        
        try {
            return parseByPathRecursive(document, path, 0);
        } catch (Exception e) {
            // 异常情况下返回null
            return null;
        }
    }
    
    /**
     * 递归解析路径，支持中间节点是数组的情况
     * 
     * @param currentObj 当前对象
     * @param path 路径定义数组
     * @param pathIndex 当前路径索引
     * @return 解析结果
     */
    private static Object parseByPathRecursive(Object currentObj, NodeDef[] path, int pathIndex) {
        // 如果已经处理完所有路径节点，返回当前对象
        if (pathIndex >= path.length) {
            return currentObj;
        }
        
        // 如果当前对象为空，返回null
        if (currentObj == null) {
            return null;
        }
        
        NodeDef nodeDef = path[pathIndex];
        boolean isLastNode = (pathIndex == path.length - 1);
        
        // 如果当前对象是Document
        if (currentObj instanceof Document) {
            Document currentDoc = (Document) currentObj;
            Object nextObj = currentDoc.get(nodeDef.getName());
            
            // 如果下一个节点为空，返回null
            if (nextObj == null) {
                return null;
            }
            
            // 处理节点可能是数组的情况
            if (nextObj instanceof java.util.List) {
                java.util.List<?> nodeList = (java.util.List<?>) nextObj;
                if (nodeList.isEmpty()) {
                    return null;
                }
                
                // 如果是最后一个节点且需要收集所有元素，或者中间节点需要收集所有元素
                if (nodeDef.isCollectAll()) {
                    // 创建结果列表
                    java.util.List<Object> resultList = new java.util.ArrayList<>();
                    
                    // 遍历数组中的每个元素
                    for (Object item : nodeList) {
                        // 如果有属性条件，检查是否匹配
                        if (item instanceof Document) {
                            Document itemDoc = (Document) item;
                            if (!nodeDef.getAttrs().isEmpty() && !matchAttributes(itemDoc, nodeDef.getAttrs())) {
                                continue; // 不匹配属性条件，跳过
                            }
                            
                            // 如果是最后一个节点
                            if (isLastNode) {
                                // 检查是否有_text子节点
                                Object textObj = itemDoc.get("_text_");
                                if (textObj != null) {
                                    if (textObj instanceof String) {
                                        resultList.add(textObj);
                                    } else if (textObj instanceof java.util.List) {
                                        java.util.List<?> textList = (java.util.List<?>) textObj;
                                        if (!textList.isEmpty()) {
                                            resultList.add(textList.get(0));
                                        }
                                    }
                                } else {
                                    resultList.add(itemDoc);
                                }
                            } else {
                                // 如果不是最后一个节点，递归处理下一级路径
                                Object subResult = parseByPathRecursive(item, path, pathIndex + 1);
                                if (subResult != null) {
                                    if (subResult instanceof java.util.List) {
                                        // 如果子结果是列表，将其所有元素添加到结果列表
                                        resultList.addAll((java.util.List<?>) subResult);
                                    } else {
                                        resultList.add(subResult);
                                    }
                                }
                            }
                        } else if (isLastNode) {
                            // 如果不是Document但是最后一个节点，直接添加
                            resultList.add(item);
                        }
                    }
                    
                    return resultList.isEmpty() ? null : resultList;
                } else {
                    // 不需要收集所有元素，只处理符合条件的第一个元素
                    Object selectedItem = null;
                    
                    // 如果有属性条件，筛选符合条件的节点
                    if (!nodeDef.getAttrs().isEmpty()) {
                        for (Object item : nodeList) {
                            if (item instanceof Document) {
                                Document itemDoc = (Document) item;
                                if (matchAttributes(itemDoc, nodeDef.getAttrs())) {
                                    selectedItem = item;
                                    break;
                                }
                            }
                        }
                    } else {
                        // 没有属性条件，取第一个元素
                        selectedItem = nodeList.get(0);
                    }
                    
                    if (selectedItem == null) {
                        return null;
                    }
                    
                    // 如果是最后一个节点
                    if (isLastNode && selectedItem instanceof Document) {
                        Document finalDoc = (Document) selectedItem;
                        
                        // 检查是否有_text子节点
                        Object textObj = finalDoc.get("_text_");
                        
                        // 如果有_text子节点，优先返回_text内容
                        if (textObj != null) {
                            // _text可能是字符串或数组
                            if (textObj instanceof String) {
                                return textObj;
                            } else if (textObj instanceof java.util.List) {
                                java.util.List<?> textList = (java.util.List<?>) textObj;
                                if (!textList.isEmpty()) {
                                    return textList.get(0);
                                }
                            }
                        }
                    }
                    
                    // 递归处理下一级路径
                    return parseByPathRecursive(selectedItem, path, pathIndex + 1);
                }
            } else {
                // 如果不是数组但有属性条件，检查属性是否匹配
                if (nextObj instanceof Document && !nodeDef.getAttrs().isEmpty()) {
                    Document nextDoc = (Document) nextObj;
                    if (!matchAttributes(nextDoc, nodeDef.getAttrs())) {
                        return null;
                    }
                }
                
                // 如果是最后一个节点且是Document
                if (isLastNode && nextObj instanceof Document) {
                    Document finalDoc = (Document) nextObj;
                    
                    // 检查是否有_text子节点
                    Object textObj = finalDoc.get("_text_");
                    
                    // 如果有_text子节点，优先返回_text内容
                    if (textObj != null) {
                        // _text可能是字符串或数组
                        if (textObj instanceof String) {
                            return textObj;
                        } else if (textObj instanceof java.util.List) {
                            java.util.List<?> textList = (java.util.List<?>) textObj;
                            if (!textList.isEmpty()) {
                                return textList.get(0);
                            }
                        }
                    }
                }
                
                // 递归处理下一级路径
                return parseByPathRecursive(nextObj, path, pathIndex + 1);
            }
        } else if (currentObj instanceof java.util.List) {
            // 如果当前对象是列表，创建结果列表
            java.util.List<?> currentList = (java.util.List<?>) currentObj;
            java.util.List<Object> resultList = new java.util.ArrayList<>();
            
            // 遍历列表中的每个元素
            for (Object item : currentList) {
                // 递归处理每个元素
                Object subResult = parseByPathRecursive(item, path, pathIndex);
                if (subResult != null) {
                    if (subResult instanceof java.util.List) {
                        // 如果子结果是列表，将其所有元素添加到结果列表
                        resultList.addAll((java.util.List<?>) subResult);
                    } else {
                        resultList.add(subResult);
                    }
                }
            }
            
            return resultList.isEmpty() ? null : resultList;
        }
        
        // 如果当前对象既不是Document也不是List，无法继续解析
        return isLastNode ? currentObj : null;
    }

    /**
     * 通用的路径解析方法，返回字符串结果
     *
     * @param document MongoDB文档
     * @param path 路径定义数组
     * @return 解析结果字符串，如果解析失败则返回空字符串
     */
    public static String parseStringByPath(Document document, NodeDef[] path) {
        Object result = parseByPath(document, path);

        if (result == null) {
            return "";
        }

        if (result instanceof String) {
            return (String) result;
        }

        if (result instanceof java.util.List) {
            java.util.List<?> resultList = (java.util.List<?>) result;
            if (resultList.isEmpty()) {
                return "";
            }

            // 将列表元素连接成字符串，用逗号分隔
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < resultList.size(); i++) {
                Object item = resultList.get(i);
                if (item != null) {
                    if (i > 0) {
                        sb.append(", ");
                    }
                    sb.append(item.toString());
                }
            }
            return sb.toString();
        }

        return result.toString();
    }

}
