package com.ruoyi.storage.equity.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.equity.domain.RuleEquityMetadataDetail;

import java.util.List;

/**
 * 权益规则元数据详情Service接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IRuleEquityMetadataDetailService extends IBaseService<RuleEquityMetadataDetail> {

    /**
     * 查询字段值
     *
     * @param metadataId 元数据
     * @return
     */
    List<RuleEquityMetadataDetail> getFieldValues(String metadataId);
}
