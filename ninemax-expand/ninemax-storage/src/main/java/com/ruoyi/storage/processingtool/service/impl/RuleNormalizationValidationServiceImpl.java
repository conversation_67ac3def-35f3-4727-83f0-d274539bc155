package com.ruoyi.storage.processingtool.service.impl;

import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.domain.RuleCheckValidation;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationValidation;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationValidationMapper;
import com.ruoyi.storage.processingtool.service.IRuleBaseService;
import com.ruoyi.storage.processingtool.service.IRuleNormalizationValidationService;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleNormalizationValidationServiceImpl extends BaseServiceImpl<RuleNormalizationValidationMapper, RuleNormalizationValidation> implements IRuleNormalizationValidationService {

    @Resource
    private IRuleBaseService ruleBaseService;

    /**
     * 验证规则
     *
     * @param validateRuleReqVO 请求参数
     */
    @Override
    public void validateRule(ValidateRuleReqVO validateRuleReqVO) {
        // 保存数据
        String ruleId = validateRuleReqVO.getRuleId();
        String sampleFile = validateRuleReqVO.getSampleFile();
        RuleBase ruleBase = ruleBaseService.getById(ruleId);
        String fileName = sampleFile.substring(sampleFile.lastIndexOf("/") + 1);
        RuleNormalizationValidation ruleNormalizationValidation = RuleNormalizationValidation.builder()
                .ruleId(ruleId)
                .name(ruleBase.getName())
                .fileName(fileName)
                .filePath(sampleFile)
                .type(1)
                .build();
        this.save(ruleNormalizationValidation);

        // todo 后期提取到统一的接口调用部分
        // 3、根据规则文件进行工具接口调用 ==> 异步调用
        log.info("著录规范化规则数据：{} | 规则文件校验数据:{}", ruleBase, ruleNormalizationValidation);

        // 使用线程池异步执行
        // todo 后续调用智能校验工具的处理接口
//        executorService.execute(() -> toolInterfaceHandleService.toolInterfaceCall(HandleRuleAnalysisToolDTO.builder().ruleBase(ruleBase).ruleAnalysisValidation(ruleAnalysisValidation).initFlag(false).build()));
    }
}
