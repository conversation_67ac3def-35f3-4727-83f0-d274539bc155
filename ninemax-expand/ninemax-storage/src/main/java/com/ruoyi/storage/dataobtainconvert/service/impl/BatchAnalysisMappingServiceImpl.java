package com.ruoyi.storage.dataobtainconvert.service.impl;

import cn.hutool.core.util.StrUtil;
import com.anwen.mongo.annotation.collection.CollectionName;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.api.RemoteExFileService;
import com.ruoyi.storage.api.domain.ParsingMappingResp;
import com.ruoyi.storage.api.vo.ExFile;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.dataobtainconvert.domain.BatchAnalysisMapping;
import com.ruoyi.storage.dataobtainconvert.mapper.BatchAnalysisMappingMapper;
import com.ruoyi.storage.dataobtainconvert.service.IBatchAnalysisMappingService;
import com.ruoyi.storage.dataobtainconvert.vo.ReprocessReqVO;
import com.ruoyi.storage.dataobtainconvert.vo.UpdateAuditStatusReqVO;
import com.ruoyi.storage.kafkacommon.domain.KafkaRsyncVO;
//import com.ruoyi.storage.kafkacommon.service.KafkaProducer;
import com.ruoyi.storage.mongocommon.domain.BasicMO;
import com.ruoyi.storage.mongocommon.domain.SingleArticlePrepareMO;
import com.ruoyi.storage.mongocommon.service.IBasicMOService;
import com.ruoyi.storage.mongocommon.service.ISingleArticlePrepareMOService;
import com.ruoyi.storage.utils.FileDownloadUtil;
import com.ruoyi.storage.utils.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchAnalysisMappingServiceImpl extends BaseServiceImpl<BatchAnalysisMappingMapper, BatchAnalysisMapping> implements IBatchAnalysisMappingService {

    @Autowired
    private IBasicMOService basicMOService;

    @Autowired
    private ISingleArticlePrepareMOService singleArticlePrepareMOService;

    @Resource
    private RemoteExFileService remoteExFileService;

//    @Resource
//    private KafkaProducer kafkaProducer;

    @Value("${storage.processingTool.tempDir}")
    private String tempDir;


    /**
     * 重新处理
     *
     * @param reprocessReqVO 批次数据信息对象
     * @return
     */
    @Override
    public boolean reprocess(ReprocessReqVO reprocessReqVO) {
        // todo 处理批次文件相关文件数据信息
        // 说明：当触发该接口时，如果之前已解析映射过数据，需将之前的数据进行清除
        // 重新调用工具方 执行数据 解析、映射方法操作，获取对应的结果数据信息
        Assert.notNull(reprocessReqVO.getBatchId(), "批次ID不能为空");

        // 1、获取对应的批次文件数据
        BatchAnalysisMapping batchAnalysisMapping = this.getById(reprocessReqVO.getId());
        Assert.notNull(batchAnalysisMapping, "批次数据不存在");

        // 当前批次解析映射信息重置
        batchAnalysisMappingDataReset(batchAnalysisMapping);

        // 2、调用工具模块获取解析后的数据信息 ==> 原始篇级数据、IMI映射后数据信息

        // 3、将数据存储到mongo数据库中


        // 4、关联相关数据信息 ==> PDF全文，解析映射后的XML片段


        System.out.println(reprocessReqVO);
        return true;
    }

    /**
     * 更新审核状态
     *
     * @param updateAuditStatusReqVO 批次数据信息对象
     */
    @Override
    public void updateAuditStatus(UpdateAuditStatusReqVO updateAuditStatusReqVO) {
        // 判断当前处理行为
        Assert.isFalse(StringUtils.isEmpty(updateAuditStatusReqVO.getId()), "批次ID不能为空！");
        Assert.isFalse(StringUtils.isEmpty(updateAuditStatusReqVO.getAuditStatus()), "审核状态不能为空！");
        String auditStatus = updateAuditStatusReqVO.getAuditStatus();
        BatchAnalysisMapping batchAnalysisMapping = new BatchAnalysisMapping();
        batchAnalysisMapping.setId(updateAuditStatusReqVO.getId());
        if (StringUtils.endsWithIgnoreCase("通过", auditStatus) || StringUtils.endsWithIgnoreCase("删除", auditStatus)) {
            batchAnalysisMapping.setAuditStatus(auditStatus);
            this.updateById(batchAnalysisMapping);
            // todo 关联其他业务处理操作后续完善

        } else if (StringUtils.endsWithIgnoreCase("拒绝", auditStatus)) {
            batchAnalysisMapping.setAuditStatus(auditStatus);
            batchAnalysisMapping.setRejectedReason(updateAuditStatusReqVO.getRejectedReason());
            batchAnalysisMapping.setReprocessFlag(updateAuditStatusReqVO.getReprocessFlag());
            this.updateById(batchAnalysisMapping);
            //  todo 关联其他业务处理操作后续完善

        } else {
            throw new IllegalArgumentException("审核状态异常！");
        }
    }

    /**
     * 处理批次数据信息
     *
     * @param batchAnalysisMapping      批次数据信息
     * @param resp 工具方返回的数据信息
     */
    @Override
    public void handleBatchAnalysisMappingResultData(BatchAnalysisMapping batchAnalysisMapping, ParsingMappingResp resp) throws IOException {
        // todo 处理批次文件相关文件数据信息

        Map<String, String> resultState = resp.getResult_state();
        String downloadPath = resp.getDownload_path();

        // 获取结果数据文件
//        String saveToDir = "D:\\home\\library2020\\batchAnalysisMappingResult\\";
//        String tempFilePath = System.getProperty("java.io.tmpdir") + StrUtil.uuid() + File.separator + fileName;
        String saveToDir = tempDir + File.separator +  "storage" + File.separator + StrUtil.uuid();
        FileDownloadUtil.createDir(saveToDir);
        String savePath = saveToDir + File.separator + batchAnalysisMapping.getBatchId() + "_result.zip";
        FileDownloadUtil.downloadFileOptimized(downloadPath, savePath);
        System.out.println("文件下载完成，保存路径: " + Paths.get(savePath).toAbsolutePath());

        // 2. 解压ZIP文件
//        String extractToDir = saveToDir + batchAnalysisMapping.getBatchId();
//        FileDownloadUtil.extractZipFile(savePath, extractToDir);

        // 3. 获取压缩包中的数据
        Map<String, Map<String, ZipEntry>> zipStructure = FileDownloadUtil.getZipStructure(savePath);

        // 4. 处理数据信息
        List<String> resultList = resultState.values().stream().toList();
        Map<String, List<String>> listMap = resultState.values().stream().collect(Collectors.groupingBy(String::trim));

        // 处理原始数据和IMI数据
        handleBasicData(batchAnalysisMapping, zipStructure, resultState, savePath);

        // 更新解析任务记录信息
        batchAnalysisMapping.setOriginalNum(resultList.size());
        batchAnalysisMapping.setAnalysisTotalNum(resultList.size());
        batchAnalysisMapping.setAnalysisSuccessNum(listMap.get("1") == null ? 0 : listMap.get("1").size());
        batchAnalysisMapping.setAnalysisFailNum(listMap.get("2") == null ? 0 : listMap.get("2").size());
        batchAnalysisMapping.setTaskStatus("结果已返回");
        batchAnalysisMapping.setTaskEndTime(new Date());
        this.updateById(batchAnalysisMapping);
    }

    /**
     * 处理原始数据和IMI数据 basicMOService、singleArticlePrepareMOService
     *
     * @param batchAnalysisMapping 批次数据信息
     * @param zipStructure         压缩包结构信息
     * @param resultState          结果状态信息
     */
    private void handleBasicData(BatchAnalysisMapping batchAnalysisMapping, Map<String, Map<String, ZipEntry>> zipStructure, Map<String, String> resultState, String zipFilePath) {
        String batchId = batchAnalysisMapping.getBatchId();
        String sourceId = batchAnalysisMapping.getSourceId();
        String dataType = batchAnalysisMapping.getDataType();
        File zipFile = new File(zipFilePath);
        // 转换sourceId为DataSource枚举
        IdGenerator.DataSource dataSource = Arrays.stream(IdGenerator.DataSource.values())
                .filter(ds -> ds.getCode().equals(sourceId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的数据源ID: " + sourceId));

        // 转换dataType为DocType枚举
        IdGenerator.DocType docType = Arrays.stream(IdGenerator.DocType.values())
                .filter(dt -> dt.getCode().equalsIgnoreCase(dataType))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的数据类型: " + dataType));
        List<BasicMO> basicMOList = new ArrayList<>();
        List<SingleArticlePrepareMO> singleArticlePrepareMOList = new ArrayList<>();

        // 数据开始处理
        resultState.forEach((key, value) -> {
            Map<String, ZipEntry> stringZipEntryMap = zipStructure.get(key);
            Map<String, Object> recordMap = new HashMap<>(3);
            if (stringZipEntryMap != null) {
                recordMap = FileDownloadUtil.processSpecialCaseEntries(zipFile, stringZipEntryMap);
            }

            // 获取原始库篇级ID
            String originalArticleId = IdGenerator.generateId(IdGenerator.DataFlowStatus.RAW, docType, IdGenerator.Level.ISSUE, dataSource);

            String projectPath = "parseMapping/result/" + batchId + "/" + originalArticleId;

            // 获取结果附件
            String oriXmlUrl = "";
            String imiXmlUrl = "";
            try {
                File oriXml = (File) recordMap.get("oriXml");
                File imiXml = (File) recordMap.get("imiXml");
                R<ExFile> oriXmlResult = remoteExFileService.uploadFile(FileDownloadUtil.convert(oriXml), projectPath, true);
                R<ExFile> imiXmlResult = remoteExFileService.uploadFile(FileDownloadUtil.convert(imiXml), projectPath, true);
                if (R.isSuccess(oriXmlResult)) {
                    ExFile oriXmlExFile = oriXmlResult.getData();
                    oriXmlUrl = oriXmlExFile.getUrl();
                }
                if (R.isSuccess(imiXmlResult)) {
                    ExFile imiXmlExFile = imiXmlResult.getData();
                    imiXmlUrl = imiXmlExFile.getUrl();
                }
            } catch (IOException e) {
                log.error("文件上传失败：{}", e.getMessage());
            }

            BasicMO basicMO = BasicMO.builder()
                    .batchId(batchId)
                    .sourceId(sourceId)
                    .originalArticleId(originalArticleId)
                    .dataType(dataType)
                    .xmlUri(oriXmlUrl)
                    .annexUri("")
                    .originalvalid(false)
                    .status(value)
                    .record(recordMap.get("oriJson") == null ? new HashMap<>() : recordMap.get("oriJson"))
                    .createTime(new Date())
                    .build();

            Map<String, Object> imiJson = (Map<String, Object>) recordMap.get("imiJson");
            SingleArticlePrepareMO singleArticlePrepareMO = SingleArticlePrepareMO.builder()
                    .batchId(batchId)
                    .sourceId(sourceId)
                    .originalArticleId(originalArticleId)
                    .dataType(dataType)
                    .status(1)
                    .hookSingleId("")
                    .createTime(new Date())
                    .record(imiJson == null ? new HashMap<>() : imiJson.get("record"))
                    .xmlUri(imiXmlUrl)
                    .build();
            basicMOList.add(basicMO);
//            singleArticlePrepareMOList.add(singleArticlePrepareMO);
            singleArticlePrepareMOService.saveData(singleArticlePrepareMO);
        });

        if (!basicMOList.isEmpty()) {
            Boolean saveBasicFlag = basicMOService.saveBatch(basicMOList);
            // 同步ES
            if (saveBasicFlag) {
                List<String> basicIds = basicMOList.stream().map(BasicMO::getId).toList();
                handleKafkaRsync(BasicMO.class, basicIds, sourceId, dataType, "");
            }
        }
//        if (!singleArticlePrepareMOList.isEmpty()) {
//            Boolean saveSingleArticlePrepareFlag = singleArticlePrepareMOService.saveBatch(singleArticlePrepareMOList);
//            // 同步ES
//            if (saveSingleArticlePrepareFlag) {
//                List<String> singleArticlePrepareIds = singleArticlePrepareMOList.stream().map(SingleArticlePrepareMO::getId).toList();
//                handleKafkaRsync(SingleArticlePrepareMO.class, singleArticlePrepareIds, sourceId, dataType, "");
//            }
//        }
    }

    /**
     * 同步ES
     *
     * @param MOClass            基本数据信息
     * @param originalArticleIds IDS
     * @param sourceId           数据源ID
     * @param dataType           数据类型
     * @param articleType        文章类型
     */
    private void handleKafkaRsync(Class<?> MOClass, List<String> originalArticleIds, String sourceId, String dataType, String articleType) {

        CollectionName collectionNameAnnotation = MOClass.getAnnotation(CollectionName.class);
        if (collectionNameAnnotation != null) {
            log.info("Collection name: {}", collectionNameAnnotation.value());
            String collectionName = collectionNameAnnotation.value();
            for (String originalArticleId : originalArticleIds) {
                KafkaRsyncVO kafkaRsyncVO = new KafkaRsyncVO();
                kafkaRsyncVO.setCollectionName(collectionName);
                kafkaRsyncVO.setId(originalArticleId);
                kafkaRsyncVO.setSourceId(sourceId);
                kafkaRsyncVO.setDataType(dataType);
//                kafkaProducer.sendRsync(kafkaRsyncVO);
            }
        } else {
            log.error("The annotation is not present.");
            throw new IllegalArgumentException("The annotation is not present.");
        }
    }

    /**
     * 重新处理批次数据信息重置
     * 说明：当触发该接口时，如果之前已解析映射过数据，需将之前的数据进行清除
     * 重新调用工具方 执行数据 解析、映射方法操作，获取对应的结果数据信息
     *
     * @param batchAnalysisMapping 批次数据信息
     */
    private void batchAnalysisMappingDataReset(BatchAnalysisMapping batchAnalysisMapping) {
        LambdaUpdateWrapper<BatchAnalysisMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BatchAnalysisMapping::getId, batchAnalysisMapping.getId())
                .set(BatchAnalysisMapping::getTaskStatus, "处理中")
                .set(BatchAnalysisMapping::getOriginalNum, null)
                .set(BatchAnalysisMapping::getAnalysisSuccessNum, null)
                .set(BatchAnalysisMapping::getAnalysisFailNum, null)
                .set(BatchAnalysisMapping::getAnalysisTotalNum, null)
                .set(BatchAnalysisMapping::getTaskStartTime, new Date())
                .set(BatchAnalysisMapping::getTaskEndTime, null)
                .set(BatchAnalysisMapping::getAuditStatus, "待审核");
        this.update(null, updateWrapper);
    }
}
