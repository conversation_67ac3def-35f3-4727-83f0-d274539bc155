package com.ruoyi.storage.searchbase.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.searchbase.domain.MappingBasicField;

import java.util.List;

public interface IMappingBasicFieldService extends IBaseService<MappingBasicField> {
    /**
     * 获取映射基本字段列表信息
     *
     * @param sourceId 数据来源ID
     * @param dataType 数据类型
     * @return
     */
    List<MappingBasicField> getMappingBasicFieldList(String sourceId, String dataType, String type);
}
