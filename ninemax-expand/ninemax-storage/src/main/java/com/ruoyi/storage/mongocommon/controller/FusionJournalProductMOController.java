package com.ruoyi.storage.mongocommon.controller;

import com.anwen.mongo.model.PageParam;
import com.anwen.mongo.model.PageResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.mongocommon.domain.FusionJournalProductMO;
import com.ruoyi.storage.mongocommon.param.QueryBasicMOReqVO;
import com.ruoyi.storage.mongocommon.service.IFusionJournalProductMOService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fusionJournalProduct")
public class FusionJournalProductMOController {

    @Resource
    private IFusionJournalProductMOService fusionJournalProductMOService;

    @GetMapping("/list")
    public R<?> list(
            FusionJournalProductMO fusionJournalProductMO,
            @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        PageResult<FusionJournalProductMO> page = fusionJournalProductMOService.page(pageNum, pageSize);
        return R.ok(page);
    }
}
