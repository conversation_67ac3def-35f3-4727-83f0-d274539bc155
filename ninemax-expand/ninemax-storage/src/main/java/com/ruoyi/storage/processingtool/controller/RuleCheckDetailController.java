package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleCheckDetail;
import com.ruoyi.storage.processingtool.mapper.RuleCheckDetailMapper;
import com.ruoyi.storage.processingtool.service.impl.RuleCheckDetailServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleCheckDetail")
@Validated
public class RuleCheckDetailController extends BaseController<RuleCheckDetailMapper, RuleCheckDetail> {
    RuleCheckDetailController(RuleCheckDetailServiceImpl service) {
        super(service, RuleCheckDetail.class);
    }
}
