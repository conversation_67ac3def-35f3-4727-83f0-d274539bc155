package com.ruoyi.storage.processingtool.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 解析映射规则表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "rule_analysis")
public class RuleAnalysis extends BaseEntity3 {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 规则ID
     */
    @Query(type = QueryType.EQ)
    private String ruleId;

    /**
     * 规则效验ID
     */
    @Query(type = QueryType.EQ)
    private String ruleValidId;

    /**
     * 路径ID
     */
    @Query(type = QueryType.EQ)
    private String pathId;

    /**
     * 原始路径
     */
    private String srcPath;

    /**
     * 原始路径
     */
    private String imiPath;

    /**
     * 映射路径ID
     */
    @Query(type = QueryType.EQ)
    private String imiId;

    /**
     * IMI标签
     */
    @Query(type = QueryType.LIKE)
    private String imiLabel;

    /**
     * IMI标签类型(属性)
     */
    private String imiType;

    /**
     * 解析频率
     */
    private Integer frequency;

    /**
     * 标签样例
     */
    private String sampleValue;

    /**
     * 样例文件存储路径
     */
    private String samplePath;

    /**
     * 应用状态
     */
    @Query(type = QueryType.EQ)
    private String status;

    /**
     * 处理状态
     */
    @Query(type = QueryType.EQ)
    private String analysisStatus;

    /**
     * 关键ID
     */
    @Query(type = QueryType.EQ)
    private String iskey;

    /**
     * 排序码
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;
}

