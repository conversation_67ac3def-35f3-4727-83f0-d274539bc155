package com.ruoyi.storage.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResult {

    @JsonProperty("validation_failed")
    private List<Object> validationFailed;

    @JsonProperty("validation_success")
    private List<Object> validationSuccess;

    @JsonProperty("validation_passed")
    private List<Object> validationPassed;
}