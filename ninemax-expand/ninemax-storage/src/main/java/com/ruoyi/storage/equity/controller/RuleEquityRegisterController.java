package com.ruoyi.storage.equity.controller;

import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.equity.domain.RuleEquityRegister;
import com.ruoyi.storage.equity.mapper.RuleEquityRegisterMapper;
import com.ruoyi.storage.equity.service.impl.RuleEquityRegisterServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权益工具登记详情表Controller
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/ruleEquityRegister")
@Validated
public class RuleEquityRegisterController extends BaseController<RuleEquityRegisterMapper, RuleEquityRegister> {
    
    public RuleEquityRegisterController(RuleEquityRegisterServiceImpl service) {
        super(service, RuleEquityRegister.class);
    }
}
