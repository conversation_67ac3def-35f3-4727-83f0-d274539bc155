package com.ruoyi.storage.mongocommon.controller;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.anwen.mongo.mapper.BaseMapper;
import com.anwen.mongo.model.PageParam;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.mongocommon.param.QueryBasicMOReqVO;
import com.ruoyi.storage.mongocommon.service.IBasicMOService;
import com.ruoyi.storage.mongocommon.vo.ArticleDetailReqVO;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/basic")
public class BasicMOController {

    @Autowired
    private IBasicMOService basicService;

    @Autowired
    private BaseMapper baseMapper;

    private static final Snowflake snowflake = IdUtil.getSnowflake(1, 1);

    @GetMapping("/list")
    public R<?> list(
            QueryBasicMOReqVO queryBasicReqVO,
            @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return R.ok(basicService.queryBasicList(queryBasicReqVO, new PageParam(pageNum, pageSize)));
    }

    /**
     * 查询当前数据的详情信息 ==> 原始数据信息、IMI映射数据信息、XML原始内容
     *
     * @param articleDetailReqVO 请求参数对象
     * @return
     */
    @PostMapping("/articleDetail")
    public R<?> articleDetail(@Valid @RequestBody ArticleDetailReqVO articleDetailReqVO) {
        return R.ok(basicService.articleDetail(articleDetailReqVO));
    }

    /**
     * 测试导入原始数据库数据信息 ==> test
     */
    @PostMapping("/saveBaseData")
    @Transactional
    public R<?> saveBaseData() {
        try {
            // 读取JSON文件
            String jsonPath = "D:/work/projects/项目代码/仓储系统V1.0/ninemax-expand/ninemax-storage/src/main/java/com/ruoyi/storage/mongocommon/util/批次数据详情.json";
            String jsonStr = new String(Files.readAllBytes(Paths.get(jsonPath)), StandardCharsets.UTF_8);

            // 解析JSON
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            JSONArray records = jsonObject.getJSONArray("RECORDS");

            // 转换为List<Map>
            List<Map<String, Object>> result = new ArrayList<>();
            for (int i = 0; i < records.size(); i++) {
                JSONObject record = records.getJSONObject(i);
                // 手动将JSONObject转换为Map
                Map<String, Object> map = new HashMap<>();
                // 遍历所有字段
                for (String key : record.keySet()) {
                    Object value = record.get(key);

                    // 处理JSON字符串类型的字段
                    if (value instanceof String && ((String) value).startsWith("[") && ((String) value).endsWith("]")) {
                        try {
                            // 尝试解析为JSONArray
                            value = JSON.parseArray((String) value);
                        } catch (Exception e) {
                            // 解析失败保持原样
                        }
                    } else if (value instanceof String && ((String) value).startsWith("{") && ((String) value).endsWith("}")) {
                        try {
                            // 尝试解析为JSONObject
                            value = JSON.parseObject((String) value);
                        } catch (Exception e) {
                            // 解析失败保持原样
                        }
                    }

                    map.put(key, value);
                }
                // 手动添加数据信息
                // 生成随机数据
                int randomNum = (int) (Math.random() * 10000);
                map.put("article_id", String.format("10.%04d/000%04d", randomNum, i));
                map.put("article_title", "Article Title " + randomNum);
                map.put("journal_title", "Journal " + (i % 10 + 1));
                map.put("issn_e", String.format("%04d-%04d", randomNum % 10000, (randomNum + 1) % 10000));
                map.put("issn_p", String.format("%04d-%04d", (randomNum + 2) % 10000, (randomNum + 3) % 10000));
                map.put("year", String.valueOf(2020 + i % 5));
                result.add(map);
            }

            // 插入数据库中
            Boolean basic = baseMapper.saveBatch("basic", result);
            return R.ok(basic);
        } catch (Exception e) {
            return R.fail("获取批次数据详情失败: " + e.getMessage());
        }
    }

    /**
     * 测试插入原始数据的xml片段数据信息 ==> test
     */
    @PostMapping("/handleBasicDataXml")
    @Transactional
    public R<?> handleBasicDataXml() {
        try {
            // 读取XML文件
            String xmlPath = "D:/work/projects/项目代码/仓储系统V1.0/ninemax-expand/ninemax-storage/src/main/java/com/ruoyi/storage/mongocommon/util/article_xml.xml";
//            String xmlContent = FileUtils.readFileToString(new File(xmlPath), StandardCharsets.UTF_8);
            SAXReader reader = new SAXReader();
            Document document = reader.read(new File(xmlPath));

            // 获取第一个PubmedArticle节点的完整XML内容
            Element firstArticle = document.getRootElement().element("PubmedArticle");
            String xmlContent = firstArticle.asXML();

            // 准备存储到basic_xml的数据

            Map<String, Object> xmlData = new HashMap<>();
            // 关联basic中的id
            xmlData.put("id", snowflake.nextId());
            xmlData.put("basic_id", "12313");
            // 存储XML内容
            xmlData.put("xml_content", xmlContent);
            // 可以添加其他关联字段
            xmlData.put("article_id", "12313");
            xmlData.put("article_title", "12313");

            // 批量插入到basic_xml集合
            baseMapper.save("basic_xml", xmlData);
            return R.ok(xmlData);
        } catch (Exception e) {
            return R.fail("导入XML数据失败: " + e.getMessage());
        }
    }

}
