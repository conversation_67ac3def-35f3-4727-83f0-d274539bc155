package com.ruoyi.storage.searchbase.service;

import com.ruoyi.storage.api.vo.ListSelect;
import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.searchbase.domain.DataOrigin;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IDataOriginService extends IBaseService<DataOrigin> {

    /**
     *  根据名称查询数据源
     * @param sourceName 数据源名称
     * @return
     */
    DataOrigin getDataOriginByName(String sourceName);

    /**
     *  查询数据源下拉列表
     * @return
     */
    List<ListSelect> querySelectList();

}
