package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleNormalization;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationMapper;
import com.ruoyi.storage.processingtool.service.impl.RuleNormalizationServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleNormalization")
@Validated
public class RuleNormalizationController extends BaseController<RuleNormalizationMapper, RuleNormalization> {
    RuleNormalizationController(RuleNormalizationServiceImpl service) {
        super(service, RuleNormalization.class);
    }
}
