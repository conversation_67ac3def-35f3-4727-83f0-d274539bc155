package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.api.vo.ListSelect;
import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.vo.AddRuleBaseReqVO;
import com.ruoyi.storage.processingtool.vo.QueryRuleBaseSelectReqVO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IRuleBaseService extends IBaseService<RuleBase> {

    void addRuleBase(@Valid AddRuleBaseReqVO addRuleBaseReqVO);

    List<ListSelect> querySelectList(QueryRuleBaseSelectReqVO queryRuleBaseSelectReqVO);

    void resendRuleBase(String ruleId);

    void updateRuleBase(@Valid RuleBase ruleBase);
}
