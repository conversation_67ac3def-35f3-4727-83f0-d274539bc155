package com.ruoyi.storage.searchbase.controller;

import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.searchbase.domain.MappingEsField;
import com.ruoyi.storage.searchbase.mapper.MappingEsFieldMapper;
import com.ruoyi.storage.searchbase.service.impl.MappingEsFieldServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mappingEsField")
@Validated
public class MappingEsFieldController extends BaseController<MappingEsFieldMapper, MappingEsField> {
    MappingEsFieldController(MappingEsFieldServiceImpl service) {
        super(service, MappingEsField.class);
    }
}
