package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.vo.AddRuleBaseReqVO;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface IRuleNormalizationBaseService extends IBaseService<RuleBase> {

    /**
     * 新增数据
     *
     * @param addRuleBaseReqVO 添加数据请求参数
     * @return 操作结果
     */
    void addRuleNormalizationBase(@Valid AddRuleBaseReqVO addRuleBaseReqVO);

    void updateRuleNormalizationBase(@Valid RuleBase ruleBase);
}
