## Mongo-Plus介绍

Mongo-Plus（简称 MP）是一个 MongoDB 的操作工具，可和现有mongoDB框架结合使用

## 文档地址 【https://mongoplus.com】

## 特性
- 无侵入：只做增强不做改变，引入它不会对现有工程产生影响，如丝般顺滑
- 损耗小：启动即会自动注入基本 CURD，性能基本无损耗，直接面向对象操作
- 强大的 CRUD 操作：通用 Service，仅仅通过少量配置即可实现单表大部分 CRUD 操作，更有强大的条件构造器，满足各类使用需求
- 支持 Lambda 形式调用：通过 Lambda 表达式，方便的编写各类查询条件，无需再担心字段写错
- 支持主键自动生成：支持多达 5 种主键策略（内含分布式唯一 ID 生成器 - Sequence），可自由配置，完美解决主键问题
- 支持自定义全局通用操作：支持全局通用方法注入
- 支持无实体类情况下的操作
- 支持动态数据源
- 支持逻辑删除、防止全集合更新和删除、自动填充等等功能

## 使用方式
### 引入依赖 （当前使用的版本为 *******）
    ```xml
       <dependency>
        <groupId>com.gitee.anwena</groupId>
        <artifactId>mongo-plus-boot-starter</artifactId>
        <version>最新版本</version>
    </dependency>
    ```
### 配置数据源
~~~
mongo-plus:
  data:
    mongodb:
      host: mongo.server   #ip
      port: 27017   #端口
      database: storage    #数据库名
      username: admin    #用户名，没有可不填（若账号中出现@,!等等符号，不需要再进行转码！！！）
      password: ninemax    #密码，同上（若密码中出现@,!等等符号，不需要再进行转码！！！）
      authenticationDatabase: admin     #验证数据库
      connectTimeoutMS: 50000   #在超时之前等待连接打开的最长时间（以毫秒为单位）
~~~

1. 编写实体类 （见代码）
2. 编写 Service 接口 （见代码）
3. 编写 Service 实现类 （见代码）
4. 编写 Controller （见代码）
5. 编写测试类 （见代码）
