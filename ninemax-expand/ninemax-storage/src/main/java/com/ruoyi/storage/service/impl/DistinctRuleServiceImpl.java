package com.ruoyi.storage.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.storage.domain.DistinctRule;
import com.ruoyi.storage.service.DistinctRuleService;
import com.ruoyi.storage.mapper.DistinctRuleMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【distinct_rule(归一规则表)】的数据库操作Service实现
* @createDate 2025-06-11 15:10:57
*/
@Service
public class DistinctRuleServiceImpl extends ServiceImpl<DistinctRuleMapper, DistinctRule>
    implements DistinctRuleService{

}




