package com.ruoyi.storage.processingtool.service.impl;

import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleCheckDetail;
import com.ruoyi.storage.processingtool.mapper.RuleCheckDetailMapper;
import com.ruoyi.storage.processingtool.service.IRuleCheckDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleCheckDetailServiceImpl extends BaseServiceImpl<RuleCheckDetailMapper, RuleCheckDetail> implements IRuleCheckDetailService {
}
