package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationValidation;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationValidationMapper;
import com.ruoyi.storage.processingtool.service.IRuleNormalizationValidationService;
import com.ruoyi.storage.processingtool.service.impl.RuleNormalizationValidationServiceImpl;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleNormalizationValidation")
@Validated
public class RuleNormalizationValidationController extends BaseController<RuleNormalizationValidationMapper, RuleNormalizationValidation> {
    RuleNormalizationValidationController(RuleNormalizationValidationServiceImpl service) {
        super(service, RuleNormalizationValidation.class);
    }

    @Autowired
    private IRuleNormalizationValidationService ruleNormalizationValidationService;

    /**
     * 校验规则文件效验
     *
     * @param validateRuleReqVO 实体对象
     * @return 操作结果
     */
    @PostMapping("/validateRule")
    public R<?> updateUseStatus(@Valid @RequestBody ValidateRuleReqVO validateRuleReqVO) {
        ruleNormalizationValidationService.validateRule(validateRuleReqVO);
        return R.ok();
    }
}
