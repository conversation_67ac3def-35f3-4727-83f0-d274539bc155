package com.ruoyi.storage.dataobtainconvert.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.dataobtainconvert.domain.BatchAnalysisMapping;
import com.ruoyi.storage.dataobtainconvert.mapper.BatchAnalysisMappingMapper;
import com.ruoyi.storage.dataobtainconvert.service.IBatchAnalysisMappingService;
import com.ruoyi.storage.dataobtainconvert.service.impl.BatchAnalysisMappingServiceImpl;
import com.ruoyi.storage.dataobtainconvert.vo.ReprocessReqVO;
import com.ruoyi.storage.dataobtainconvert.vo.UpdateAuditStatusReqVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/batchAnalysisMapping")
@Validated
public class BatchAnalysisMappingController extends BaseController<BatchAnalysisMappingMapper, BatchAnalysisMapping> {
    BatchAnalysisMappingController(BatchAnalysisMappingServiceImpl service) {
        super(service, BatchAnalysisMapping.class);
    }

    @Resource
    private IBatchAnalysisMappingService batchAnalysisMappingService;

    /**
     * 重新处理
     *
     * @param reprocessReqVO 批次数据信息对象
     * @return
     */
    @PostMapping("/reprocess")
    public R<?> reprocess(@Valid @RequestBody ReprocessReqVO reprocessReqVO) {
        return R.ok(batchAnalysisMappingService.reprocess(reprocessReqVO));
    }

    /**
     * 更新批次数据信息状态 ==> 通过、拒绝、删除
     *
     * @param updateAuditStatusReqVO 批次数据信息对象
     * @return
     */
    @PostMapping("/updateAuditStatus")
    public R<?> updateAuditStatus(@Valid @RequestBody UpdateAuditStatusReqVO updateAuditStatusReqVO) {
        batchAnalysisMappingService.updateAuditStatus(updateAuditStatusReqVO);
        return R.ok();
    }

}
