package com.ruoyi.storage.processingtool.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import com.ruoyi.storage.processingtool.vo.AddRuleBaseReqVO;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 规则总表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "rule_base")
public class RuleBase extends BaseEntity3 {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 规则名称
     */
    @Query(type = QueryType.LIKE)
    private String name;

    /**
     * 数据来源ID
     */
    @Query(type = QueryType.EQ)
    private String sourceId;

    /**
     * 文献类型
     */
    @Query(type = QueryType.EQ)
    private String docType;

    /**
     * 规则类型
     */
    @Query(type = QueryType.EQ)
    private String ruleType;

    /**
     * 应用状态
     */
    @Query(type = QueryType.EQ)
    private String useStatus;

    /**
     * 解析规则处理状态
     */
    @Query(type = QueryType.EQ)
    private String ruleStatus;

    /**
     * 工具状态
     */
    @Query(type = QueryType.EQ)
    private String toolStatus;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 启用环节 原始、单源、多源
     */
    private String phase;

    /**
     * 处理状态 （校验工具 待处理、通过、拒绝）
     */
    @Query(type = QueryType.EQ)
    private String checkStatus;

    // ========================== 检索信息 ==========================

    @TableField(exist = false)
    private String ruleValidId;

    /**
     * 时间检索条件信息
     */
    @TableField(exist = false)
    private String searchCreatTime;

    public static RuleBase convertToRuleBase(AddRuleBaseReqVO addRuleBaseReqVO) {
        return RuleBase.builder()
                .name(addRuleBaseReqVO.getName())
                .sourceId(addRuleBaseReqVO.getSourceId())
                .docType(addRuleBaseReqVO.getDocType())
                .ruleType(addRuleBaseReqVO.getRuleType())
                .useStatus(addRuleBaseReqVO.getUseStatus())
                .ruleStatus(addRuleBaseReqVO.getRuleStatus())
                .toolStatus(addRuleBaseReqVO.getToolStatus())
                .description(addRuleBaseReqVO.getDescription())
                .phase(addRuleBaseReqVO.getPhase())
                .build();
    }
}
