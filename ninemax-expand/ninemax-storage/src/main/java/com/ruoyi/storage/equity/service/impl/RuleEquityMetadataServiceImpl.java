package com.ruoyi.storage.equity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.equity.domain.RuleEquityMetadata;
import com.ruoyi.storage.equity.domain.RuleEquityMetadataDetail;
import com.ruoyi.storage.equity.mapper.RuleEquityMetadataMapper;
import com.ruoyi.storage.equity.service.IRuleEquityMetadataDetailService;
import com.ruoyi.storage.equity.service.IRuleEquityMetadataService;
import com.ruoyi.storage.equity.vo.UpdateEquityMetadataStatusReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 权益规则元数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@Slf4j
public class RuleEquityMetadataServiceImpl extends BaseServiceImpl<RuleEquityMetadataMapper, RuleEquityMetadata> implements IRuleEquityMetadataService {

    @Autowired
    private IRuleEquityMetadataDetailService ruleEquityMetadataDetailService;

    /**
     * 修改权益规则元数据状态
     *
     * @param updateEquityMetadataStatusReqVO 修改权益规则元数据状态请求对象
     */
    @Override
    public void updateEquityMetadataStatus(UpdateEquityMetadataStatusReqVO updateEquityMetadataStatusReqVO) {
        String id = updateEquityMetadataStatusReqVO.getId();
        // todo 后续看下是否有需要效验的逻辑 比如状态是否正确等
        Assert.isFalse(StringUtils.isBlank(id), "元数据信息修改失败 ==> id不能为空");
        RuleEquityMetadata updateRuleEquityMetadata = RuleEquityMetadata.builder().id(id)
                .status(updateEquityMetadataStatusReqVO.getStatus())
                .requiredFlag(updateEquityMetadataStatusReqVO.getRequiredFlag())
                .build();
        this.updateById(updateRuleEquityMetadata);
    }

    /**
     * 删除权益规则元数据
     *
     * @param ids 需要删除的权益规则元数据ID
     */
    @Override
    public void deleteEquityMetadata(List<String> ids) {
        Assert.isFalse(ids.isEmpty(), "元数据信息删除失败 ==> ids不能为空");

        log.info("开始删除权益规则元数据，待删除ID列表: {}", ids);

        // 先查看当前数据是否存在关联数据信息
        LambdaQueryWrapper<RuleEquityMetadataDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RuleEquityMetadataDetail::getEquityMetadataId, ids);
        List<RuleEquityMetadataDetail> list = ruleEquityMetadataDetailService.list(queryWrapper);

        if (!list.isEmpty()) {
            List<String> equityMetadataIds = list.stream().map(RuleEquityMetadataDetail::getEquityMetadataId).distinct().toList();
            List<RuleEquityMetadata> ruleEquityMetadataList = this.listByIds(equityMetadataIds);
            String cannotDeleteRuleEquityMetadataFieldNameStr = ruleEquityMetadataList.stream().map(RuleEquityMetadata::getFieldName).collect(Collectors.joining(","));
            Assert.isFalse(!list.isEmpty() || StringUtils.isNotBlank(cannotDeleteRuleEquityMetadataFieldNameStr), "元数据信息删除失败 ==> 字段名称为 " + cannotDeleteRuleEquityMetadataFieldNameStr + " 的数据存在关联的详情数据，请先删除关联数据");
        }

        // 执行删除操作
        boolean result = this.removeByIds(ids);
        if (result) {
            log.info("权益规则元数据删除成功，删除数量: {}", ids.size());
        } else {
            log.error("权益规则元数据删除失败，ID列表: {}", ids);
            Assert.isFalse(true, "元数据信息删除失败 ==> 数据库操作异常");
        }
    }
}
