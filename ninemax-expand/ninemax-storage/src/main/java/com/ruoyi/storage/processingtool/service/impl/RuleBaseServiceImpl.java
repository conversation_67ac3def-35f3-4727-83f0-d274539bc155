package com.ruoyi.storage.processingtool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.storage.api.service.AnalysisService;
import com.ruoyi.storage.api.vo.ExFileVO;
import com.ruoyi.storage.api.vo.ListSelect;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleAnalysis;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.dto.HandleRuleAnalysisToolDTO;
import com.ruoyi.storage.processingtool.mapper.RuleBaseMapper;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisService;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisValidationService;
import com.ruoyi.storage.processingtool.service.IRuleBaseService;
import com.ruoyi.storage.processingtool.vo.AddRuleBaseReqVO;
import com.ruoyi.storage.processingtool.vo.QueryRuleBaseSelectReqVO;
import com.ruoyi.storage.utils.QueryCommon;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleBaseServiceImpl extends BaseServiceImpl<RuleBaseMapper, RuleBase> implements IRuleBaseService {

    @Resource
    @Lazy
    private IRuleAnalysisValidationService ruleAnalysisValidationService;
    @Resource
    @Lazy
    private AnalysisService analysisService;
    @Resource
    @Lazy
    private IRuleAnalysisService ruleAnalysisService;
    // 在类中添加线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    public IPage<RuleBase> queryList(RuleBase ruleBase, Page<RuleBase> page) {

        QueryWrapper<RuleBase> ruleBaseQueryWrapper = QueryCommon.buildQueryWrapper(ruleBase);
        String searchTime = ruleBase.getSearchCreatTime();
        if (StringUtils.isNotEmpty(searchTime)) {
            String[] split = searchTime.split(",");
            ruleBaseQueryWrapper.lambda().between(RuleBase::getCreateTime, split[0], split[1]);
        }
        Page<RuleBase> ruleBasePage = this.page(page, ruleBaseQueryWrapper);
        // 处理关联规则效验文件
        List<RuleAnalysisValidation> analysisValidationList = ruleAnalysisValidationService.list(new LambdaQueryWrapper<RuleAnalysisValidation>().eq(RuleAnalysisValidation::getType, 0));
        if (!analysisValidationList.isEmpty()) {
            Map<String, String> stringMap = analysisValidationList.stream().collect(Collectors.toMap(RuleAnalysisValidation::getRuleId, RuleAnalysisValidation::getId));
            List<RuleBase> records = ruleBasePage.getRecords();
            for (RuleBase rule : records) {
                String ruleId = rule.getId();
                String ruleValidId = stringMap.get(ruleId);
                rule.setRuleValidId(ruleValidId);
            }
            ruleBasePage.setRecords(records);
        }

        return ruleBasePage;
    }

    /**
     * 新增数据
     * @param addRuleBaseReqVO 新增数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addRuleBase(AddRuleBaseReqVO addRuleBaseReqVO) {
        log.info("新增解析规则数据:{}", addRuleBaseReqVO);
        List<ExFileVO> fileList = addRuleBaseReqVO.getFileList();
        Assert.isFalse(fileList.isEmpty(), "解析映射规则添加失败==> 请上传样例文件!");
        RuleBase ruleBase = RuleBase.convertToRuleBase(addRuleBaseReqVO);
        //  1、解析规则效验
        validationRuleBase(ruleBase, true);
        // 2、解析规则保存
        this.save(ruleBase);
        // 3.校验规则表
        if (StringUtils.equalsIgnoreCase(ruleBase.getRuleType(), "ANALYSIS")) {
            // 4、添加规则校验表
            ExFileVO exFileVO = fileList.get(0);
            String filePath = exFileVO.getUrl();
            String fileName = exFileVO.getName();
            RuleAnalysisValidation ruleAnalysisValidation = RuleAnalysisValidation.builder()
                    .ruleId(ruleBase.getId())
                    .name(ruleBase.getName())
                    .fileName(fileName)
                    .filePath(filePath)
                    .md5(exFileVO.getMd5())
                    .fileSize(exFileVO.getSize())
                    .build();
            ruleAnalysisValidationService.save(ruleAnalysisValidation);
            // 4、根据规则文件进行工具接口调用 ==> 使用线程池异步执行
            log.info("根据规则文件进行工具接口调用，解析映射规则：{} ", ruleBase.getName());
            executorService.execute(() -> analysisService.toolInterfaceCall(HandleRuleAnalysisToolDTO.builder().ruleBase(ruleBase).ruleAnalysisValidation(ruleAnalysisValidation).initFlag(true).build()));
        }
    }

    /**
     * 更新规则
     *
     * @param ruleBase 规则对象
     */
    @Override
    public void updateRuleBase(RuleBase ruleBase) {
        //  1、解析规则效验
        validationRuleBase(ruleBase, false);
        this.updateById(ruleBase);
    }

    /**
     * 规则效验 ==> 规则名称唯一
     *
     * @param ruleBase 规则对象
     */
    private void validationRuleBase(RuleBase ruleBase, boolean isAdd) {
        String ruleBaseName = ruleBase.getName();
        Assert.isFalse(StringUtils.isEmpty(ruleBaseName), "规则名称不能为空");
        LambdaQueryWrapper<RuleBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleBase::getName, ruleBaseName).last("LIMIT 1");
        RuleBase dbRuleBase = this.getOne(queryWrapper);
        if (isAdd) {
            Assert.isFalse(dbRuleBase != null, "规则名称已存在");
        } else {
            Assert.isFalse(dbRuleBase != null && !StringUtils.endsWithIgnoreCase(dbRuleBase.getId(), ruleBase.getId()), "规则名称已存在");
            if (dbRuleBase != null) {
                String useStatus = ruleBase.getUseStatus();
                String ruleStatus = dbRuleBase.getRuleStatus();
                Assert.isFalse(StringUtils.equals(useStatus, "Y") && StringUtils.equals(ruleStatus, "EXECUTING"), "规则启用失败，规则生成中");
                Assert.isFalse(StringUtils.equals(useStatus, "Y") && StringUtils.equals(ruleStatus, "PENDING"), "规则启用失败，存在待处理节点");
            }
        }
    }

    @Override
    public List<ListSelect> querySelectList(QueryRuleBaseSelectReqVO queryRuleBaseSelectReqVO) {
        List<ListSelect> selectList = Collections.emptyList();
        LambdaQueryWrapper<RuleBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleBase::getRuleType, queryRuleBaseSelectReqVO.getRuleType());
        if (StringUtils.isNotBlank(queryRuleBaseSelectReqVO.getRuleStatus())) {
            queryWrapper.eq(RuleBase::getRuleStatus, queryRuleBaseSelectReqVO.getRuleStatus());
        }
        if (StringUtils.isNotBlank(queryRuleBaseSelectReqVO.getSourceId())) {
            queryWrapper.eq(RuleBase::getSourceId, queryRuleBaseSelectReqVO.getSourceId());
        }
        if (StringUtils.isNotBlank(queryRuleBaseSelectReqVO.getDocType())) {
            queryWrapper.eq(RuleBase::getDocType, queryRuleBaseSelectReqVO.getDocType());
        }
        List<RuleBase> list = this.list(queryWrapper);
        if (list != null && !list.isEmpty()) {
            selectList = list.stream().map(ruleBase -> ListSelect.builder()
                    .value(ruleBase.getId())
                    .text(ruleBase.getName())
                    .status("")
                    .build()).toList();
        }
        return selectList;
    }

    /**
     * 重新发送规则
     *
     * @param ruleId 规则ID
     */
    @Override
    public void resendRuleBase(String ruleId) {
        RuleBase ruleBase = this.getById(ruleId);
        RuleAnalysisValidation ruleAnalysisValidation = ruleAnalysisValidationService
                .getOne(new LambdaQueryWrapper<RuleAnalysisValidation>()
                        .eq(RuleAnalysisValidation::getRuleId, ruleId)
                        .eq(RuleAnalysisValidation::getType, 0)
                        .last("LIMIT 1"));

        List<RuleAnalysis> ruleAnalysisList = ruleAnalysisService.getRuleAnalysisListByAnalysisRuleId(ruleId, ruleAnalysisValidation.getId());
        Assert.isFalse(!ruleAnalysisList.isEmpty(), "规则重新发送失败，当前规则已存在解析节点数据");

        log.info("重新发送：解析映射规则数据：{} | 规则文件解析映射效验数据:{}", ruleBase, ruleAnalysisValidation);
        // 使用线程池异步执行
        executorService.execute(() -> analysisService.toolInterfaceCall(HandleRuleAnalysisToolDTO.builder().ruleBase(ruleBase).ruleAnalysisValidation(ruleAnalysisValidation).initFlag(true).build()));
    }

}
