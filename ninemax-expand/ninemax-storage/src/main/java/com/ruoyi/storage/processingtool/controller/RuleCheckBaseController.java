package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.mapper.RuleCheckBaseMapper;
import com.ruoyi.storage.processingtool.service.IRuleCheckBaseService;
import com.ruoyi.storage.processingtool.service.impl.RuleCheckBaseServiceImpl;
import com.ruoyi.storage.processingtool.vo.AddRuleBaseReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleCheckBase")
@Validated
public class RuleCheckBaseController extends BaseController<RuleCheckBaseMapper, RuleBase> {
    RuleCheckBaseController(RuleCheckBaseServiceImpl service) {
        super(service, RuleBase.class);
    }

    @Autowired
    private IRuleCheckBaseService ruleCheckBaseService;

    /**
     * 新增数据
     *
     * @param addRuleBaseReqVO 添加数据请求参数
     * @return 操作结果
     */
    @PostMapping("/addRuleCheckBase")
    public R<?> addRuleCheckBase(@Valid @RequestBody AddRuleBaseReqVO addRuleBaseReqVO) {
        ruleCheckBaseService.addRuleCheckBase(addRuleBaseReqVO);
        return R.ok();
    }

    /**
     * 修改数据
     *
     * @param ruleBase 实体对象
     * @return 操作结果
     */
    @Override
    @Log(title = "校验规则-修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody RuleBase ruleBase) {
        ruleCheckBaseService.updateRuleCheckBase(ruleBase);
        return R.ok();
    }

    /**
     * 重新发送规则校验请求
     *
     * @param ruleId 规则ID
     * @return 操作结果
     */
    @PostMapping("/resendRuleBase/{ruleId}")
    public R<?> resendRuleBase(@PathVariable("ruleId") String ruleId) {
        ruleCheckBaseService.resendRuleCheckBase(ruleId);
        return R.ok();
    }
}
