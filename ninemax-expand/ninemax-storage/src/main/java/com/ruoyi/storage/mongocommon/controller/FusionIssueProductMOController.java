package com.ruoyi.storage.mongocommon.controller;

import com.anwen.mongo.model.PageResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.mongocommon.domain.FusionIssueProductMO;
import com.ruoyi.storage.mongocommon.service.IFusionIssueProductMOService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fusionIssueProduct")
public class FusionIssueProductMOController {

    @Resource
    private IFusionIssueProductMOService fusionIssueProductMOService;

    @GetMapping("/list")
    public R<?> list(
            FusionIssueProductMO fusionIssueProductMO,
            @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        PageResult<FusionIssueProductMO> page = fusionIssueProductMOService.page(pageNum, pageSize);
        return R.ok(page);
    }
}
