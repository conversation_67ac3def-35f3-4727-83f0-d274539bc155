package com.ruoyi.storage.equity.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.equity.domain.RuleEquityMetadataDetail;
import com.ruoyi.storage.equity.mapper.RuleEquityMetadataDetailMapper;
import com.ruoyi.storage.equity.service.IRuleEquityMetadataDetailService;
import com.ruoyi.storage.equity.service.impl.RuleEquityMetadataDetailServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 权益规则元数据详情Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/ruleEquityMetadataDetail")
@Validated
public class RuleEquityMetadataDetailController extends BaseController<RuleEquityMetadataDetailMapper, RuleEquityMetadataDetail> {

    public RuleEquityMetadataDetailController(RuleEquityMetadataDetailServiceImpl service) {
        super(service, RuleEquityMetadataDetail.class);
    }

    @Autowired
    private IRuleEquityMetadataDetailService ruleEquityMetadataDetailService;

    /**
     * 获取元数据字段值
     *
     * @param metadataId 元数据id
     * @return
     */
    @GetMapping("/fieldValues/{metadataId}")
    public R<?> fieldValues(@PathVariable("metadataId") String metadataId) {
        return R.ok(ruleEquityMetadataDetailService.getFieldValues(metadataId));
    }
}
