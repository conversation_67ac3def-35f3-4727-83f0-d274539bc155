package com.ruoyi.storage.processingtool.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.storage.api.vo.ExFileVO;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationWord;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationWordMapper;
import com.ruoyi.storage.processingtool.service.IRuleNormalizationWordService;
import com.ruoyi.storage.processingtool.vo.AddNormalWordReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleNormalizationWordServiceImpl extends BaseServiceImpl<RuleNormalizationWordMapper, RuleNormalizationWord> implements IRuleNormalizationWordService {

    /**
     * 新增词表信息
     *
     * @param addNormalWordReqVO 新增数据
     */
    @Override
    public void addNormalWord(AddNormalWordReqVO addNormalWordReqVO) {
        log.info("词表文件创建开始 ===> {}", addNormalWordReqVO);
        // 1、保存词表信息
        RuleNormalizationWord ruleNormalizationWord = RuleNormalizationWord.builder()
                .wordName(addNormalWordReqVO.getWordName())
                .attributeId(addNormalWordReqVO.getAttributeId())
                .attributeName(addNormalWordReqVO.getAttributeName())
                .interactiveType(addNormalWordReqVO.getInteractiveType())
                .build();
        List<ExFileVO> fileList = addNormalWordReqVO.getFileList();
        if (fileList != null && !fileList.isEmpty()) {
            ExFileVO exFileVO = fileList.get(0);
            ruleNormalizationWord.setFileName(exFileVO.getName());
            ruleNormalizationWord.setFileSize(exFileVO.getSize());
            ruleNormalizationWord.setMd5(exFileVO.getMd5());
        }
        log.info("词表文件创建开始 ===> {}", ruleNormalizationWord);
        // todo 等待工具平台接口提供
        // 2、调用工具平台接口

        this.save(ruleNormalizationWord);
    }

    /**
     * 替换文件
     *
     * @param addNormalWordReqVO 新增数据
     */
    @Override
    public void replaceFile(AddNormalWordReqVO addNormalWordReqVO) {
        log.info("词表文件替换开始 ===> {}", addNormalWordReqVO);
        // 参数校验
        String id = addNormalWordReqVO.getId();
        Assert.isFalse(StringUtils.isBlank(id), "ID不能为空");
        List<ExFileVO> fileList = addNormalWordReqVO.getFileList();
        RuleNormalizationWord updateNormalizationWord = RuleNormalizationWord.builder()
                .id(id).build();
        if (fileList != null && !fileList.isEmpty()) {
            ExFileVO exFileVO = fileList.get(0);
            updateNormalizationWord.setFileName(exFileVO.getName());
            updateNormalizationWord.setFileSize(exFileVO.getSize());
            updateNormalizationWord.setMd5(exFileVO.getMd5());
            this.updateById(updateNormalizationWord);
        }
        log.info("词表文件替换结束 ===> {}", updateNormalizationWord);
    }
}
