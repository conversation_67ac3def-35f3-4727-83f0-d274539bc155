package com.ruoyi.storage.processingtool.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 智能校验规则详情表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "rule_check_detail")
public class RuleCheckDetail extends BaseEntity3 {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 路径ID
     */
    @Query(type = QueryType.EQ)
    private String pathId;

    /**
     * 规则ID
     */
    @Query(type = QueryType.EQ)
    private String ruleId;

    /**
     * 字段名称
     */
    @Query(type = QueryType.LIKE)
    private String fieldName;

    /**
     * 校验项
     */
    private String checkItem;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 条件类型
     */
    @Query(type = QueryType.EQ)
    private String checkType;

    /**
     * 表达式
     */
    private String expression;

    /**
     * 属性值
     */
    private String attributeValue;

    /**
     * 提示语
     */
    private String prompt;

    /**
     * 优先级
     */
    @Query(type = QueryType.EQ)
    private String priority;

    /**
     * 应用状态
     */
    @Query(type = QueryType.EQ)
    private String status;

    /**
     * 是否标签
     */
    @Query(type = QueryType.EQ)
    private String isLabel;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 备注
     */
    private String remark;
}
