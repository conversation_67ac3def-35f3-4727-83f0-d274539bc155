package com.ruoyi.storage.processingtool.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UpdateRuleCheckStatusReqVO {

    @NotBlank(message = "校验规则ID不能为空")
    private String ruleId;

    private String ruleValidId;

    private List<String> ids;

    @NotBlank(message = "审核状态不能为空")
    private String checkStatus;

}
