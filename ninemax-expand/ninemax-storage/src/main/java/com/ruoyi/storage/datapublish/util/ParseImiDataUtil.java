package com.ruoyi.storage.datapublish.util;

import com.ruoyi.common.core.utils.StringUtils;
import org.bson.Document;

import java.util.HashMap;
import java.util.Map;

public class ParseImiDataUtil {

    /**
     * 获取文章标题
     * 解析路径：record.source-meta.source-title-group.abbrev-source-title
     * 注意：路径中所有节点可能是数组或非数组，abbrev-source-title下可能还有_text子节点
     * 节点可能包含属性，需要根据属性筛选
     *
     * @param document MongoDB文档
     * @return 文章标题
     */
    public static String getJournalTitle(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("source-meta"),
                new NodeDef("source-title-group"),
                new NodeDef("abbrev-source-title")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static String getIssn(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("source-meta"),
                new NodeDef("issn").addAttr("publication-format","Electronic")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static String getPubyear(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("source-meta"),
                new NodeDef("pub-date"),
                new NodeDef("year")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static String getVolume(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("source-meta"),
                new NodeDef("volume-issue-group"),
                new NodeDef("volume")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static String getIssue(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("source-meta"),
                new NodeDef("volume-issue-group"),
                new NodeDef("issue")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static String getPublish(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("source-meta"),
                new NodeDef("publisher"),
                new NodeDef("address"),
                new NodeDef("country"),
                new NodeDef("country")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    /**
     * 获取文章标题
     * 解析路径：record.article-meta.title-group.article-title
     * 注意：路径中所有节点可能是数组或非数组，article-title下可能还有_text子节点
     * 节点可能包含属性，需要根据属性筛选
     *
     * @param document MongoDB文档
     * @return 文章标题
     */
    public static String getArticleTitle(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("article-meta"),
                new NodeDef("title-group"),
                new NodeDef("article-title")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static Map<String,String> getFpageAndLpage(Document document) {
        Map<String,String> map = new HashMap<String,String>();
        if (document == null) {
            return map;
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("article-meta"),
                new NodeDef("fpage")
        };

        // 使用通用解析方法获取结果
        String fpage = ParseImiToolUtil.parseStringByPath(document, path);
        if(StringUtils.isEmpty(fpage)){
            NodeDef[] path1 = {
                    new NodeDef("article-meta"),
                    new NodeDef("page-range")
            };

            // 使用通用解析方法获取结果
            String pageRange = ParseImiToolUtil.parseStringByPath(document, path1);
            if(StringUtils.isNotEmpty(pageRange)){
                String[] split = pageRange.split("-");
                fpage = split[0];
            }
        }else{
            String[] split = fpage.split("-");
            fpage = split[0];
        }

        // 定义解析路径
        NodeDef[] path2 = {
                new NodeDef("article-meta"),
                new NodeDef("lpage")
        };

        // 使用通用解析方法获取结果
        String lpage = ParseImiToolUtil.parseStringByPath(document, path2);
        if(StringUtils.isEmpty(lpage)){
            NodeDef[] path3 = {
                    new NodeDef("article-meta"),
                    new NodeDef("page-range")
            };

            // 使用通用解析方法获取结果
            String pageRange = ParseImiToolUtil.parseStringByPath(document, path3);
            if(StringUtils.isNotEmpty(pageRange)){
                String[] split = pageRange.split("-");
                if(split.length == 2){
                    lpage = split[1];
                    if(StringUtils.isEmpty(fpage)) {
                        fpage = split[0];
                    }
                }
            }
        }else{
            String[] split = lpage.split("-");
            if(split.length == 2){
                fpage = split[0];
                lpage = split[1];
            }
        }

        map.put("fpage",fpage);
        map.put("lpage",lpage);

        return map;
    }

    public static String getDoi(Document document) {
        if (document == null) {
            return "";
        }


        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("article-meta"),
                new NodeDef("elocation-id").addAttr("elocation-id-type","doi")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static String getAbstract(Document document) {
        if (document == null) {
            return "";
        }


        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("article-meta"),
                new NodeDef("abstracts"),
                new NodeDef("abstract")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static String getKeyword(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("subj-class-kwd"),
                new NodeDef("substance-group"),
                new NodeDef("substance-name",true)
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }

    public static String getAuthors(Document document) {
        if (document == null) {
            return "";
        }

        // 定义解析路径
        NodeDef[] path = {
                new NodeDef("contrib-group"),
                new NodeDef("contrib"),
                new NodeDef("first-author")
        };

        // 使用通用解析方法获取结果
        return ParseImiToolUtil.parseStringByPath(document, path);
    }
}
