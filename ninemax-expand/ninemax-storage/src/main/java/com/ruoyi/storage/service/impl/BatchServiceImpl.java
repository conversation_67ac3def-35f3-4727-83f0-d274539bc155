package com.ruoyi.storage.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.storage.domain.Batch;
import com.ruoyi.storage.mapper.BatchMapper;
import com.ruoyi.storage.service.IBatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 批次Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@Service
public class BatchServiceImpl extends ServiceImpl<BatchMapper, Batch> implements IBatchService
{



}
