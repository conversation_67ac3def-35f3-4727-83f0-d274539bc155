package com.ruoyi.storage.fullArchive.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

/**
 * 数据源信息 VO 类（用于前端展示）
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ArchiveDataSourceVO {

    /**
     * 数据源ID
     */
    private Long sourceId;

    // ********************************* 核心字段 ************************************

    /**
     * 文件数据源名称
     */
    private String sourceName;

    /**
     * 文件类型(PDF全文/图像/图表/研究数据等)
     */
    private String fileType;

    /**
     * 文献类型(期刊/图书/会议/报告等)
     */
    private String documentType;

    /**
     * 收割方式(解析关联数据/系统交互等)
     */
    private String harvestMethod;

    /**
     * 来源类型(商业订购/第三方授权/网络采集/OA采集等)
     */
    private String sourceType;

    /**
     * 关联字段(多个字段以逗号分隔)
     */
    private String relatedFields;

    /**
     * 状态(0启用 1停用)
     */
    private String status;

    /**
     * 状态文本（将0/1转换为"启用"/"停用"，便于前端直接展示）
     */
    private String statusText;

    // ********************************* 公共字段 ************************************

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间（格式化展示）
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间（格式化展示）
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;


    // 可选：添加状态转换的方法（也可在服务层处理）
    public void setStatus(String status) {
        this.status = status;
        // 自动转换状态为文本
        this.statusText = "0".equals(status) ? "启用" : "停用";
    }

    private Integer pageNum;
    private Integer pageSize;

}
