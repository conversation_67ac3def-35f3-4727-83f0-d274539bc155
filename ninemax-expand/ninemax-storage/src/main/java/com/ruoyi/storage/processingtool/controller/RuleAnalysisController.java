package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleAnalysis;
import com.ruoyi.storage.processingtool.mapper.RuleAnalysisMapper;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisService;
import com.ruoyi.storage.processingtool.service.impl.RuleAnalysisServiceImpl;
import com.ruoyi.storage.processingtool.vo.AddRuleAnalysisReqVO;
import com.ruoyi.storage.processingtool.vo.UpdateRuleAnalysisStatusReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleAnalysis")
@Validated
public class RuleAnalysisController extends BaseController<RuleAnalysisMapper, RuleAnalysis> {
    RuleAnalysisController(RuleAnalysisServiceImpl service) {
        super(service, RuleAnalysis.class);
    }

    @Autowired
    private IRuleAnalysisService ruleAnalysisService;

    /**
     * 更新路径状态
     *
     * @param updateAnalysisStatusReqVO 更改接口应用状态请求参数
     * @return 操作结果
     */
    @PostMapping("/updateAnalysisStatus")
    public R<?> updateAnalysisStatus(@Valid @RequestBody UpdateRuleAnalysisStatusReqVO updateAnalysisStatusReqVO) {
        ruleAnalysisService.updateAnalysisStatus(updateAnalysisStatusReqVO);
        return R.ok();
    }

    /**
     * 添加规则分析
     *
     * @param addRuleAnalysisReqVO 添加规则分析请求参数
     * @return 操作结果
     */
    @PostMapping("/addRuleAnalysis")
    public R<?> addRuleAnalysis(@Valid @RequestBody AddRuleAnalysisReqVO addRuleAnalysisReqVO) {
        ruleAnalysisService.addRuleAnalysis(addRuleAnalysisReqVO);
        return R.ok();
    }

}
