package com.ruoyi.storage.async.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.storage.async.domain.AsyncTask;
import com.ruoyi.storage.async.vo.AsyncTaskCreateReqVO;
import com.ruoyi.storage.async.vo.AsyncTaskRespVO;

/**
 * 异步任务服务接口
 * 
 * <AUTHOR>
 */
public interface IAsyncTaskService extends IService<AsyncTask> {

    /**
     * 创建异步任务
     * 
     * @param createReqVO 创建请求参数
     * @return 任务ID
     */
    String createAsyncTask(AsyncTaskCreateReqVO createReqVO);

    /**
     * 根据任务ID查询任务信息
     * 
     * @param taskId 任务ID
     * @return 任务信息
     */
    AsyncTaskRespVO getTaskByTaskId(String taskId);

    /**
     * 根据工具方任务ID查询任务信息
     * 
     * @param toolTaskId 工具方任务ID
     * @return 任务信息
     */
    AsyncTask getTaskByToolTaskId(String toolTaskId);

    /**
     * 更新任务状态为处理中
     * 
     * @param taskId 任务ID
     * @param toolTaskId 工具方任务ID
     * @return 是否成功
     */
    boolean updateTaskProcessing(String taskId, String toolTaskId);

    /**
     * 更新任务为成功状态
     * 
     * @param taskId 任务ID
     * @param responseData 响应数据
     * @return 是否成功
     */
    boolean updateTaskSuccess(String taskId, String responseData);

    /**
     * 更新任务为失败状态
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 是否成功
     */
    boolean updateTaskFailure(String taskId, String errorMessage);

    /**
     * 更新任务为超时状态
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean updateTaskTimeout(String taskId);

    /**
     * 增加任务重试次数
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean incrementRetryCount(String taskId);

    /**
     * 处理超时任务
     * 
     * @return 处理的任务数量
     */
    int handleTimeoutTasks();

    /**
     * 处理重试任务
     * 
     * @return 处理的任务数量
     */
    int handleRetryTasks();

    /**
     * 重新发送任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean resendTask(String taskId);
}
