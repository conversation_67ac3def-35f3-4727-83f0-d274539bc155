package com.ruoyi.storage.async.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 异步任务创建请求VO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsyncTaskCreateReqVO {

    /**
     * 业务类型（必填）
     */
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    /**
     * 业务关联ID（必填）
     */
    @NotBlank(message = "业务关联ID不能为空")
    private String businessId;

    /**
     * 回调地址（必填）
     */
    @NotBlank(message = "回调地址不能为空")
    private String callbackUrl;

    /**
     * 请求数据（JSON格式）
     */
    private String requestData;

    /**
     * 超时时间（分钟，默认30分钟）
     */
    @Builder.Default
    private Integer timeoutMinutes = 30;

    /**
     * 最大重试次数（默认3次）
     */
    @Builder.Default
    private Integer maxRetryCount = 3;
}
