package com.ruoyi.storage.processingtool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationWordDetail;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationWordDetailMapper;
import com.ruoyi.storage.processingtool.service.IRuleNormalizationWordDetailService;
import com.ruoyi.storage.processingtool.vo.AddRuleNormalizationWordDetailReqVO;
import com.ruoyi.storage.processingtool.vo.DeleteRuleNormalizationWordDetailReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleNormalizationWordDetailServiceImpl extends BaseServiceImpl<RuleNormalizationWordDetailMapper, RuleNormalizationWordDetail> implements IRuleNormalizationWordDetailService {

    /**
     * 新增
     *
     * @param addRuleNormalizationWordDetailReqVO 新增参数
     * @return
     */
    @Override
    public boolean addRuleNormalizationWordDetail(AddRuleNormalizationWordDetailReqVO addRuleNormalizationWordDetailReqVO) {
        Integer type = addRuleNormalizationWordDetailReqVO.getType();
        RuleNormalizationWordDetail ruleNormalizationWordDetail = RuleNormalizationWordDetail.builder()
                .normalizationWordId(addRuleNormalizationWordDetailReqVO.getNormalizationWordId())
                .type(type).build();
        return this.save(ruleNormalizationWordDetail);
    }

    /**
     * 删除
     *
     * @param deleteRuleNormalizationWordDetailReqVO 删除参数
     * @return
     */
    @Override
    public boolean deleteRuleNormalizationWordDetail(DeleteRuleNormalizationWordDetailReqVO deleteRuleNormalizationWordDetailReqVO) {
        // 进行属性值或者属性值映射数据的删除操作
        String id = deleteRuleNormalizationWordDetailReqVO.getId();
        RuleNormalizationWordDetail ruleNormalizationWordDetail = this.getById(id);
        Integer type = ruleNormalizationWordDetail.getType();
        if (type == 0) {
            checkRuleNormalizationWordDetailMappingExists(ruleNormalizationWordDetail);
        }
        return this.removeById(id);
    }

    /**
     * 检查映射关系是否存在
     *
     * @param ruleNormalizationWordDetail 映射关系
     */
    private void checkRuleNormalizationWordDetailMappingExists(RuleNormalizationWordDetail ruleNormalizationWordDetail) {
        // 检查映射关系是否存在
        String id = ruleNormalizationWordDetail.getId();
        // 判断当前数据库中属性值映射是否存在当前数据信息关联
        LambdaQueryWrapper<RuleNormalizationWordDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleNormalizationWordDetail::getType, 1)
                .eq(RuleNormalizationWordDetail::getNormalizationWordId, ruleNormalizationWordDetail.getNormalizationWordId());
        List<RuleNormalizationWordDetail> normalizationWordDetails = this.list(queryWrapper);

        // 检查normalizationWordDetails集合中是否存在originalAttributeId或targetAttributeId等于当前id的数据
        if (normalizationWordDetails != null && !normalizationWordDetails.isEmpty()) {
            boolean hasRelatedMapping = normalizationWordDetails.stream()
                    .anyMatch(detail ->
                            id.equals(detail.getOriginalAttributeId()) || id.equals(detail.getTargetAttributeId())
                    );
            Assert.isFalse(hasRelatedMapping, "删除失败：该属性值存在关联的映射关系，请先删除相关映射后再进行删除操作");
        }
        log.info("映射关系检查通过，可以删除ID为 {} 的数据", id);
    }
}
