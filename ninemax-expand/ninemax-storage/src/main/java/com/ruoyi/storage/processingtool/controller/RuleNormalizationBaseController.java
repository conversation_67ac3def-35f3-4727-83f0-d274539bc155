package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationBaseMapper;
import com.ruoyi.storage.processingtool.service.IRuleNormalizationBaseService;
import com.ruoyi.storage.processingtool.service.impl.RuleNormalizationBaseServiceImpl;
import com.ruoyi.storage.processingtool.vo.AddRuleBaseReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleNormalizationBase")
@Validated
public class RuleNormalizationBaseController extends BaseController<RuleNormalizationBaseMapper, RuleBase> {
    RuleNormalizationBaseController(RuleNormalizationBaseServiceImpl service) {
        super(service, RuleBase.class);
    }

    @Autowired
    private IRuleNormalizationBaseService ruleNormalizationBaseService;

    /**
     * 新增数据
     *
     * @param addRuleBaseReqVO 添加数据请求参数
     * @return 操作结果
     */
    @PostMapping("/addRuleNormalizationBase")
    public R<?> addRuleCheckBase(@Valid @RequestBody AddRuleBaseReqVO addRuleBaseReqVO) {
        ruleNormalizationBaseService.addRuleNormalizationBase(addRuleBaseReqVO);
        return R.ok();
    }

    /**
     * 修改数据
     *
     * @param ruleBase 实体对象
     * @return 操作结果
     */
    @Override
    @Log(title = "著录规范化规则-修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody RuleBase ruleBase) {
        ruleNormalizationBaseService.updateRuleNormalizationBase(ruleBase);
        return R.ok();
    }

}
