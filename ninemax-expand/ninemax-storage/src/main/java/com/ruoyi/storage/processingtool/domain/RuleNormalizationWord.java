package com.ruoyi.storage.processingtool.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 数据著录规范化词表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "rule_normalization_word")
public class RuleNormalizationWord extends BaseEntity3 {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 词表名称
     */
    private String wordName;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private String fileSize;

    /**
     * 文件MD5值
     */
    private String md5;

    /**
     * 属性ID
     */
    private String attributeId;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 属性数量
     */
    private String attributeCount;

    /**
     * 交互类型
     */
    private String interactiveType;

    /**
     * 工具状态
     */
    @Query(type = QueryType.EQ)
    private String toolStatus;
}
