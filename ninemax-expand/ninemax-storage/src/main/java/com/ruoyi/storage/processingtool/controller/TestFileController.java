package com.ruoyi.storage.processingtool.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.api.RemoteExFileService;
import com.ruoyi.storage.api.vo.ExFile;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Validated
public class TestFileController {

    @Resource
    private RemoteExFileService remoteExFileService;

    /**
     * 新增数据
     *
     * @param file 添加数据请求参数
     * @return 操作结果
     */
    @PostMapping("/upload")
    public R<?> upload(@RequestPart(value = "file") MultipartFile file, @RequestParam("projectPath") String projectPath, @RequestParam("isSpecifiedFile") Boolean isSpecifiedFile) {
        R<ExFile> fileResult = remoteExFileService.uploadFile(file, projectPath, isSpecifiedFile);
        ExFile data = fileResult.getData();
        return fileResult;
    }

    @GetMapping("/download")
    public R<?> download(@RequestParam("filePath") String filePath,
                         @RequestParam("fileName") String fileName) {
        //将文件拷贝到临时文件夹中
        try (Response response = remoteExFileService.resourceDownload(filePath);
             InputStream is = response.body().asInputStream()) {
            //String responseMessage = is.toString();
            //获取文件对象
            //这里使用uuid进行相同文件名的区分，防止进行文件的覆盖(目标文件)
//            String tempFilePath = System.getProperty("java.io.tmpdir") + StrUtil.uuid() + File.separator + fileName;
            String tempFilePath = "d://home//" + StrUtil.uuid() + File.separator + fileName;
            File dest = new File(tempFilePath);
            //将输入流复制到临时文件夹的目标文件路径下
            FileUtils.copyInputStreamToFile(is, dest);
            //获取文件大小
            long length = dest.length();
            if (length == 0) {
                //如果没有该文件
                log.info("文件不存在!!!【文件获取错误】==>{}", filePath);
            }
        } catch (Exception e) {
            log.error("未知错误!!!【文件获取错误】==>文件路径：{} 错误信息==> {}", filePath, e.getMessage());
            return null;
        }
        return R.ok();
    }

    /**
     * 测试智能效验工具
     *
     * @param file 添加数据请求参数
     * @return 操作结果
     */
    @PostMapping("/testValidate")
    public R<?> testValidate(@RequestPart(value = "file") MultipartFile file, @RequestParam("projectPath") String projectPath, @RequestParam("isSpecifiedFile") Boolean isSpecifiedFile) {
        R<ExFile> fileResult = remoteExFileService.uploadFile(file, projectPath, isSpecifiedFile);
        ExFile data = fileResult.getData();
        return fileResult;
    }
}
