package com.ruoyi.storage.processingtool.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 数据著录规范化词表详情表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "rule_normalization_word_detail")
public class RuleNormalizationWordDetail extends BaseEntity3 {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 词表主键ID
     */
    @Query(type = QueryType.EQ)
    private String normalizationWordId;

    /**
     * 元素名称
     */
    @Query(type = QueryType.LIKE)
    private String elementName;

    /**
     * 属性值
     */
    private String attributeValue;

    /**
     * 属性状态
     */
    private String attributeStatus;

    /**
     * 原属性ID
     */
    private String originalAttributeId;

    /**
     * 原属性
     */
    private String originalAttribute;

    /**
     * 目标属性ID
     */
    private String targetAttributeId;

    /**
     * 目标属性
     */
    private String targetAttribute;

    /**
     * 数据类型（0-属性值|1-属性映射）
     */
    @Query(type = QueryType.EQ)
    private Integer type;

    /**
     * 是否子查询属性值
     */
    private String checkStatus;


}
