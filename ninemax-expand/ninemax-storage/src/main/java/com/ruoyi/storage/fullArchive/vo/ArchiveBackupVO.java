package com.ruoyi.storage.fullArchive.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 保存与备份信息 VO 类（用于前端传参）
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ArchiveBackupVO {

    // 备份源位置
    private String backupSource;

    // 备份目标位置
    private String backupTarget;

    // 备份类型
    private String backupType;

    // 备份频率
    private String backupFrequency;

    // 备份时间(开始时间)
    private String startTime;

    // 下次备份时间
    private String nextBackupTime;

    // 清楚机制
    private String cleanupPolicy;

    private Integer pageNum;
    private Integer pageSize;

}
