package com.ruoyi.storage.async.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.storage.async.domain.AsyncTask;
import com.ruoyi.storage.async.mapper.AsyncTaskMapper;
import com.ruoyi.storage.async.service.IAsyncTaskService;
import com.ruoyi.storage.async.vo.AsyncTaskCreateReqVO;
import com.ruoyi.storage.async.vo.AsyncTaskRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 异步任务服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncTaskServiceImpl extends ServiceImpl<AsyncTaskMapper, AsyncTask> implements IAsyncTaskService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createAsyncTask(AsyncTaskCreateReqVO createReqVO) {
        log.info("创建异步任务，请求参数：{}", createReqVO);
        
        String taskId = UUID.randomUUID().toString().replace("-", "");
        Date now = new Date();
        
        AsyncTask asyncTask = AsyncTask.builder()
                .taskId(taskId)
                .businessType(createReqVO.getBusinessType())
                .businessId(createReqVO.getBusinessId())
                .callbackUrl(createReqVO.getCallbackUrl())
                .taskStatus(AsyncTask.TaskStatus.PENDING.getCode())
                .requestData(createReqVO.getRequestData())
                .retryCount(0)
                .maxRetryCount(createReqVO.getMaxRetryCount())
                .timeoutMinutes(createReqVO.getTimeoutMinutes())
                .startTime(now)
                .build();
        
        asyncTask.setCreateTime(now);
        asyncTask.setUpdateTime(now);
        
        boolean saved = this.save(asyncTask);
        if (!saved) {
            throw new RuntimeException("创建异步任务失败");
        }
        
        log.info("异步任务创建成功，任务ID：{}", taskId);
        return taskId;
    }

    @Override
    public AsyncTaskRespVO getTaskByTaskId(String taskId) {
        AsyncTask asyncTask = baseMapper.selectByTaskId(taskId);
        if (asyncTask == null) {
            return null;
        }
        
        AsyncTaskRespVO respVO = new AsyncTaskRespVO();
        BeanUtils.copyProperties(asyncTask, respVO);
        
        // 设置状态描述
        respVO.setTaskStatusDesc(getTaskStatusDesc(asyncTask.getTaskStatus()));
        
        return respVO;
    }

    @Override
    public AsyncTask getTaskByToolTaskId(String toolTaskId) {
        return baseMapper.selectByToolTaskId(toolTaskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskProcessing(String taskId, String toolTaskId) {
        log.info("更新任务状态为处理中，任务ID：{}，工具方任务ID：{}", taskId, toolTaskId);
        
        int updated = baseMapper.updateToolTaskId(taskId, toolTaskId, AsyncTask.TaskStatus.PROCESSING.getCode());
        boolean success = updated > 0;
        
        if (success) {
            log.info("任务状态更新成功，任务ID：{}", taskId);
        } else {
            log.warn("任务状态更新失败，任务ID：{}", taskId);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskSuccess(String taskId, String responseData) {
        log.info("更新任务状态为成功，任务ID：{}", taskId);
        
        Date endTime = new Date();
        int updated = baseMapper.updateTaskResponse(taskId, responseData, 
                AsyncTask.TaskStatus.SUCCESS.getCode(), endTime);
        boolean success = updated > 0;
        
        if (success) {
            log.info("任务成功状态更新成功，任务ID：{}", taskId);
        } else {
            log.warn("任务成功状态更新失败，任务ID：{}", taskId);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskFailure(String taskId, String errorMessage) {
        log.info("更新任务状态为失败，任务ID：{}，错误信息：{}", taskId, errorMessage);
        
        Date endTime = new Date();
        int updated = baseMapper.updateTaskError(taskId, errorMessage, 
                AsyncTask.TaskStatus.FAILURE.getCode(), endTime);
        boolean success = updated > 0;
        
        if (success) {
            log.info("任务失败状态更新成功，任务ID：{}", taskId);
        } else {
            log.warn("任务失败状态更新失败，任务ID：{}", taskId);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskTimeout(String taskId) {
        log.info("更新任务状态为超时，任务ID：{}", taskId);
        
        Date endTime = new Date();
        int updated = baseMapper.updateTaskStatus(taskId, AsyncTask.TaskStatus.TIMEOUT.getCode(), endTime);
        boolean success = updated > 0;
        
        if (success) {
            log.info("任务超时状态更新成功，任务ID：{}", taskId);
        } else {
            log.warn("任务超时状态更新失败，任务ID：{}", taskId);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementRetryCount(String taskId) {
        log.info("增加任务重试次数，任务ID：{}", taskId);
        
        int updated = baseMapper.incrementRetryCount(taskId);
        boolean success = updated > 0;
        
        if (success) {
            log.info("任务重试次数增加成功，任务ID：{}", taskId);
        } else {
            log.warn("任务重试次数增加失败，任务ID：{}", taskId);
        }
        
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int handleTimeoutTasks() {
        log.info("开始处理超时任务");
        
        List<AsyncTask> timeoutTasks = baseMapper.selectTimeoutTasks();
        int count = 0;
        
        for (AsyncTask task : timeoutTasks) {
            boolean updated = updateTaskTimeout(task.getTaskId());
            if (updated) {
                count++;
            }
        }
        
        log.info("超时任务处理完成，处理数量：{}", count);
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int handleRetryTasks() {
        log.info("开始处理重试任务");
        
        List<AsyncTask> retryTasks = baseMapper.selectRetryTasks();
        int count = 0;
        
        for (AsyncTask task : retryTasks) {
            boolean resent = resendTask(task.getTaskId());
            if (resent) {
                count++;
            }
        }
        
        log.info("重试任务处理完成，处理数量：{}", count);
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resendTask(String taskId) {
        log.info("重新发送任务，任务ID：{}", taskId);
        
        AsyncTask task = baseMapper.selectByTaskId(taskId);
        if (task == null) {
            log.warn("任务不存在，无法重新发送，任务ID：{}", taskId);
            return false;
        }
        
        // 增加重试次数
        incrementRetryCount(taskId);
        
        // 重置任务状态为待处理
        Date now = new Date();
        int updated = baseMapper.updateTaskStatus(taskId, AsyncTask.TaskStatus.PENDING.getCode(), null);
        
        if (updated > 0) {
            log.info("任务重新发送成功，任务ID：{}", taskId);
            // TODO: 这里需要调用具体的业务处理器重新发送任务
            return true;
        } else {
            log.warn("任务重新发送失败，任务ID：{}", taskId);
            return false;
        }
    }

    /**
     * 获取任务状态描述
     * 
     * @param status 状态码
     * @return 状态描述
     */
    private String getTaskStatusDesc(String status) {
        for (AsyncTask.TaskStatus taskStatus : AsyncTask.TaskStatus.values()) {
            if (taskStatus.getCode().equals(status)) {
                return taskStatus.getDesc();
            }
        }
        return status;
    }
}
