package com.ruoyi.storage.processingtool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.storage.api.service.AnalysisService;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.domain.RuleCheckValidation;
import com.ruoyi.storage.processingtool.mapper.RuleCheckValidationMapper;
import com.ruoyi.storage.processingtool.service.IRuleBaseService;
import com.ruoyi.storage.processingtool.service.IRuleCheckValidationService;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;
import com.ruoyi.storage.utils.QueryCommon;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleCheckValidationServiceImpl extends BaseServiceImpl<RuleCheckValidationMapper, RuleCheckValidation> implements IRuleCheckValidationService {
    @Resource
    private IRuleBaseService ruleBaseService;
    @Resource
    @Lazy
    private AnalysisService analysisService;

    // 在类中添加线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @Override
    public IPage<RuleCheckValidation> queryList(RuleCheckValidation ruleCheckValidation, Page<RuleCheckValidation> page) {

        QueryWrapper<RuleCheckValidation> queryWrapper = QueryCommon.buildQueryWrapper(ruleCheckValidation);
        String searchTime = ruleCheckValidation.getSearchCreatTime();
        if (StringUtils.isNotEmpty(searchTime)) {
            String[] split = searchTime.split(",");
            queryWrapper.lambda().between(RuleCheckValidation::getCreateTime, split[0], split[1]);
        }
        return this.page(page, queryWrapper);
    }

    @Override
    public void validateRule(ValidateRuleReqVO validateRuleReqVO) {
        System.out.println(validateRuleReqVO);
        // 保存数据
        String ruleId = validateRuleReqVO.getRuleId();
        String sampleFile = validateRuleReqVO.getSampleFile();
        RuleBase ruleBase = ruleBaseService.getById(ruleId);
        String fileName = sampleFile.substring(sampleFile.lastIndexOf("/") + 1);
        RuleCheckValidation ruleCheckValidation = RuleCheckValidation.builder()
                .ruleId(ruleId)
                .name(ruleBase.getName())
                .fileName(fileName)
                .filePath(sampleFile)
                .type(1)
                .build();
        this.save(ruleCheckValidation);

        // todo 后期提取到统一的接口调用部分
        // 3、根据规则文件进行工具接口调用 ==> 异步调用
        log.info("校验映射规则数据：{} | 规则文件校验数据:{}", ruleBase, ruleCheckValidation);

        // 使用线程池异步执行
        // todo 后续调用智能校验工具的处理接口
//        executorService.execute(() -> toolInterfaceHandleService.toolInterfaceCall(HandleRuleAnalysisToolDTO.builder().ruleBase(ruleBase).ruleAnalysisValidation(ruleAnalysisValidation).initFlag(false).build()));
    }
}
