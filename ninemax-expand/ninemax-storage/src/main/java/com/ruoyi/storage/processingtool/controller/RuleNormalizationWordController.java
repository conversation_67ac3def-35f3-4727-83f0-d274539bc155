package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationWord;
import com.ruoyi.storage.processingtool.mapper.RuleNormalizationWordMapper;
import com.ruoyi.storage.processingtool.service.IRuleNormalizationWordService;
import com.ruoyi.storage.processingtool.service.impl.RuleNormalizationWordServiceImpl;
import com.ruoyi.storage.processingtool.vo.AddNormalWordReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleNormalizationWord")
@Validated
public class RuleNormalizationWordController extends BaseController<RuleNormalizationWordMapper, RuleNormalizationWord> {
    RuleNormalizationWordController(RuleNormalizationWordServiceImpl service) {
        super(service, RuleNormalizationWord.class);
    }

    @Autowired
    private IRuleNormalizationWordService ruleNormalizationWordService;

    /**
     * 新建词表信息
     *
     * @param addNormalWordReqVO 实体对象
     * @return 操作结果
     */
    @PostMapping("/addNormalWord")
    public R<?> addNormalWord(@Valid @RequestBody AddNormalWordReqVO addNormalWordReqVO) {
        ruleNormalizationWordService.addNormalWord(addNormalWordReqVO);
        return R.ok();
    }

    /**
     * 替换词表信息
     *
     * @param addNormalWordReqVO 实体对象
     * @return 操作结果
     */
    @PostMapping("/replaceFile")
    public R<?> replaceFile(@Valid @RequestBody AddNormalWordReqVO addNormalWordReqVO) {
        ruleNormalizationWordService.replaceFile(addNormalWordReqVO);
        return R.ok();
    }

}
