package com.ruoyi.storage.equity.controller;

import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.equity.domain.RuleEquityTemplate;
import com.ruoyi.storage.equity.mapper.RuleEquityTemplateMapper;
import com.ruoyi.storage.equity.service.impl.RuleEquityTemplateServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权益模版管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/ruleEquityTemplate")
@Validated
public class RuleEquityTemplateController extends BaseController<RuleEquityTemplateMapper, RuleEquityTemplate> {
    
    public RuleEquityTemplateController(RuleEquityTemplateServiceImpl service) {
        super(service, RuleEquityTemplate.class);
    }
}
