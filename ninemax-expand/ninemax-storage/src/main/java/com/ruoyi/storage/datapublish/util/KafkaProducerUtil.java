package com.ruoyi.storage.datapublish.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;
import org.bson.Document;

import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
public class KafkaProducerUtil {
    private static final String brokerList = "10.33.5.124:9092";
    private static final String articles_data_topic = "articles_data";
    private static final String subject_index_topic = "subject_index_topic";
    public static final String topic_fusion_data = "topic_fusion_data";

    public KafkaProducerUtil() {
    }

    public static Properties initConfig() {
        Properties properties = new Properties();
//        properties.put("bootstrap.servers", "10.33.5.124:9092");
        properties.put("bootstrap.servers", "120.46.58.187:9092");
        properties.put("key.serializer", StringSerializer.class.getName());
        properties.put("value.serializer", StringSerializer.class.getName());
        return properties;
    }

    public static void sendArticlesData(String data) {
        Properties properties = initConfig();
        KafkaProducer<String, String> kafkaProducer = new KafkaProducer(properties);
        ProducerRecord<String, String> producerRecord = new ProducerRecord("articles_data", data);

        try {
            Future<RecordMetadata> future = kafkaProducer.send(producerRecord);
            RecordMetadata recordMetadata = (RecordMetadata)future.get();
            System.out.println("metadata.topic() = " + recordMetadata.topic());
        } catch (Exception var6) {
            var6.printStackTrace();
        }

        kafkaProducer.close();
    }

    public static void sendSubjectIndex(List<Document> documents, String source) {
        String topic = "subject_index_topic";
        if (StringUtils.isNotBlank(source)) {
            topic = "subject_index_topic_" + source;
        }

        Properties properties = initConfig();
        KafkaProducer<String, String> kafkaProducer = new KafkaProducer(properties);
        int i = 0;
        Iterator var6 = documents.iterator();

        while(var6.hasNext()) {
            Document document = (Document)var6.next();
            String string = JSONObject.toJSONString(document);
            ProducerRecord<String, String> producerRecord = new ProducerRecord(topic, string);

            try {
                kafkaProducer.send(producerRecord);
                System.out.println("*******" + i++);
            } catch (Exception var11) {
                var11.printStackTrace();
            }
        }

        kafkaProducer.close();
    }

    public static void sendFusionData(List<Document> documents, String source) {
        String topic = "topic_nstl_data";
        if (StringUtils.isNotBlank(source)) {
            topic = "topic_nstl_data_" + source;
        }

        Properties properties = initConfig();
        KafkaProducer<String, String> kafkaProducer = new KafkaProducer(properties);
        Iterator var6 = documents.iterator();

        while(var6.hasNext()) {
            Document document = (Document)var6.next();
            String string = JSONObject.toJSONString(document);
            ProducerRecord<String, String> producerRecord = new ProducerRecord(topic, string);

            try {
                kafkaProducer.send(producerRecord);
            } catch (Exception var11) {
                var11.printStackTrace();
            }
        }

        kafkaProducer.close();
    }

    public static void main(String[] args) {
        String s = "aaaaa";
        Properties properties = initConfig();
        KafkaProducer<String, String> kafkaProducer = new KafkaProducer(properties);

        for(int i = 0; i < 100; ++i) {
            ProducerRecord<String, String> producerRecord = new ProducerRecord("subject_index_topic", s + i++);

            try {
                Future<RecordMetadata> future = kafkaProducer.send(producerRecord);
                RecordMetadata recordMetadata = (RecordMetadata)future.get();
                System.out.println("metadata.topic() = " + recordMetadata.topic());
            } catch (Exception var8) {
                var8.printStackTrace();
            }
        }

        kafkaProducer.close();
    }

    public static void sendPubmenData(List<Document> documents, String source) {
        String topic = "topic_pubmed_data";
        if (StringUtils.isNotBlank(source)) {
            topic = "topic_pubmed_data_" + source;
        }

        Properties properties = initConfig();
        KafkaProducer<String, String> kafkaProducer = new KafkaProducer(properties);
        Iterator var6 = documents.iterator();

        while(var6.hasNext()) {
            Document document = (Document)var6.next();
            String string = JSONObject.toJSONString(document);
            ProducerRecord<String, String> producerRecord = new ProducerRecord(topic, string);

            try {
                kafkaProducer.send(producerRecord);
            } catch (Exception var11) {
                var11.printStackTrace();
            }
        }

        kafkaProducer.close();
    }

}
