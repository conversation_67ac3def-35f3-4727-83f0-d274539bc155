package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.api.vo.ListSelect;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleCheck;
import com.ruoyi.storage.processingtool.mapper.RuleCheckMapper;
import com.ruoyi.storage.processingtool.service.IRuleCheckService;
import com.ruoyi.storage.processingtool.service.impl.RuleCheckServiceImpl;
import com.ruoyi.storage.processingtool.vo.QueryRuleCheckSelectReqVO;
import com.ruoyi.storage.processingtool.vo.UpdateRuleCheckStatusReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleCheck")
@Validated
public class RuleCheckController extends BaseController<RuleCheckMapper, RuleCheck> {
    RuleCheckController(RuleCheckServiceImpl service) {
        super(service, RuleCheck.class);
    }

    @Autowired
    private IRuleCheckService ruleCheckService;

    /**
     * 根据规则ID查询校验规则
     *
     * @param ruleId 规则ID
     * @return 校验规则列表
     */
    @GetMapping("/getByRuleId/{ruleId}")
    public R<?> getByRuleId(@PathVariable String ruleId) {
        return R.ok(ruleCheckService.getByRuleId(ruleId));
    }

    /**
     * 根据规则验证ID查询校验规则
     *
     * @param ruleValidId 规则验证ID
     * @return 校验规则列表
     */
    @GetMapping("/getByRuleValidId/{ruleValidId}")
    public R<?> getByRuleValidId(@PathVariable String ruleValidId) {
        return R.ok(ruleCheckService.getByRuleValidId(ruleValidId));
    }

    /**
     * 更新处理状态
     *
     * @param id          规则校验ID
     * @param checkStatus 处理状态
     * @return 操作结果
     */
    @PostMapping("/updateCheckStatus")
    public R<?> updateCheckStatus(@RequestParam String id, @RequestParam String checkStatus) {
        ruleCheckService.updateCheckStatus(id, checkStatus);
        return R.ok();
    }

    /**
     * 更新应用状态
     *
     * @param id     规则校验ID
     * @param status 应用状态
     * @return 操作结果
     */
    @PostMapping("/updateStatus")
    public R<?> updateStatus(@RequestParam String id, @RequestParam String status) {
        ruleCheckService.updateStatus(id, status);
        return R.ok();
    }

    /**
     * 更新路径状态
     *
     * @param updateRuleCheckStatusReqVO 更改接口应用状态请求参数
     * @return 操作结果
     */
    @PostMapping("/handleCheckStatus")
    public R<?> handleCheckStatus(@Valid @RequestBody UpdateRuleCheckStatusReqVO updateRuleCheckStatusReqVO) {
        ruleCheckService.handleCheckStatus(updateRuleCheckStatusReqVO);
        return R.ok();
    }

    @GetMapping("/querySelectList")
    public R<List<ListSelect>> querySelectList(QueryRuleCheckSelectReqVO queryRuleCheckSelectReqVO) {
        return R.ok(ruleCheckService.querySelectList(queryRuleCheckSelectReqVO));
    }

}
