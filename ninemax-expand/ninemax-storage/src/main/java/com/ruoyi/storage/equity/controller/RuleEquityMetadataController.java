package com.ruoyi.storage.equity.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.equity.domain.RuleEquityMetadata;
import com.ruoyi.storage.equity.mapper.RuleEquityMetadataMapper;
import com.ruoyi.storage.equity.service.IRuleEquityMetadataService;
import com.ruoyi.storage.equity.service.impl.RuleEquityMetadataServiceImpl;
import com.ruoyi.storage.equity.vo.UpdateEquityMetadataStatusReqVO;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 权益规则元数据Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/ruleEquityMetadata")
@Validated
public class RuleEquityMetadataController extends BaseController<RuleEquityMetadataMapper, RuleEquityMetadata> {

    public RuleEquityMetadataController(RuleEquityMetadataServiceImpl service) {
        super(service, RuleEquityMetadata.class);
    }

    @Autowired
    private IRuleEquityMetadataService ruleEquityMetadataService;

    /**
     * 更新处理状态
     *
     * @param updateEquityMetadataStatusReqVO 更新处理状态请求对象
     * @return 操作结果
     */
    @PostMapping("/updateStatus")
    public R<?> updateEquityMetadataStatus(@Valid @RequestBody UpdateEquityMetadataStatusReqVO updateEquityMetadataStatusReqVO) {
        ruleEquityMetadataService.updateEquityMetadataStatus(updateEquityMetadataStatusReqVO);
        return R.ok();
    }

    /**
     * 删除数据
     *
     * @param ids ID列表
     * @return 操作结果
     */
    @Override
    @Log(title = "通用-删除", businessType = BusinessType.DELETE)
    @DeleteMapping
    public R<?> del(@NotEmpty(message = "ID列表不能为空") @RequestBody List<String> ids) {
        ruleEquityMetadataService.deleteEquityMetadata(ids);
        return R.ok();
    }

}
