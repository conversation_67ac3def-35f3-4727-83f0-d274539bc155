package com.ruoyi.storage.processingtool.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description 数据著录规范化词表详情表新增请求对象
 * <AUTHOR>
 */
@Data
public class AddRuleNormalizationWordDetailReqVO {

    @NotBlank(message = "规范化词id不能为空")
    private String normalizationWordId;

    /**
     * 属性值
     */
    private String attributeValue;

    /**
     * 属性状态
     */
    private String attributeStatus;

    /**
     * 原属性
     */
    private String originalAttribute;

    /**
     * 目标属性
     */
    private String targetAttribute;

    /**
     * 数据类型（0-属性值|1-属性映射）
     */
    private Integer type;

    /**
     * 是否子查询属性值
     */
    private String checkStatus;

}
