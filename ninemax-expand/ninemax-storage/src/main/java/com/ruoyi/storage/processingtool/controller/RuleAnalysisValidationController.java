package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.mapper.RuleAnalysisValidationMapper;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisValidationService;
import com.ruoyi.storage.processingtool.service.impl.RuleAnalysisValidationServiceImpl;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleAnalysisValidation")
@Validated
public class RuleAnalysisValidationController extends BaseController<RuleAnalysisValidationMapper, RuleAnalysisValidation> {
    RuleAnalysisValidationController(RuleAnalysisValidationServiceImpl service) {
        super(service, RuleAnalysisValidation.class);
    }

    @Autowired
    private IRuleAnalysisValidationService ruleAnalysisValidationService;

    /**
     * 解析规则文件效验
     *
     * @param validateRuleReqVO 实体对象
     * @return 操作结果
     */
    @PostMapping("/validateRule")
    public R<?> updateUseStatus(@Valid @RequestBody ValidateRuleReqVO validateRuleReqVO) {
        ruleAnalysisValidationService.validateRule(validateRuleReqVO);
        return R.ok();
    }

}
