package com.ruoyi.storage;

import com.anwen.mongo.annotation.MongoMapperScan;
import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * 仓储业务模块
 *
 * <AUTHOR>
 */
@RefreshScope
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
@MongoMapperScan("com.ruoyi.storage.mongocommon.**.mapper")
public class StorageApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(StorageApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  仓储模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " .-------.       ____     __        \n" +
                " |  _ _   \\      \\   \\   /  /    \n" +
                " | ( ' )  |       \\  _. /  '       \n" +
                " |(_ o _) /        _( )_ .'         \n" +
                " | (_,_).' __  ___(_ o _)'          \n" +
                " |  |\\ \\  |  ||   |(_,_)'         \n" +
                " |  | \\ `'   /|   `-'  /           \n" +
                " |  |  \\    /  \\      /           \n" +
                " ''-'   `'-'    `-..-'              ");
    }
}
