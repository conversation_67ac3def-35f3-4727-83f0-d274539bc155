package com.ruoyi.storage.processingtool.dto;

import com.ruoyi.storage.dataobtainconvert.domain.BatchAnalysisMapping;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HandleParsingMappingToolDTO {

    private RuleBase ruleBase;

    private RuleAnalysisValidation ruleAnalysisValidation;

    private BatchAnalysisMapping batchAnalysisMapping;

    private String message;

    private String result;

}
