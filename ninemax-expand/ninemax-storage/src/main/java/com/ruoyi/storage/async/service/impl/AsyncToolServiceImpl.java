package com.ruoyi.storage.async.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.storage.api.domain.AnalysisMappingRule;
import com.ruoyi.storage.api.domain.FileChecks;
import com.ruoyi.storage.api.domain.ParsingMappingReq;
import com.ruoyi.storage.api.domain.ParsingMappingResp;
import com.ruoyi.storage.api.domain.RuleInfo;
import com.ruoyi.storage.api.service.RemoteExFileService;
import com.ruoyi.storage.async.domain.AsyncTask;
import com.ruoyi.storage.async.service.IAsyncTaskService;
import com.ruoyi.storage.async.service.IAsyncToolService;
import com.ruoyi.storage.async.vo.AsyncTaskCreateReqVO;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.dto.HandleRuleAnalysisToolDTO;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisService;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisValidationService;
import com.ruoyi.storage.processingtool.service.IRuleBaseService;
import com.ruoyi.storage.utils.RestUtils;
import com.ruoyi.storage.utils.ResultConversionUtil;
import cn.hutool.core.util.StrUtil;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 异步工具调用服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncToolServiceImpl implements IAsyncToolService {

    @Autowired
    private IAsyncTaskService asyncTaskService;

    @Autowired
    private IRuleBaseService ruleBaseService;

    @Autowired
    private IRuleAnalysisValidationService ruleAnalysisValidationService;

    @Autowired
    private IRuleAnalysisService ruleAnalysisService;

    @Autowired
    private RestUtils restUtils;

    @Autowired
    private ResultConversionUtil resultConversionUtil;

    @Resource
    private RemoteExFileService remoteExFileService;

    @Value("${storage.tool.analysis_url}")
    private String analysisUrl;

    @Value("${storage.processingTool.tempDir}")
    private String tempDir;

    // 线程池用于异步调用
    private static final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String asyncCallRuleAnalysisTool(HandleRuleAnalysisToolDTO handleAnalysisToolDTO, String callbackUrl) {
        log.info("开始异步调用规则解析工具，回调地址：{}", callbackUrl);

        // 1. 创建异步任务记录
        AsyncTaskCreateReqVO createReqVO = AsyncTaskCreateReqVO.builder()
                .businessType(AsyncTask.BusinessType.RULE_ANALYSIS.getCode())
                .businessId(handleAnalysisToolDTO.getRuleBase().getId())
                .callbackUrl(callbackUrl)
                .requestData(JSON.toJSONString(handleAnalysisToolDTO))
                .timeoutMinutes(30)
                .maxRetryCount(3)
                .build();

        String taskId = asyncTaskService.createAsyncTask(createReqVO);

        // 2. 异步执行工具调用
        executorService.execute(() -> {
            try {
                executeAsyncToolCall(taskId, handleAnalysisToolDTO);
            } catch (Exception e) {
                log.error("异步工具调用执行异常，任务ID：{}", taskId, e);
                asyncTaskService.updateTaskFailure(taskId, "工具调用执行异常：" + e.getMessage());
            }
        });

        log.info("异步任务创建成功，任务ID：{}", taskId);
        return taskId;
    }

    /**
     * 执行异步工具调用
     * 
     * @param taskId 任务ID
     * @param handleAnalysisToolDTO 工具调用参数
     */
    private void executeAsyncToolCall(String taskId, HandleRuleAnalysisToolDTO handleAnalysisToolDTO) {
        List<File> files = Collections.emptyList();
        try {
            log.info("开始执行异步工具调用，任务ID：{}，线程名称：{}", taskId, Thread.currentThread().getName());

            // 1. 准备请求参数
            ParsingMappingReq req = buildParsingMappingReq(handleAnalysisToolDTO, taskId);
            
            // 2. 处理文件
            RuleAnalysisValidation ruleAnalysisValidation = handleAnalysisToolDTO.getRuleAnalysisValidation();
            String filePath = ruleAnalysisValidation.getFilePath();
            String fileName = ruleAnalysisValidation.getFileName();
            files = handleToolRuleFile(filePath, fileName);
            
            if (files.isEmpty()) {
                throw new RuntimeException("规则效验文件处理失败，文件不存在");
            }
            
            req.setFiles(files);

            // 3. 调用工具接口
            log.info("调用工具接口，任务ID：{}", taskId);
            String result = restUtils.analysisAsync(analysisUrl, req, taskId);
            
            if (StringUtils.isBlank(result)) {
                throw new RuntimeException("工具接口调用失败，返回结果为空");
            }

            // 4. 解析返回结果，获取工具方任务ID
            String toolTaskId = parseToolTaskId(result);
            if (StringUtils.isNotBlank(toolTaskId)) {
                // 更新任务状态为处理中
                asyncTaskService.updateTaskProcessing(taskId, toolTaskId);
                log.info("工具接口调用成功，任务ID：{}，工具方任务ID：{}", taskId, toolTaskId);
            } else {
                // 如果没有返回工具方任务ID，说明是同步返回结果
                handleSyncResult(taskId, result, handleAnalysisToolDTO);
            }

        } catch (Exception e) {
            log.error("异步工具调用失败，任务ID：{}", taskId, e);
            asyncTaskService.updateTaskFailure(taskId, "工具调用异常：" + e.getMessage());
            
            // 更新业务状态
            updateBusinessAfterFailure(handleAnalysisToolDTO, e.getMessage());
        } finally {
            // 清理临时文件
            files.forEach(FileUtils::deleteQuietly);
        }
    }

    /**
     * 构建解析映射请求对象
     */
    private ParsingMappingReq buildParsingMappingReq(HandleRuleAnalysisToolDTO handleAnalysisToolDTO, String taskId) {
        RuleBase ruleBase = handleAnalysisToolDTO.getRuleBase();
        
        // 构建回调URL
        String callbackUrl = buildCallbackUrl(taskId);
        
        ParsingMappingReq req = ParsingMappingReq.builder()
                .callbackUrl(callbackUrl)
                .info(RuleInfo.builder()
                        .ruleId(ruleBase.getId())
                        .sourceId(ruleBase.getSourceId())
                        .sourceId("J") // TODO: 临时写期刊
                        .docType(ruleBase.getDocType())
                        .build())
                .checks(new ArrayList<>())
                .rules(new ArrayList<>())
                .build();

        // 添加文件检查信息
        RuleAnalysisValidation validation = handleAnalysisToolDTO.getRuleAnalysisValidation();
        req.getChecks().add(FileChecks.builder()
                .fileName(validation.getFileName())
                .md5(validation.getMd5())
                .build());

        return req;
    }

    /**
     * 构建回调URL
     */
    private String buildCallbackUrl(String taskId) {
        // TODO: 这里需要根据实际部署环境配置回调地址
        return "http://localhost:8080/async/callback/" + taskId;
    }

    /**
     * 处理规则文件
     */
    private List<File> handleToolRuleFile(String filePath, String fileName) {
        File targetFile = null;
        try (Response response = remoteExFileService.resourceDownload(filePath);
             InputStream is = response.body().asInputStream()) {

            String tempFilePath = tempDir + File.separator + StrUtil.uuid() + File.separator + fileName;
            targetFile = new File(tempFilePath);
            FileUtils.copyInputStreamToFile(is, targetFile);
            
            long length = targetFile.length();
            if (length == 0) {
                log.warn("文件下载失败，文件不存在，文件路径：{}", filePath);
                return Collections.emptyList();
            }
            
            return Collections.singletonList(targetFile);
        } catch (Exception e) {
            log.error("处理规则文件失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 解析工具方任务ID
     */
    private String parseToolTaskId(String result) {
        try {
            // 假设工具方返回格式为 {"taskId": "xxx", "status": "processing"}
            // 这里需要根据实际工具方返回格式进行解析
            return JSON.parseObject(result).getString("taskId");
        } catch (Exception e) {
            log.warn("解析工具方任务ID失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 处理同步返回结果
     */
    private void handleSyncResult(String taskId, String result, HandleRuleAnalysisToolDTO handleAnalysisToolDTO) {
        try {
            // 直接处理返回结果
            handleAnalysisToolDTO.setResult(result);
            updateBusinessAfterSuccess(handleAnalysisToolDTO);
            
            // 更新任务状态为成功
            asyncTaskService.updateTaskSuccess(taskId, result);
            
        } catch (Exception e) {
            log.error("处理同步返回结果失败，任务ID：{}", taskId, e);
            asyncTaskService.updateTaskFailure(taskId, "处理返回结果失败：" + e.getMessage());
            updateBusinessAfterFailure(handleAnalysisToolDTO, e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleAsyncCallback(String taskId, String responseData) {
        log.info("处理异步回调，任务ID：{}", taskId);

        try {
            // 1. 更新任务状态
            boolean updated = asyncTaskService.updateTaskSuccess(taskId, responseData);
            if (!updated) {
                log.warn("更新任务状态失败，任务ID：{}", taskId);
                return false;
            }

            // 2. 获取任务信息
            AsyncTask task = asyncTaskService.getById(taskId);
            if (task == null) {
                log.warn("任务不存在，任务ID：{}", taskId);
                return false;
            }

            // 3. 处理业务逻辑
            HandleRuleAnalysisToolDTO handleAnalysisToolDTO = JSON.parseObject(task.getRequestData(), HandleRuleAnalysisToolDTO.class);
            handleAnalysisToolDTO.setResult(responseData);
            
            updateBusinessAfterSuccess(handleAnalysisToolDTO);

            log.info("异步回调处理成功，任务ID：{}", taskId);
            return true;

        } catch (Exception e) {
            log.error("处理异步回调失败，任务ID：{}", taskId, e);
            asyncTaskService.updateTaskFailure(taskId, "回调处理失败：" + e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleAsyncCallbackFailure(String taskId, String errorMessage) {
        log.info("处理异步回调失败，任务ID：{}，错误信息：{}", taskId, errorMessage);

        try {
            // 1. 更新任务状态
            boolean updated = asyncTaskService.updateTaskFailure(taskId, errorMessage);
            if (!updated) {
                log.warn("更新任务失败状态失败，任务ID：{}", taskId);
                return false;
            }

            // 2. 获取任务信息并处理业务逻辑
            AsyncTask task = asyncTaskService.getById(taskId);
            if (task != null) {
                HandleRuleAnalysisToolDTO handleAnalysisToolDTO = JSON.parseObject(task.getRequestData(), HandleRuleAnalysisToolDTO.class);
                updateBusinessAfterFailure(handleAnalysisToolDTO, errorMessage);
            }

            log.info("异步回调失败处理完成，任务ID：{}", taskId);
            return true;

        } catch (Exception e) {
            log.error("处理异步回调失败异常，任务ID：{}", taskId, e);
            return false;
        }
    }

    @Override
    public boolean resendAsyncTask(String taskId) {
        log.info("重新发送异步任务，任务ID：{}", taskId);
        
        try {
            AsyncTask task = asyncTaskService.getById(taskId);
            if (task == null) {
                log.warn("任务不存在，无法重新发送，任务ID：{}", taskId);
                return false;
            }

            // 解析原始请求数据
            HandleRuleAnalysisToolDTO handleAnalysisToolDTO = JSON.parseObject(task.getRequestData(), HandleRuleAnalysisToolDTO.class);
            
            // 重新执行异步调用
            executorService.execute(() -> {
                try {
                    executeAsyncToolCall(taskId, handleAnalysisToolDTO);
                } catch (Exception e) {
                    log.error("重新发送异步任务执行异常，任务ID：{}", taskId, e);
                    asyncTaskService.updateTaskFailure(taskId, "重新发送执行异常：" + e.getMessage());
                }
            });

            return true;

        } catch (Exception e) {
            log.error("重新发送异步任务失败，任务ID：{}", taskId, e);
            return false;
        }
    }

    /**
     * 工具调用成功后更新业务状态
     */
    private void updateBusinessAfterSuccess(HandleRuleAnalysisToolDTO handleAnalysisToolDTO) {
        try {
            RuleBase ruleBase = handleAnalysisToolDTO.getRuleBase();
            RuleAnalysisValidation ruleAnalysisValidation = handleAnalysisToolDTO.getRuleAnalysisValidation();

            // 更新规则状态
            if (handleAnalysisToolDTO.isInitFlag()) {
                ruleBase.setRuleStatus("COMPLETED");
                ruleBase.setToolStatus("INIT_SUCCESS");
                ruleBaseService.updateById(ruleBase);
            }

            // 更新验证状态
            ruleAnalysisValidation.setStatus("SUCCESS");
            ruleAnalysisValidation.setDescription("工具调用成功");
            ruleAnalysisValidationService.updateById(ruleAnalysisValidation);

            // 处理返回数据
            String result = handleAnalysisToolDTO.getResult();
            ParsingMappingResp resp = resultConversionUtil.convertToRespDTO(result);
            if (resp != null) {
                ruleAnalysisService.handleResultData(resp, ruleBase.getId(), ruleBase.getName(), ruleAnalysisValidation.getId());
            }

        } catch (Exception e) {
            log.error("更新业务成功状态失败", e);
            throw new RuntimeException("更新业务状态失败：" + e.getMessage());
        }
    }

    /**
     * 工具调用失败后更新业务状态
     */
    private void updateBusinessAfterFailure(HandleRuleAnalysisToolDTO handleAnalysisToolDTO, String errorMessage) {
        try {
            RuleBase ruleBase = handleAnalysisToolDTO.getRuleBase();
            RuleAnalysisValidation ruleAnalysisValidation = handleAnalysisToolDTO.getRuleAnalysisValidation();

            // 更新规则状态
            if (handleAnalysisToolDTO.isInitFlag()) {
                ruleBase.setRuleStatus("COMPLETED");
                ruleBase.setToolStatus("INIT_FAIL");
                ruleBaseService.updateById(ruleBase);
            }

            // 更新验证状态
            ruleAnalysisValidation.setStatus("FAILURE");
            ruleAnalysisValidation.setDescription(errorMessage);
            ruleAnalysisValidationService.updateById(ruleAnalysisValidation);

        } catch (Exception e) {
            log.error("更新业务失败状态失败", e);
        }
    }
}
