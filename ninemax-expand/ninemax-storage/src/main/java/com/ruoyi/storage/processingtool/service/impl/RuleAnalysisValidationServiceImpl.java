package com.ruoyi.storage.processingtool.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.storage.api.service.AnalysisService;
import com.ruoyi.storage.api.vo.ExFileVO;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.dto.HandleRuleAnalysisToolDTO;
import com.ruoyi.storage.processingtool.mapper.RuleAnalysisValidationMapper;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisValidationService;
import com.ruoyi.storage.processingtool.service.IRuleBaseService;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;
import com.ruoyi.storage.utils.QueryCommon;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RuleAnalysisValidationServiceImpl extends BaseServiceImpl<RuleAnalysisValidationMapper, RuleAnalysisValidation> implements IRuleAnalysisValidationService {

    @Resource
    private IRuleBaseService ruleBaseService;
    @Resource
    @Lazy
    private AnalysisService analysisService;

    // 在类中添加线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @Override
    public IPage<RuleAnalysisValidation> queryList(RuleAnalysisValidation ruleAnalysisValidation, Page<RuleAnalysisValidation> page) {

        QueryWrapper<RuleAnalysisValidation> queryWrapper = QueryCommon.buildQueryWrapper(ruleAnalysisValidation);
        String searchTime = ruleAnalysisValidation.getSearchCreatTime();
        if (StringUtils.isNotEmpty(searchTime)) {
            String[] split = searchTime.split(",");
            queryWrapper.lambda().between(RuleAnalysisValidation::getCreateTime, split[0], split[1]);
        }
        Page<RuleAnalysisValidation> resultPage = this.page(page, queryWrapper);
        resultPage.convert(item -> {
            RuleBase ruleBase = ruleBaseService.getById(item.getRuleId());
            item.setSourceId(ruleBase.getSourceId());
            item.setDocType(ruleBase.getDocType());
            return item;});
        return resultPage;
    }

    @Override
    public void validateRule(ValidateRuleReqVO validateRuleReqVO) {
        System.out.println(validateRuleReqVO);
        // 保存数据
        List<ExFileVO> fileList = validateRuleReqVO.getFileList();
        Assert.notEmpty(fileList, "样例文件不能为空！");
        ExFileVO exFileVO = fileList.get(0);
        String ruleId = validateRuleReqVO.getRuleId();
        RuleBase ruleBase = ruleBaseService.getById(ruleId);
        RuleAnalysisValidation ruleAnalysisValidation = RuleAnalysisValidation.builder()
                .ruleId(ruleId)
                .name(ruleBase.getName())
                .fileName(exFileVO.getName())
                .filePath(exFileVO.getUrl())
                .md5(exFileVO.getMd5())
                .fileSize(exFileVO.getSize())
                .type(1)
                .build();
        this.save(ruleAnalysisValidation);

        // todo 后期提取到统一的接口调用部分
        // 3、根据规则文件进行工具接口调用 ==> 异步调用
        log.info("解析映射规则数据：{} | 规则文件解析映射效验数据:{}", ruleBase, ruleAnalysisValidation);

        // 使用线程池异步执行
        executorService.execute(() -> analysisService.toolInterfaceCall(HandleRuleAnalysisToolDTO.builder().ruleBase(ruleBase).ruleAnalysisValidation(ruleAnalysisValidation).initFlag(false).build()));
    }
}
