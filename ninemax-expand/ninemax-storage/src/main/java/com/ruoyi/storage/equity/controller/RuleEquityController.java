package com.ruoyi.storage.equity.controller;

import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.equity.domain.RuleEquity;
import com.ruoyi.storage.equity.mapper.RuleEquityMapper;
import com.ruoyi.storage.equity.service.impl.RuleEquityServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 规则权益对象Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/ruleEquity")
@Validated
public class RuleEquityController extends BaseController<RuleEquityMapper, RuleEquity> {

    public RuleEquityController(RuleEquityServiceImpl service) {
        super(service, RuleEquity.class);
    }
}
