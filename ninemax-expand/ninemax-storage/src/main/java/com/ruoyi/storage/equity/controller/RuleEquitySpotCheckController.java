package com.ruoyi.storage.equity.controller;

import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.equity.domain.RuleEquitySpotCheck;
import com.ruoyi.storage.equity.mapper.RuleEquitySpotCheckMapper;
import com.ruoyi.storage.equity.service.impl.RuleEquitySpotCheckServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权益抽检管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/ruleEquitySpotCheck")
@Validated
public class RuleEquitySpotCheckController extends BaseController<RuleEquitySpotCheckMapper, RuleEquitySpotCheck> {
    
    public RuleEquitySpotCheckController(RuleEquitySpotCheckServiceImpl service) {
        super(service, RuleEquitySpotCheck.class);
    }
}
