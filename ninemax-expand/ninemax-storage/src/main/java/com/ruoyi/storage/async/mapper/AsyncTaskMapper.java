package com.ruoyi.storage.async.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.storage.async.domain.AsyncTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * 异步任务Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface AsyncTaskMapper extends BaseMapper<AsyncTask> {

    /**
     * 根据任务ID查询任务信息
     * 
     * @param taskId 任务ID
     * @return 异步任务
     */
    @Select("SELECT * FROM async_task WHERE task_id = #{taskId}")
    AsyncTask selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据工具方任务ID查询任务信息
     * 
     * @param toolTaskId 工具方任务ID
     * @return 异步任务
     */
    @Select("SELECT * FROM async_task WHERE tool_task_id = #{toolTaskId}")
    AsyncTask selectByToolTaskId(@Param("toolTaskId") String toolTaskId);

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 任务状态
     * @param endTime 结束时间
     * @return 更新行数
     */
    @Update("UPDATE async_task SET task_status = #{status}, end_time = #{endTime}, update_time = NOW() WHERE task_id = #{taskId}")
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") String status, @Param("endTime") Date endTime);

    /**
     * 更新任务响应数据
     * 
     * @param taskId 任务ID
     * @param responseData 响应数据
     * @param status 任务状态
     * @param endTime 结束时间
     * @return 更新行数
     */
    @Update("UPDATE async_task SET response_data = #{responseData}, task_status = #{status}, end_time = #{endTime}, update_time = NOW() WHERE task_id = #{taskId}")
    int updateTaskResponse(@Param("taskId") String taskId, @Param("responseData") String responseData, 
                          @Param("status") String status, @Param("endTime") Date endTime);

    /**
     * 更新任务错误信息
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @param status 任务状态
     * @param endTime 结束时间
     * @return 更新行数
     */
    @Update("UPDATE async_task SET error_message = #{errorMessage}, task_status = #{status}, end_time = #{endTime}, update_time = NOW() WHERE task_id = #{taskId}")
    int updateTaskError(@Param("taskId") String taskId, @Param("errorMessage") String errorMessage, 
                       @Param("status") String status, @Param("endTime") Date endTime);

    /**
     * 更新工具方任务ID
     * 
     * @param taskId 任务ID
     * @param toolTaskId 工具方任务ID
     * @param status 任务状态
     * @return 更新行数
     */
    @Update("UPDATE async_task SET tool_task_id = #{toolTaskId}, task_status = #{status}, update_time = NOW() WHERE task_id = #{taskId}")
    int updateToolTaskId(@Param("taskId") String taskId, @Param("toolTaskId") String toolTaskId, @Param("status") String status);

    /**
     * 增加重试次数
     * 
     * @param taskId 任务ID
     * @return 更新行数
     */
    @Update("UPDATE async_task SET retry_count = retry_count + 1, update_time = NOW() WHERE task_id = #{taskId}")
    int incrementRetryCount(@Param("taskId") String taskId);

    /**
     * 查询超时的任务
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时任务列表
     */
    @Select("SELECT * FROM async_task WHERE task_status IN ('PENDING', 'PROCESSING') " +
            "AND TIMESTAMPDIFF(MINUTE, start_time, NOW()) > timeout_minutes")
    List<AsyncTask> selectTimeoutTasks();

    /**
     * 查询需要重试的任务
     * 
     * @return 需要重试的任务列表
     */
    @Select("SELECT * FROM async_task WHERE task_status = 'FAILURE' " +
            "AND retry_count < max_retry_count " +
            "AND TIMESTAMPDIFF(MINUTE, update_time, NOW()) > 5")
    List<AsyncTask> selectRetryTasks();
}
