package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.api.vo.ListSelect;
import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleCheck;
import com.ruoyi.storage.processingtool.vo.QueryRuleCheckSelectReqVO;
import com.ruoyi.storage.processingtool.vo.UpdateRuleCheckStatusReqVO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IRuleCheckService extends IBaseService<RuleCheck> {

    /**
     * 根据规则ID查询校验规则
     *
     * @param ruleId 规则ID
     * @return 校验规则列表
     */
    java.util.List<RuleCheck> getByRuleId(String ruleId);

    /**
     * 根据规则验证ID查询校验规则
     *
     * @param ruleValidId 规则验证ID
     * @return 校验规则列表
     */
    java.util.List<RuleCheck> getByRuleValidId(String ruleValidId);

    /**
     * 更新处理状态
     *
     * @param id          规则校验ID
     * @param checkStatus 处理状态
     */
    void updateCheckStatus(String id, String checkStatus);

    /**
     * 更新应用状态
     *
     * @param id     规则校验ID
     * @param status 应用状态
     */
    void updateStatus(String id, String status);

    /**
     * 处理处理状态
     *
     * @param updateRuleCheckStatusReqVO 处理状态请求参数
     */
    void handleCheckStatus(@Valid UpdateRuleCheckStatusReqVO updateRuleCheckStatusReqVO);

    /**
     * 查询规则校验列表
     *
     * @param queryRuleCheckSelectReqVO 查询规则校验列表请求参数
     * @return 规则校验列表
     */
    List<ListSelect> querySelectList(QueryRuleCheckSelectReqVO queryRuleCheckSelectReqVO);
}
