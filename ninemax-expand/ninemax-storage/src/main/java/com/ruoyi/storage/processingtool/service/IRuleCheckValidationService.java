package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleCheckValidation;
import com.ruoyi.storage.processingtool.vo.ValidateRuleReqVO;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface IRuleCheckValidationService extends IBaseService<RuleCheckValidation> {

    /**
     * 验证规则
     *
     * @param validateRuleReqVO 实体对象
     */
    void validateRule(@Valid ValidateRuleReqVO validateRuleReqVO);

}
