package com.ruoyi.storage.searchbase.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.api.vo.TreeSelect;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.searchbase.domain.DataImi;
import com.ruoyi.storage.searchbase.mapper.DataImiMapper;
import com.ruoyi.storage.searchbase.service.IDataImiService;
import com.ruoyi.storage.searchbase.service.impl.DataImiServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataImi")
@Validated
public class DataImiController extends BaseController<DataImiMapper, DataImi> {
    DataImiController(DataImiServiceImpl service) {
        super(service, DataImi.class);
    }

    @Resource
    private IDataImiService dataImiService;

    /**
     * 获取IMI映射数据的树结构
     *
     * @return 树结构数据
     */
    @GetMapping("/queryTreeSelectList")
    public R<List<TreeSelect>> treeList() {
        // 后期可完善检索参数信息
        return R.ok(dataImiService.queryTreeSelectList());
    }

}
