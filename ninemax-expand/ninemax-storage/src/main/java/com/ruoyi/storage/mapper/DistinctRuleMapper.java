package com.ruoyi.storage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.storage.domain.DistinctRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 查重规则Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DistinctRuleMapper extends BaseMapper<DistinctRule> {
    /**
     * 获取应用的查重规则
     * 
     * @param taskId 任务ID
     * @return 查重规则
     */
    DistinctRule getAppliedRule(@Param("taskId") Long taskId);
}




