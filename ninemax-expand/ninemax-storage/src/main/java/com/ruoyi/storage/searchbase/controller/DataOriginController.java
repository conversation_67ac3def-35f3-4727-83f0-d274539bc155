package com.ruoyi.storage.searchbase.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.api.vo.ListSelect;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.searchbase.domain.DataOrigin;
import com.ruoyi.storage.searchbase.mapper.DataOriginMapper;
import com.ruoyi.storage.searchbase.service.IDataOriginService;
import com.ruoyi.storage.searchbase.service.impl.DataOriginServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataOrigin")
@Validated
public class DataOriginController extends BaseController<DataOriginMapper, DataOrigin> {
    DataOriginController(DataOriginServiceImpl service) {
        super(service, DataOrigin.class);
    }

    @Resource
    private IDataOriginService dataOriginService;

    @GetMapping("/querySelectList")
    public R<List<ListSelect>> querySelectList() {
        return R.ok(dataOriginService.querySelectList());
    }

}
