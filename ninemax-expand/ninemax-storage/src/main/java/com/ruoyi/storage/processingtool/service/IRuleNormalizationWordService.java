package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationWord;
import com.ruoyi.storage.processingtool.vo.AddNormalWordReqVO;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface IRuleNormalizationWordService extends IBaseService<RuleNormalizationWord> {

    /**
     * 新增词表信息
     *
     * @param addNormalWordReqVO 新增参数
     */
    void addNormalWord(@Valid AddNormalWordReqVO addNormalWordReqVO);

    /**
     * 替换词表信息
     *
     * @param addNormalWordReqVO 新增参数
     */
    void replaceFile(@Valid AddNormalWordReqVO addNormalWordReqVO);
}
