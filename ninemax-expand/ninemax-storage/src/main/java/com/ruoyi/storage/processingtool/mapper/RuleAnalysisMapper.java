package com.ruoyi.storage.processingtool.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.storage.processingtool.domain.RuleAnalysis;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RuleAnalysisMapper extends BaseMapper<RuleAnalysis> {

    /**
     * 根据分析规则id查询规则分析列表
     *
     * @param analysisRuleId 分析规则id
     * @return
     */
    default List<RuleAnalysis> queryRuleAnalysisListByAnalysisRuleId(String analysisRuleId, String analysisValidationId) {
        LambdaQueryWrapper<RuleAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleAnalysis::getRuleId, analysisRuleId);
        queryWrapper.eq(RuleAnalysis::getRuleValidId, analysisValidationId);
        queryWrapper.orderByAsc(RuleAnalysis::getSort);
        return this.selectList(queryWrapper);
    }

    ;
}
