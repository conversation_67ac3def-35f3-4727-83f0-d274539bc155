package com.ruoyi.storage.processingtool.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 数据著录规范化规则详情表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "rule_normalization")
public class RuleNormalization extends BaseEntity3 {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 路径ID
     */
    private String pathId;

    /**
     * 元素路径
     */
    @Query(type = QueryType.LIKE)
    private String path;


    /**
     * 规则ID
     */
    @Query(type = QueryType.EQ)
    private String ruleId;

    /**
     * 元素集
     */
    @Query(type = QueryType.EQ)
    private String elementSet;

    /**
     * 元素名称
     */
    @Query(type = QueryType.LIKE)
    private String elementName;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 属性值
     */
    private String attributeValue;

    /**
     * 属性值数量
     */
    private String attributeCount;

}
