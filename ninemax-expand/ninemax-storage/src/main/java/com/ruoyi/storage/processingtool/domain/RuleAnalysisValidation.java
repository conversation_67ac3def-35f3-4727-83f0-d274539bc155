package com.ruoyi.storage.processingtool.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.entity.BaseEntity3;
import com.ruoyi.storage.enums.QueryType;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 解析规则验证表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "rule_analysis_validation")
public class RuleAnalysisValidation extends BaseEntity3 {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 名称
     */
    @Query(type = QueryType.LIKE)
    private String name;

    /**
     * 规则ID
     */
    @Query(type = QueryType.EQ)
    private String ruleId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private String fileSize;

    /**
     * 验证文件路径
     */
    private String filePath;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    @Query(type = QueryType.EQ)
    private String status;

    /**
     * 失败说明
     */
    private String description;

    /**
     * 是否模板规则文件 0-是 1-否
     */
    private Integer type;
    /**
     * md5值
     */
    private String md5;

    // ========================== 检索信息 ==========================

    @TableField(exist = false)
    private String sourceId;

    @TableField(exist = false)
    private String docType;

    /**
     * 时间检索条件信息
     */
    @TableField(exist = false)
    private String searchCreatTime;
}
