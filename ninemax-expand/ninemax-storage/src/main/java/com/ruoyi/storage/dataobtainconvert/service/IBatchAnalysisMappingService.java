package com.ruoyi.storage.dataobtainconvert.service;

import com.ruoyi.storage.api.domain.ParsingMappingResp;
import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.dataobtainconvert.domain.BatchAnalysisMapping;
import com.ruoyi.storage.dataobtainconvert.vo.ReprocessReqVO;
import com.ruoyi.storage.dataobtainconvert.vo.UpdateAuditStatusReqVO;

import javax.validation.Valid;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public interface IBatchAnalysisMappingService extends IBaseService<BatchAnalysisMapping> {

    /**
     * 重新处理
     *
     * @param reprocessReqVO 批次数据信息对象
     * @return
     */
    boolean reprocess(@Valid ReprocessReqVO reprocessReqVO);

    /**
     * 更新审核状态
     *
     * @param updateAuditStatusReqVO 批次数据信息对象
     */
    void updateAuditStatus(@Valid UpdateAuditStatusReqVO updateAuditStatusReqVO);

    /**
     * 处理批次数据信息
     *
     * @param batchAnalysisMapping 批次数据信息对象
     * @param resp 批次数据信息对象
     */
    void handleBatchAnalysisMappingResultData(BatchAnalysisMapping batchAnalysisMapping, ParsingMappingResp resp) throws IOException;
}
