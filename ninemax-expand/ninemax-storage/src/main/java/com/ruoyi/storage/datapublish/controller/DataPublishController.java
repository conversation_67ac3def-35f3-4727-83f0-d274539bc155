package com.ruoyi.storage.datapublish.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.datapublish.domain.DataPublish;
import com.ruoyi.storage.datapublish.mapper.DataPublishMapper;
import com.ruoyi.storage.datapublish.service.IDataPublishService;
import com.ruoyi.storage.datapublish.service.impl.DataPublishServiceImpl;
import com.ruoyi.storage.datapublish.vo.StartPublishDataReqVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataPublish")
@Validated
public class DataPublishController extends BaseController<DataPublishMapper, DataPublish> {
    DataPublishController(DataPublishServiceImpl service) {
        super(service, DataPublish.class);
    }

    @Resource
    private IDataPublishService dataPublishService;

    /**
     * 系统发布文献数据信息 ==> (单源pubmed、nstlzjg、标注的数据)
     *
     * @param startPublishDataReqVO 系统发布文献数据信息请求对象
     * @return 操作结果
     */
    @PostMapping("/startPublishData")
    public R<?> startPublishData(@Valid @RequestBody StartPublishDataReqVO startPublishDataReqVO) {
        return dataPublishService.startPublishData(startPublishDataReqVO);
    }

    /**
     * 系统发布文献数据信息 ==> (单源pubmed、nstlzjg、标注的数据)
     *
     * @param startPublishDataReqVO 系统发布文献数据信息请求对象
     * @return 操作结果
     */
    @PostMapping("/startPublishPubmedData")
    public R<?> publishPubmedData(@Valid @RequestBody StartPublishDataReqVO startPublishDataReqVO) {
        return dataPublishService.publishPubmedData(startPublishDataReqVO);
    }

    /**
     * 系统发布文献数据信息 ==> (单源pubmed、nstlzjg、标注的数据)
     *
     * @param startPublishDataReqVO 系统发布文献数据信息请求对象
     * @return 操作结果
     */
    @PostMapping("/publishPubmedData")
    public R<?> pubmedData(@Valid @RequestBody StartPublishDataReqVO startPublishDataReqVO) {
        return dataPublishService.pubmedData(startPublishDataReqVO);
    }

    /**
     * 系统发布文献数据信息 ==> (单源pubmed、nstlzjg、标注的数据)
     *
     * @param startPublishDataReqVO 系统发布文献数据信息请求对象
     * @return 操作结果
     */
    @PostMapping("/startPublishNstlData")
    public R<?> publishNstlData(@Valid @RequestBody StartPublishDataReqVO startPublishDataReqVO) {
        return dataPublishService.publishNstlData(startPublishDataReqVO);
    }

    /**
     * 系统发布文献数据信息 ==> (单源pubmed、nstlzjg、标注的数据)
     *
     * @param startPublishDataReqVO 系统发布文献数据信息请求对象
     * @return 操作结果
     */
    @PostMapping("/publishAnnotationData")
    public R<?> publishAnnotationData(@Valid @RequestBody StartPublishDataReqVO startPublishDataReqVO) {
        return dataPublishService.publishAnnotationData(startPublishDataReqVO);
    }

}
