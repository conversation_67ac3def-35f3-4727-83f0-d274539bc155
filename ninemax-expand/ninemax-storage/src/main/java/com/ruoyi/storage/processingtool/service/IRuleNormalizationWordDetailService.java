package com.ruoyi.storage.processingtool.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.processingtool.domain.RuleNormalizationWordDetail;
import com.ruoyi.storage.processingtool.vo.AddRuleNormalizationWordDetailReqVO;
import com.ruoyi.storage.processingtool.vo.DeleteRuleNormalizationWordDetailReqVO;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface IRuleNormalizationWordDetailService extends IBaseService<RuleNormalizationWordDetail> {

    /**
     * 新增
     *
     * @param addRuleNormalizationWordDetailReqVO 新增参数
     * @return
     */
    boolean addRuleNormalizationWordDetail(@Valid AddRuleNormalizationWordDetailReqVO addRuleNormalizationWordDetailReqVO);

    /**
     * 删除
     *
     * @param deleteRuleNormalizationWordDetailReqVO 删除参数
     * @return
     */
    boolean deleteRuleNormalizationWordDetail(DeleteRuleNormalizationWordDetailReqVO deleteRuleNormalizationWordDetailReqVO);
}
