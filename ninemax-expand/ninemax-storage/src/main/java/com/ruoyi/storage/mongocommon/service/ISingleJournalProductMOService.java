package com.ruoyi.storage.mongocommon.service;

import com.anwen.mongo.service.IService;
import com.ruoyi.storage.mongocommon.domain.SingleArticlePrepareMO;
import com.ruoyi.storage.mongocommon.domain.SingleArticleProductMO;
import com.ruoyi.storage.mongocommon.domain.SingleJournalProductMO;

/**
 * <AUTHOR>
 */
public interface ISingleJournalProductMOService extends IService<SingleJournalProductMO> {


    String saveByArticle(SingleJournalProductMO singleJournalProductMO);
}
