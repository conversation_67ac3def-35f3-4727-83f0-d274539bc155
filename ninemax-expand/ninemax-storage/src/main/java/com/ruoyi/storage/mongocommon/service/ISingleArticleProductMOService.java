package com.ruoyi.storage.mongocommon.service;

import com.anwen.mongo.service.IService;
import com.ruoyi.storage.mongocommon.domain.SingleArticlePrepareMO;
import com.ruoyi.storage.mongocommon.domain.SingleArticleProductMO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISingleArticleProductMOService extends IService<SingleArticleProductMO> {


    void saveByPrepare(SingleArticlePrepareMO mo);
}
