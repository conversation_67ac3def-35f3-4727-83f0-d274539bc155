package com.ruoyi.storage.mongocommon.service;

import com.anwen.mongo.model.PageParam;
import com.anwen.mongo.model.PageResult;
import com.anwen.mongo.service.IService;
import com.ruoyi.storage.mongocommon.vo.PublishReqVO;
import org.bson.Document;

/**
 * <AUTHOR>
 */
public interface IMongoDBService extends IService<Document> {

    PageResult<Document> queryList(String collectionName, PublishReqVO vo, PageParam pageParam);

    public Document queryById(String collectionName, String id);
}
