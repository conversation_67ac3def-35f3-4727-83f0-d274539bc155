package com.ruoyi.storage.mongocommon.controller;

import com.anwen.mongo.model.PageResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.mongocommon.domain.FusionArticleProductMO;
import com.ruoyi.storage.mongocommon.domain.FusionIssueProductMO;
import com.ruoyi.storage.mongocommon.service.IFusionArticleProductMOService;
import com.ruoyi.storage.mongocommon.service.IFusionIssueProductMOService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fusionArticleProduct")
public class FusionArticleProductMOController {

    @Resource
    private IFusionArticleProductMOService fusionArticleProductMOService;

    @GetMapping("/list")
    public R<?> list(
            FusionArticleProductMO funsionArticleProductMO,
            @RequestParam(name = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        PageResult<FusionArticleProductMO> page = fusionArticleProductMOService.page(pageNum, pageSize);
        return R.ok(page);
    }

    @GetMapping("/queryFusionArticleProduct")
    public R<?> queryFusionArticleProduct(String fusionArticleId){
        return R.ok(fusionArticleProductMOService.queryFusionArticleProductByArticleId(fusionArticleId));
    }

    public R<?> queryFusionArticleProductByIds(String fusionArticleIds){
        return R.ok(fusionArticleProductMOService.queryFusionArticleProductByArticleIds(Arrays.asList(fusionArticleIds.split(","))));
    }
}
