package com.ruoyi.storage.equity.service;

import com.ruoyi.storage.base.service.IBaseService;
import com.ruoyi.storage.equity.domain.RuleEquityMetadata;
import com.ruoyi.storage.equity.vo.UpdateEquityMetadataStatusReqVO;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 权益规则元数据Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IRuleEquityMetadataService extends IBaseService<RuleEquityMetadata> {

    /**
     * 修改权益规则元数据状态
     *
     * @param updateEquityMetadataStatusReqVO 更新权益规则元数据状态请求对象
     */
    void updateEquityMetadataStatus(@Valid UpdateEquityMetadataStatusReqVO updateEquityMetadataStatusReqVO);

    /**
     * 删除权益规则元数据
     *
     * @param ids 删除权益规则元数据ID列表
     */
    void deleteEquityMetadata(@NotEmpty(message = "ID列表不能为空") List<String> ids);
}
