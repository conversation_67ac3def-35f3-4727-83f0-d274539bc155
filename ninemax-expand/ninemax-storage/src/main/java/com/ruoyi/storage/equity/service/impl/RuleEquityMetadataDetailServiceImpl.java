package com.ruoyi.storage.equity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.equity.domain.RuleEquityMetadataDetail;
import com.ruoyi.storage.equity.mapper.RuleEquityMetadataDetailMapper;
import com.ruoyi.storage.equity.service.IRuleEquityMetadataDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 权益规则元数据详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
@Slf4j
public class RuleEquityMetadataDetailServiceImpl extends BaseServiceImpl<RuleEquityMetadataDetailMapper, RuleEquityMetadataDetail> implements IRuleEquityMetadataDetailService {

    /**
     * 查询字段值
     *
     * @param metadataId 元数据id
     * @return
     */
    @Override
    public List<RuleEquityMetadataDetail> getFieldValues(String metadataId) {
        Assert.isFalse(StringUtils.isBlank(metadataId), "查询字段值失败 ==> metadataId不能为空");

        LambdaQueryWrapper<RuleEquityMetadataDetail> queryWrapper = new LambdaQueryWrapper<RuleEquityMetadataDetail>()
                .eq(RuleEquityMetadataDetail::getEquityMetadataId, metadataId)
                .orderByAsc(RuleEquityMetadataDetail::getSort);
        return this.list(queryWrapper);
    }
}
