package com.ruoyi.storage.datapublish.domain;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName Basic 原始数据库
 * 在进行存储时不使用实体类方式 ==> 原始数据存储到 record 字段中
 * 所以使用Map<String, Object> 进行存储 ==> baseMapper
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@CollectionName("nstl_article_final")
public class NstlArticleFinalMO {

    @ID
    @CollectionField("_id")
    private String _id;

    /**
     * 批次ID
     */
    @CollectionField("id")
    private String id;

    /**
     * 批次ID
     */
    @CollectionField("GUID")
    private String guid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 原始文件解析数据内容
     */
    @CollectionField("record")
    private Object record;


}
