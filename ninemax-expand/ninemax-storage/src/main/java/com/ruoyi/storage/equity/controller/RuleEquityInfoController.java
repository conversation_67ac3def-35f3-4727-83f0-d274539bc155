package com.ruoyi.storage.equity.controller;

import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.equity.domain.RuleEquityInfo;
import com.ruoyi.storage.equity.mapper.RuleEquityInfoMapper;
import com.ruoyi.storage.equity.service.impl.RuleEquityInfoServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权益信息管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/ruleEquityInfo")
@Validated
public class RuleEquityInfoController extends BaseController<RuleEquityInfoMapper, RuleEquityInfo> {

    public RuleEquityInfoController(RuleEquityInfoServiceImpl service) {
        super(service, RuleEquityInfo.class);
    }
}
