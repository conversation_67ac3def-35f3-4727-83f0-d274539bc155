# Tomcat
server:
  port: 9207

# Spring
spring:
  application:
    # 应用名称
    name: storage
  profiles:
    # 环境配置
    active: @profile.name@
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server.addr@
        namespace: @config.namespace@
      config:
        # 配置中心地址
        server-addr: @nacos.server.addr@
        namespace: @config.namespace@

        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - ${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
