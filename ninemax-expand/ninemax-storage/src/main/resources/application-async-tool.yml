# 异步工具平台对接配置
app:
  # 回调配置
  callback:
    # 回调服务基础URL
    base-url: http://localhost:9207
    # 回调超时时间（秒）
    timeout: 30
  
  # 工具平台配置
  tool-platform:
    # 默认超时时间（分钟）
    default-timeout: 60
    # 最大重试次数
    max-retry-count: 3
    # 连接超时时间（毫秒）
    connect-timeout: 30000
    # 读取超时时间（毫秒）
    read-timeout: 60000
  
  # 文件处理配置
  file:
    # 支持的文件类型
    supported-types:
      - zip
      - rar
      - tar
      - gz
      - 7z
    # 最大文件大小（MB）
    max-size: 500
    # 临时文件保存天数
    temp-file-days: 7
  
  # 任务配置
  task:
    # 任务状态检查间隔（分钟）
    status-check-interval: 5
    # 任务数据保留天数
    data-retention-days: 30
    # 并发处理数量
    concurrent-limit: 10

# 定时任务配置
scheduling:
  # 是否启用定时任务
  enabled: true
  # 线程池大小
  pool-size: 5

# 日志配置
logging:
  level:
    com.ruoyi.storage.service.impl.AsyncToolTaskServiceImpl: INFO
    com.ruoyi.storage.service.impl.AsyncToolResultServiceImpl: INFO
    com.ruoyi.storage.controller.AsyncToolCallbackController: INFO
    com.ruoyi.storage.utils.RestUtils: INFO
