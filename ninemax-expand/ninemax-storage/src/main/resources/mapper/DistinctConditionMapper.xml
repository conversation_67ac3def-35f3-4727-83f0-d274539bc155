<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.storage.mapper.DistinctConditionMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.storage.domain.DistinctCondition">
            <id property="id" column="id" />
            <result property="ruleId" column="rule_id" />
            <result property="fieldName" column="field_name" />
            <result property="fieldType" column="field_type" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,rule_id,field_name,field_type,create_time,update_time
    </sql>
</mapper>
