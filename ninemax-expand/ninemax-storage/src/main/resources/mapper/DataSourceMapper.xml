<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.storage.mapper.DataSourceMapper">

    <resultMap type="com.ruoyi.storage.api.model.DataSource" id="DataSourceResult">
        <result property="id"    column="id"    />
        <result property="dataId"    column="data_id"    />
        <result property="name"    column="name"    />
        <result property="dataType"    column="data_type"    />
        <result property="sourceType"    column="source_type"    />
        <result property="fileType"    column="file_type"    />
        <result property="harvestType"    column="harvest_type"    />
        <result property="executeCycle"    column="execute_cycle"    />
        <result property="executeDay"    column="execute_day"    />
        <result property="executeDate"    column="execute_date"    />
        <result property="state"    column="state"    />
        <result property="analysisRuleId"    column="analysis_rule_id"    />
        <result property="validRuleId"    column="valid_rule_id"    />
        <result property="matchRuleId"    column="match_rule_id"    />
        <result property="lastState"    column="last_state"    />
        <result property="totalBatch"    column="total_batch"    />
        <result property="errorReason"    column="error_reason"    />
        <result property="executeTime"    column="execute_time"    />
        <result property="ifaceName"    column="iface_name"    />
        <result property="describes"    column="describes"    />
        <result property="ifaceUrl"    column="iface_url"    />
        <result property="ifaceParams"    column="iface_params"    />
        <result property="ftpUrl"    column="ftp_url"    />
        <result property="ftpPort"    column="ftp_port"    />
        <result property="ftpUser"    column="ftp_user"    />
        <result property="ftpPwd"    column="ftp_pwd"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="operUser"    column="oper_user"    />
        <result property="jobId"    column="job_id"    />
    </resultMap>

    <sql id="selectDataSourceVo">
        select id, data_id, name, data_type,source_type, harvest_type, execute_cycle, state, analysis_rule_id, valid_rule_id, match_rule_id, last_state, total_batch, error_reason, execute_time, iface_name, describes, iface_url, iface_params, ftp_url,ftp_port, ftp_user, ftp_pwd, is_delete, create_time, update_time, oper_user, job_id, file_type from data_source
    </sql>

    <select id="selectDataSourceList" parameterType="com.ruoyi.storage.api.model.DataSource" resultMap="DataSourceResult">
        <include refid="selectDataSourceVo"/>
        <where>
            <if test="dataId != null  and dataId != ''"> and data_id = #{dataId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="sourceType != null  and sourceType != ''"> and source_type = #{sourceType}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="harvestType != null  and harvestType != ''"> and harvest_type = #{harvestType}</if>
            <if test="executeCycle != null  and executeCycle != ''"> and execute_cycle = #{executeCycle}</if>
            <if test="executeDay != null  and executeDay != ''"> and execute_day = #{executeDay}</if>
            <if test="executeDate != null  and executeDate != ''"> and execute_date = #{executeDate}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="analysisRuleId != null  and analysisRuleId != ''"> and analysis_rule_id = #{analysisRuleId}</if>
            <if test="validRuleId != null  and validRuleId != ''"> and valid_rule_id = #{validRuleId}</if>
            <if test="matchRuleId != null  and matchRuleId != ''"> and match_rule_id = #{matchRuleId}</if>
            <if test="lastState != null "> and last_state = #{lastState}</if>
            <if test="totalBatch != null  and totalBatch != ''"> and total_batch = #{totalBatch}</if>
            <if test="errorReason != null  and errorReason != ''"> and error_reason = #{errorReason}</if>
            <if test="executeTime != null "> and execute_time = #{executeTime}</if>
            <if test="ifaceName != null  and ifaceName != ''"> and iface_name like concat('%', #{ifaceName}, '%')</if>
            <if test="describes != null  and describes != ''"> and describes = #{describes}</if>
            <if test="ifaceUrl != null  and ifaceUrl != ''"> and iface_url = #{ifaceUrl}</if>
            <if test="ifaceParams != null  and ifaceParams != ''"> and iface_params = #{ifaceParams}</if>
            <if test="ftpUrl != null  and ftpUrl != ''"> and ftp_url = #{ftpUrl}</if>
            <if test="ftpUser != null  and ftpUser != ''"> and ftp_user = #{ftpUser}</if>
            <if test="ftpPwd != null  and ftpPwd != ''"> and ftp_pwd = #{ftpPwd}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="operUser != null  and operUser != ''"> and oper_user = #{operUser}</if>
            and is_delete = 0
        </where>
    </select>

    <select id="selectDataSourceById" parameterType="String" resultMap="DataSourceResult">
        <include refid="selectDataSourceVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataSource" parameterType="com.ruoyi.storage.api.model.DataSource" useGeneratedKeys="true" keyProperty="id">
        insert into data_source
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null and dataId != ''">data_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="dataType != null and dataType != ''">data_type,</if>
            <if test="sourceType != null and sourceType != ''">source_type,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="harvestType != null and harvestType != ''">harvest_type,</if>
            <if test="executeCycle != null">execute_cycle,</if>
            <if test="executeDay != null">execute_day,</if>
            <if test="executeDate != null">execute_date,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="analysisRuleId != null">analysis_rule_id,</if>
            <if test="validRuleId != null">valid_rule_id,</if>
            <if test="matchRuleId != null">match_rule_id,</if>
            <if test="lastState != null">last_state,</if>
            <if test="totalBatch != null">total_batch,</if>
            <if test="errorReason != null">error_reason,</if>
            <if test="executeTime != null">execute_time,</if>
            <if test="ifaceName != null">iface_name,</if>
            <if test="describes != null">describes,</if>
            <if test="ifaceUrl != null">iface_url,</if>
            <if test="ifaceParams != null">iface_params,</if>
            <if test="ftpUrl != null">ftp_url,</if>
            <if test="ftpPort != null">ftp_port,</if>
            <if test="ftpUser != null">ftp_user,</if>
            <if test="ftpPwd != null">ftp_pwd,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="operUser != null and operUser != ''">oper_user,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null and dataId != ''">#{dataId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="dataType != null and dataType != ''">#{dataType},</if>
            <if test="sourceType != null and sourceType != ''">#{sourceType},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="harvestType != null and harvestType != ''">#{harvestType},</if>
            <if test="executeCycle != null">#{executeCycle},</if>
            <if test="executeDay != null">#{executeDay},</if>
            <if test="executeDate != null">#{executeDate},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="analysisRuleId != null">#{analysisRuleId},</if>
            <if test="validRuleId != null">#{validRuleId},</if>
            <if test="matchRuleId != null">#{matchRuleId},</if>
            <if test="lastState != null">#{lastState},</if>
            <if test="totalBatch != null">#{totalBatch},</if>
            <if test="errorReason != null">#{errorReason},</if>
            <if test="executeTime != null">#{executeTime},</if>
            <if test="ifaceName != null">#{ifaceName},</if>
            <if test="describes != null">#{describes},</if>
            <if test="ifaceUrl != null">#{ifaceUrl},</if>
            <if test="ifaceParams != null">#{ifaceParams},</if>
            <if test="ftpUrl != null">#{ftpUrl},</if>
            <if test="ftpPort != null">#{ftpPort},</if>
            <if test="ftpUser != null">#{ftpUser},</if>
            <if test="ftpPwd != null">#{ftpPwd},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="operUser != null and operUser != ''">#{operUser},</if>
         </trim>
    </insert>

    <update id="updateDataSource" parameterType="com.ruoyi.storage.api.model.DataSource">
        update data_source
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null and dataId != ''">data_id = #{dataId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="dataType != null and dataType != ''">data_type = #{dataType},</if>
            <if test="sourceType != null and sourceType != ''">source_type = #{sourceType},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="harvestType != null and harvestType != ''">harvest_type = #{harvestType},</if>
            <if test="executeCycle != null">execute_cycle = #{executeCycle},</if>
            <if test="executeDay != null">execute_day = #{executeDay},</if>
            <if test="executeDate != null">execute_date = #{executeDate},</if>
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="analysisRuleId != null">analysis_rule_id = #{analysisRuleId},</if>
            <if test="validRuleId != null">valid_rule_id = #{validRuleId},</if>
            <if test="matchRuleId != null">match_rule_id = #{matchRuleId},</if>
            <if test="lastState != null">last_state = #{lastState},</if>
            <if test="totalBatch != null">total_batch = #{totalBatch},</if>
            <if test="errorReason != null">error_reason = #{errorReason},</if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            <if test="ifaceName != null">iface_name = #{ifaceName},</if>
            <if test="describes != null">describes = #{describes},</if>
            <if test="ifaceUrl != null">iface_url = #{ifaceUrl},</if>
            <if test="ifaceParams != null">iface_params = #{ifaceParams},</if>
            <if test="ftpUrl != null">ftp_url = #{ftpUrl},</if>
            <if test="ftpPort != null">ftp_port = #{ftpPort},</if>
            <if test="ftpUser != null">ftp_user = #{ftpUser},</if>
            <if test="ftpPwd != null">ftp_pwd = #{ftpPwd},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="operUser != null and operUser != ''">oper_user = #{operUser},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataSourceById" parameterType="String">
        update data_source set is_delete=1 where id = #{id}
    </delete>

    <delete id="deleteDataSourceByIds" parameterType="String">
        update data_source set is_delete=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
