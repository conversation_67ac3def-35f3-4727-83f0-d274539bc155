<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.storage.mapper.FusionRuleMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.storage.domain.FusionRule">
            <id property="ruleId" column="rule_id" />
            <result property="ruleName" column="rule_name" />
            <result property="interactionStatus" column="interaction_status" />
            <result property="status" column="status" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
            <result property="remark" column="remark" />
    </resultMap>

    <sql id="Base_Column_List">
        rule_id,rule_name,interaction_status,status,create_by,create_time,
        update_by,update_time,remark
    </sql>
</mapper>
