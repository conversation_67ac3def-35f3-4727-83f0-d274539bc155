<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.storage.mapper.DataSourceTaskMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.storage.domain.DataSourceTask">
            <id property="id" column="id" />
            <result property="dataId" column="data_id" />
            <result property="cycleType" column="cycle_type" />
            <result property="cycleDate" column="cycle_date" />
            <result property="cycleWeek" column="cycle_week" />
            <result property="cycleMode" column="cycle_mode" />
            <result property="cycleCron" column="cycle_cron" />
            <result property="nextCycleStartDate" column="next_cycle_start_date" />
            <result property="nextCycleEndDate" column="next_cycle_end_date" />
            <result property="nextCycleDate" column="next_cycle_date" />
            <result property="isDelete" column="is_delete" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="operUser" column="oper_user" />
            <result property="jobId" column="job_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,data_id,cycle_type,cycle_date,cycle_week,cycle_mode,
        cycle_cron,next_cycle_start_date,next_cycle_end_date,next_cycle_date,is_delete,
        create_time,update_time,oper_user,job_id
    </sql>
</mapper>
