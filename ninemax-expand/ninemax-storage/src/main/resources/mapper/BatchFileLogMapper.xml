<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.storage.mapper.BatchFileLogMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.storage.domain.BatchFileLog">
            <id property="id" column="id" />
            <result property="batchId" column="batch_id" />
            <result property="fileName" column="file_name" />
            <result property="pageNum" column="page_num" />
            <result property="errorReason" column="error_reason" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,batch_id,file_name,page_num,error_reason,create_time
    </sql>
</mapper>
