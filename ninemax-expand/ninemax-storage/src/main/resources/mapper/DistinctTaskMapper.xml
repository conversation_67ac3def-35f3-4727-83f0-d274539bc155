<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.storage.mapper.DistinctTaskMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.storage.domain.DistinctTask">
            <id property="taskId" column="task_id" />
            <result property="taskName" column="task_name" />
            <result property="distinctRange" column="distinct_range" />
            <result property="dataType" column="data_type" />
            <result property="dataLevel" column="data_level" />
            <result property="fileName" column="file_name" />
            <result property="fileSize" column="file_size" />
            <result property="operator" column="operator" />
            <result property="status" column="status" />
            <result property="totalCount" column="total_count" />
            <result property="repeatCount" column="repeat_count" />
            <result property="startTime" column="start_time" />
            <result property="endTime" column="end_time" />
    </resultMap>

    <sql id="Base_Column_List">
        task_id,task_name,distinct_range,data_type,data_level,file_name,
        file_size,operator,status,total_count,repeat_count,
        start_time,end_time
    </sql>
</mapper>
