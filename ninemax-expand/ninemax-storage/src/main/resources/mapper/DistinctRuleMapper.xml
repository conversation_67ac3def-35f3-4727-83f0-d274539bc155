<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.storage.mapper.DistinctRuleMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.storage.domain.DistinctRule">
            <id property="id" column="id" />
            <result property="distinctNode" column="distinct_node" />
            <result property="distinctRange" column="distinct_range" />
            <result property="dataType" column="data_type" />
            <result property="dataLevel" column="data_level" />
            <result property="state" column="state" />
    </resultMap>

    <sql id="Base_Column_List">
        id,distinct_node,distinct_range,data_type,data_level,state
    </sql>
</mapper>
