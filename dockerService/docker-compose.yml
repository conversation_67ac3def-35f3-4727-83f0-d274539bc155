version : '3.8'
services:
  nginx:
    container_name: nginx
    image: nginx
    build:
      context: ./nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/html/dist:/home/<USER>/nginx/ruoyi-ui
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - ruoyi-gateway
    links:
      - ruoyi-gateway
  ruoyi-gateway:
    container_name: ruoyi-gateway
    build:
      context: ./ruoyi/gateway
      dockerfile: dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - ruoyi-redis
    links:
      - ruoyi-redis
  ruoyi-auth:
    container_name: ruoyi-auth
    build:
      context: ./ruoyi/auth
      dockerfile: dockerfile
    ports:
      - "8081:8081"
    depends_on:
      - ruoyi-redis
    links:
      - ruoyi-redis
  ruoyi-modules-system:
    container_name: ruoyi-modules-system
    build:
      context: ./ruoyi/modules/system
      dockerfile: dockerfile
    ports:
      - "8082:8082"
    depends_on:
      - ruoyi-redis
      - ruoyi-mysql
    links:
      - ruoyi-redis
      - ruoyi-mysql
  ruoyi-modules-gen:
    container_name: ruoyi-modules-gen
    build:
      context: ./ruoyi/modules/gen
      dockerfile: dockerfile
    ports:
      - "8083:8083"
    depends_on:
      - ruoyi-mysql
    links:
      - ruoyi-mysql
  ruoyi-modules-job:
    container_name: ruoyi-modules-job
    build:
      context: ./ruoyi/modules/job
      dockerfile: dockerfile
    ports:
      - "8084:8084"
    depends_on:
      - ruoyi-mysql
    links:
      - ruoyi-mysql
  ruoyi-modules-file:
    container_name: ruoyi-modules-file
    build:
      context: ./ruoyi/modules/file
      dockerfile: dockerfile
    ports:
      - "8085:8085"
    volumes:
    - ./ruoyi/uploadPath:/home/<USER>/uploadPath
  ruoyi-visual-monitor:
    container_name: ruoyi-visual-monitor
    build:
      context: ./ruoyi/visual/monitor
      dockerfile: dockerfile
    ports:
      - "8086:8086"
