<template>
  <div class="navbar"> 
    <tags-view /> 
    <!-- <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->
    <!-- <breadcrumb v-if="!settingsStore.topNav" id="breadcrumb-container" class="breadcrumb-container" /> -->
    <!-- <top-nav v-if="settingsStore.topNav" id="topmenu-container" class="topmenu-container" /> -->

    <!-- 标题 -->
    <div class="add_top_menu_tit">生物医学文献仓储平台</div>
    <!-- 消息提示 -->
    <el-popover
        class="box-item"
        title="Title"
        content="Bottom Center prompts info"
        placement="bottom"
    >
      <template #reference>
        <el-badge :value="23" :max="99" class="top_menu_badge">
          <svg 
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="19px" height="23px">
            <image  x="0px" y="0px" width="19px" height="23px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAXCAMAAADeH4ToAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAolBMVEX///8AeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNT////Ffo8bAAAANHRSTlMABRbHxhSBfwl56Od9CybY3ysa5OogsbowMoCnpbDCudnVEv79EUWvFVAXdtBzQj5f41wK+RnFLQAAAAFiS0dEAIgFHUgAAAAHdElNRQfpBhAXFRuGf9jYAAAAj0lEQVQY03XR1w7CMBBE0QVM7wRCDaGG3uf/vw1ix4QxcJ+sI61WWovYMuKWzUHlHSsAKJKUypWXVWv1lBpNmFptSx0Ptm4vMR9pfUMDfDbUNiIzy8dkE20BWfDX/B82JQu1zcjmMS2WZKu1SKTAqcjZGreR7c5tn5zhcIw70VHPeurCt796uN2/funxfj0BiyEvwlVfRawAAAAASUVORK5CYII=" />
          </svg>
        </el-badge>
      </template>
    </el-popover>

    <div class="right-menu">
      <!-- <template v-if="appStore.device !== 'mobile'"> -->
        <!-- 全局搜索 -->
        <!-- <header-search id="header-search" class="right-menu-item" /> -->

<!--        <el-tooltip content="源码地址" effect="dark" placement="bottom">-->
<!--          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />-->
<!--        </el-tooltip>-->

<!--        <el-tooltip content="文档地址" effect="dark" placement="bottom">-->
<!--          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />-->
<!--        </el-tooltip>-->
        <!-- 全屏 -->
        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->
        <!-- 主题 切换-->
        <!-- <el-tooltip content="主题模式" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect theme-switch-wrapper" @click="toggleTheme">
            <svg-icon v-if="settingsStore.isDark" icon-class="sunny" />
            <svg-icon v-if="!settingsStore.isDark" icon-class="moon" />
          </div>
        </el-tooltip> -->
        <!-- 布局大小 -->
        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
      <!-- </template> -->
      <div class="avatar-container">
        <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <!-- <img :src="userStore.avatar" class="user-avatar" /> -->
            <!-- <el-icon><caret-bottom /></el-icon> -->
            <svg 
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="14px" height="16px"
              style="margin-right: 6px;"
              >
              <image  x="0px" y="0px" width="14px" height="16px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAQCAMAAAARSr4IAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAllBMVEX///8AeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNT////LBBQjAAAAMHRSTlMAMrDm2IwRRfrXAdyIJdEp1AOUVuQZR8r89KcdB2KjvMDbKhDifsbdudXg6/DStFINx/Z+AAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kGEBYnMih/fvIAAAB4SURBVAjXbczbFoFQFEbhX5GzEEKHXTqxC+v9n67GXouxL5p3380EhiaOO515kOYLGlqKV2sybZhbFu2YvnDPPLjMo6wCo9P5t76E19s9wmhxkqosU2kSm3EuZ8ofQFHSv7JARVYVaps1GpsNgudL67brWq3fn28Pb74XiWj2nuMAAAAASUVORK5CYII=" />
            </svg>
            Admin
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <el-dropdown-item command="setLayout" v-if="settingsStore.showSettings">
                <span>布局设置</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import HeaderSearch from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import useAppStore from '@/store/modules/app'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import TagsView from './TagsView'

const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()

function toggleSideBar() {
  appStore.toggleSideBar()
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/index';
    })
  }).catch(() => { });
}

const emits = defineEmits(['setLayout'])
function setLayout() {
  emits('setLayout');
}

function toggleTheme() {
  settingsStore.toggleTheme()
}
</script>

<style lang='scss' scoped>
.navbar {
  height: 37px;
  overflow: hidden;
  position: relative;
  background: var(--navbar-bg);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  display: flex;
  flex-direction: row;
  align-items: center;
  height: 37px;
  border-bottom: 1px solid #bccde0;

  .add_top_menu_tit {
    width: 200px;
    white-space: nowrap;
    font-size: 20px;
    font-family: "SourceHanSansSC";
    color: rgb(0, 109, 192);
    font-weight: bold;
    line-height: 1.5;
    text-align: center;
    margin-right: 26px;
  }

  .top_menu_badge {
    margin: 0 36px;
    font-size: 10px !important;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    border-width: 2px;
    border-color: rgb(255, 255, 255);
    border-style: solid;

    :deep(.el-badge__content--danger) {
      height: 14px !important;
      font-size: 10px !important;
      font-family: "MicrosoftYaHei";
      color: rgb(255, 255, 255);
      top: 3px;
      padding: 1px 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ff4c50;
    }
  }



  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    // float: right;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: var(--navbar-text);
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }

      &.theme-switch-wrapper {
        display: flex;
        align-items: center;

        svg {
          transition: transform 0.3s;

          &:hover {
            transform: scale(1.15);
          }
        }
      }
    }

    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        // margin-top: 5px;
        position: relative;

        font-size: 12px;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 0, 0);

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          // top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
