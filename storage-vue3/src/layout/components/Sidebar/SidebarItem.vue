<template>
  <div v-if="!item.hidden" class="my_menu_item">
    <template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
        <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)" class="my_sub_menu">
          <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }" class="my_sub_menu_noDropdown">
            <svg-icon :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"/>
            <span class="menu-title" :title="hasTitle(onlyOneChild.meta.title)">{{ onlyOneChild.meta.title }}</span>
            <!-- <template #title><span class="menu-title" :title="hasTitle(onlyOneChild.meta.title)">{{ onlyOneChild.meta.title }}</span></template> -->
          </el-menu-item>
        </app-link>
    </template>
<!-- teleported -->
    <el-sub-menu v-else ref="subMenu" :index="resolvePath(item.path)"  class="my_sub_menu asd999">
      <template v-if="item.meta" #title>
        <svg-icon :icon-class="item.meta && item.meta.icon" />
        <span class="menu-title my_menu_title" :title="hasTitle(item.meta.title)">{{ item.meta.title.length > 4 ? item.meta.title.substr(0, 4) : item.meta.title }}</span>
      </template>
      

      <!-- <sidebar-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />  -->
      <div class="nest-menu my_menu_children">
          <MySidebarItem v-for="(child, index) in item.children" :key="child.path + index" :data-asd="resolvePath(child.path)" :base-path="basePath" :item="child"></MySidebarItem>
      </div>

    </el-sub-menu>
  </div>
</template>

<script setup>
import { isExternal } from '@/utils/validate'
import AppLink from './Link'
import { getNormalPath } from '@/utils/ruoyi'
import MySidebarItem from './MySidebarItem.vue'

const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true
  },
  isNest: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
})

const onlyOneChild = ref({});

function hasOneShowingChild(children = [], parent) {
  if (!children) {
    children = [];
  }
  const showingChildren = children.filter(item => {
    if (item.hidden) {
      return false
    }
    onlyOneChild.value = item
    return true
  })

  // When there is only one child router, the child router is displayed by default
  if (showingChildren.length === 1) {
    return true
  }

  // Show parent if there are no child router to display
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
};

function resolvePath(routePath, routeQuery) {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  if (routeQuery) {
    let query = JSON.parse(routeQuery);
    return { path: getNormalPath(props.basePath + '/' + routePath), query: query }
  }
  return getNormalPath(props.basePath + '/' + routePath)
}

function hasTitle(title){
  if (title.length > 5) {
    return title;
  } else {
    return "";
  }
}
</script>

<style scoped lang="scss">
.my_menu_item {
  height: 66px;
  display: flex;
  align-items: center;
  justify-content: center;

  :deep(.my_sub_menu) {
    width: 100%;
    height: 100%;
    
    & > .el-sub-menu__title {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    & .my_menu_title {
      display: inline-block !important;
      width: 60px !important;
      height: 20px !important;
      line-height: 20px !important;
      visibility: visible !important;
      font-size: 14px;
      text-align: center;
      font-family: "MicrosoftYaHei";
      color: rgb(255, 255, 255);
    }


    .svg-icon {
      width: 20px;
      height: 20px;
      margin: 0 0 7px 0 !important;
    }
  }

}
.my_menu_children {
  padding: 19px 27px;
  background-color: #fff;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.17);
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;

  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  max-height: 400px;
  column-gap: 48px;

  :deep(.el-popper) {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    border: 0;
    background-color: transparent;
  }
  :deep(.el-menu--popup) {
    padding: 0;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}
.my_sub_menu_noDropdown {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0;

  &:hover {
    background-color: #2192e9 !important;
    border-right: 3px solid #95d1ff;
  }

  .menu-title {
    display: inline-block !important;
    width: 60px !important;
    height: 20px !important;
    line-height: 20px !important;
    visibility: visible !important;
    font-size: 14px;
    text-align: center;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
  }
}
#app .sidebar-container .svg-icon {
  fill: #bfcbd9;
}
</style>

<style>
#app .my_sidebar_container .el-sub-menu__title:hover {
  background-color: #2192e9 !important;
  border-right: 3px solid #95d1ff;
}
</style>