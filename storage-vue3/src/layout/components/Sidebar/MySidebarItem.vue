<template>
  <div v-if="!item.hidden" :class="item?.children?.length > 0 ? 'my_sidebar_item' : ''">
    <p 
      class="my_sidebar_item_title"
      :class="item?.children?.length > 0 ? 'my_sidebar_item_title_has_children' : ''">
      <app-link v-if="item.path" :to="resolvePath(item.path, item.query)">
        {{ item.meta.title }}
      </app-link>
    </p>
    <template v-if="item?.children">
      <template v-for="(child, index) in item.children" :key="child.path + index">
        <MySidebarItem :item="child" :base-path="resolvePath(item.path)"></MySidebarItem>
      </template>
    </template>
    <template v-else>
      <!-- <MySidebarItem :item="item"></MySidebarItem> -->
       <!-- {{ item }} -->
       <!-- <p 
          class="my_sidebar_item_title"
          :class="item?.children?.length > 0 ? 'my_sidebar_item_title_has_children' : ''">
          <app-link v-if="item.path" :to="resolvePath(item.path, item.query)">
            {{ item.meta.title }}
          </app-link>
        </p> -->
    </template>
  </div>
</template>

<script setup>
import AppLink from './Link'
import { isExternal } from "@/utils/validate";
import { getNormalPath } from "@/utils/ruoyi";

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  basePath: {
    type: String,
    default: ''
  }
});

function resolvePath(routePath, routeQuery) {
  if (isExternal(routePath)) {
    return routePath;
  }
  if (isExternal(props.basePath)) {
    return props.basePath;
  }
  if (routeQuery) {
    let query = JSON.parse(routeQuery);
    return {
      path: getNormalPath(props.basePath + "/" + routePath),
      query: query,
    };
  }
  return getNormalPath(props.basePath + "/" + routePath);
}

</script>

<style scoped lang="scss">
.my_sidebar_item {
  margin-bottom: 12px;
}

.my_sidebar_item_title {
  white-space: nowrap;
  font-size: 14px;
  font-family: "DengXian";
  color: rgb(95, 95, 95);
  line-height: 2.071;
  margin: 0;

  & > a:hover{
    color: rgb(0, 120, 212);
  }

}

.my_sidebar_item_title_has_children {
  font-size: 16px;
  font-family: "SourceHanSansSC";
  color: rgb(0, 0, 0);
  line-height: 2.25;
}
</style>
