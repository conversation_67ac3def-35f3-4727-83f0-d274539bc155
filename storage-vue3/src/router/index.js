import { createWebHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  // {
  //   path: '/dataSource',
  //   component: Layout,
  //   hidden: false,
  //   redirect: 'noredirect',
  //   children: [
  //     {
  //       path: 'management',
  //       // component: () => import('@/views/system/dataSource/management/index'),
  //       name: 'dataSourceManagement',
  //       meta: { title: '数据源管理', icon: 'user' }
  //     }
  //   ]
  // }
            {
                "name": "Source2",
                "path": "/source2",
                "hidden": false,
                "redirect": "noRedirect",
                "component": Layout,
                "alwaysShow": true,
                "meta": {
                    "title": "测试数据源管理",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                },
                "children": [
                    {
                        "name": "DataSource2",
                        "path": "dataSource",
                        "hidden": false,
                        "component": () => import('@/views/source/dataSource/index'),
                        "meta": {
                            "title": "测试数据源管理",
                            "icon": "drag",
                            "noCache": false,
                            "link": null
                        }
                    },{
                      "name": "DataSourceAcquisition2",
                      "path": "dataSourceAcquisition",
                      "hidden": false,
                      "component": () => import('@/views/source/dataSourceAcquisition/index'),
                      "meta": {
                          "title": "测试数据源采集",
                          "icon": "component",
                          "noCache": false,
                          "link": null
                      }
                    }
                ]
            },
            {
                "name": "Storage2",
                "path": "/storage2",
                "hidden": false,
                "redirect": "noRedirect",
                "component": Layout,
                "alwaysShow": true,
                "meta": {
                    "title": "测试存储服务",
                    "icon": "build",
                    "noCache": false,
                    "link": null
                },
                "children": [
                  {
                    "name": "RightsInformationManagement2",
                    "path": "rightsInformationManagement",
                    "hidden": false,
                    "component": () => import('@/views/storage/rightsInformationManagement/index'),
                    "meta": {
                        "title": "测试权益信息管理",
                        "icon": "drag",
                        "noCache": false,
                        "link": null
                    }
                  },{
                    "name": "ArticleLevelData2",
                    "path": "articleLevelData",
                    "hidden": false,
                    "component": () => import('@/views/storage/articleLevelData/index'),
                    "meta": {
                        "title": "测试篇级数据",
                        "icon": "component",
                        "noCache": false,
                        "link": null
                    }
                  }
                ]
            },
            {
                "name": "FileBackup",
                "path": "/fileBackup",
                "hidden": false,
                "redirect": "noRedirect",
                "component": Layout,
                "alwaysShow": true,
                "meta": {
                    "title": "文件备份管理",
                    "icon": "folder",
                    "noCache": false,
                    "link": null
                },
                "children": [
                  {
                    "name": "FileDataSource",
                    "path": "fileDataSource",
                    "hidden": false,
                    "component": () => import('@/views/FileBackup/fileDataSource/index'),
                    "meta": {
                        "title": "文件数据源",
                        "icon": "database",
                        "noCache": false,
                        "link": null
                    }
                  },
                  {
                    "name": "DataUpload",
                    "path": "dataUpload",
                    "hidden": false,
                    "component": () => import('@/views/FileBackup/dataUpload/index'),
                    "meta": {
                        "title": "数据上传",
                        "icon": "upload",
                        "noCache": false,
                        "link": null
                    }
                  },
                  {
                    "name": "DataBackup",
                    "path": "dataBackup",
                    "hidden": false,
                    "component": () => import('@/views/FileBackup/dataBackup/index'),
                    "meta": {
                        "title": "数据备份",
                        "icon": "copy-document",
                        "noCache": false,
                        "link": null
                    }
                  },
                  {
                    "name": "DataAssociated",
                    "path": "dataAssociated",
                    "hidden": false,
                    "component": () => import('@/views/FileBackup/dataAssociated/index'),
                    "meta": {
                        "title": "数据关联",
                        "icon": "connection",
                        "noCache": false,
                        "link": null
                    }
                  },
                  {
                    "name": "DataRelationship",
                    "path": "dataRelationship",
                    "hidden": false,
                    "component": () => import('@/views/FileBackup/dataRelationship/index'),
                    "meta": {
                        "title": "数据关系",
                        "icon": "share",
                        "noCache": false,
                        "link": null
                    }
                  },
                  {
                    "name": "SearchService",
                    "path": "searchService",
                    "hidden": false,
                    "component": () => import('@/views/FileBackup/searchService/index'),
                    "meta": {
                        "title": "搜索服务",
                        "icon": "search",
                        "noCache": false,
                        "link": null
                    }
                  }
                ]
            },
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
  //   // 数据获取与转换 ==> 解析映射任务列表 ==> 批次数据详情
  {
    path: '/obtainConvert/batch-detail',
    component: Layout,
    hidden: true,
    permissions: ['obtainConvert:parsingMappingTask:batchDetail'],
    children: [
      {
        path: 'index/:batchId',
        component: () => import('@/views/obtainConvert/batchDetail/index'),
        name: 'BatchDetail',
        meta: { title: '批次数据详情', activeMenu: '/obtainConvert/batchDetail' }
      }
    ]
  },
  {
    path: '/obtainConvert/article-detail',
    component: Layout,
    hidden: true,
    permissions: ['obtainConvert:parsingMappingTask:articleDetail'],
    children: [
      {
        path: 'index/:originalArticleId',
        component: () => import('@/views/obtainConvert/articleDetail/index'),
        name: 'ArticleDetail',
        meta: { title: '篇级数据详情', activeMenu: '/obtainConvert/articleDetail' }
      }
    ]
  },
  // 篇级数据归一化 - 疑似详情页
  {
    path: '/unitary/single/article',
    component: Layout,
    hidden: true,
    permissions: ['unitary:single:article:detail'],
    children: [
      {
        path: 'suspectDetail',
        component: () => import('@/views/unitary/single/article/suspectDetail'),
        name: 'SuspectDetail',
        meta: { title: '疑似数据详情', activeMenu: '/unitary/single/article' }
      },
      {
        path: 'relateDetail',
        component: () => import('@/views/unitary/single/article/relateDetail'),
        name: 'RelateDetail',
        meta: { title: '挂接数据详情', activeMenu: '/unitary/single/article' }
      }
    ]
  },
  // 文件备份管理 - 数据关联详情页
  {
    path: '/fileBackup/dataAssociated',
    component: Layout,
    hidden: true,
    permissions: ['fileBackup:dataAssociated:detail'],
    children: [
      {
        path: 'detail/:id',
        component: () => import('@/views/FileBackup/dataAssociated/detail'),
        name: 'DataAssociatedDetail',
        meta: { title: '数据关联详情', activeMenu: '/fileBackup/dataAssociated' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
});

export default router;
