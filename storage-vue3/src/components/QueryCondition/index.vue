<template>
  <el-form-item>
    <el-select
      v-model="leftValue"
      @change="emitQuery"
      placeholder="选择条件"
      :style="`width: ${leftWidth};`"
      class="left_wrapper"
    >
      <el-option
        v-for="option in leftOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>

    <!-- 输入框 -->
    <el-input
      v-if="rightComponent === 'el-input'"
      v-model="rightValue"
      @input="emitQuery"
      v-bind="rightProps"
      :style="`width: ${rightWidth};`"
      placeholder="请输入"
    />

    <!-- 下拉框 -->
    <el-select
      v-else-if="rightComponent === 'el-select'"
      v-model="rightValue"
      @change="emitQuery"
      v-bind="rightProps"
      :style="`width: ${rightWidth};`"
      placeholder="请选择"
    >
      <el-option
        v-for="opt in rightProps.options"
        :key="opt.value"
        :label="opt.label"
        :value="opt.value"
      />
    </el-select>

    <!-- 日期选择器 -->
    <el-date-picker
      v-else-if="rightComponent === 'el-date-picker'"
      v-model="rightValue"
      @change="emitQuery"
      v-bind="rightProps"
      :style="`width: ${rightWidth};`"
      placeholder="选择日期"
    />

    <!-- 复选框组 -->
    <el-checkbox-group
      v-else-if="rightComponent === 'el-checkbox-group'"
      v-model="rightValue"
      @change="emitQuery"
      v-bind="rightProps"
      :style="`width: ${rightWidth};`"
    >
      <el-checkbox
        v-for="item in rightProps.options"
        :key="item.value"
        :label="item.value"
      >
        {{ item.label }}
      </el-checkbox>
    </el-checkbox-group>

    <!-- 可以添加更多的条件组件 -->
    <!-- <el-button @click="reset" style="margin-left: 8px;">重置</el-button> -->
  </el-form-item>
</template>

<script>
export default {
  props: {
    leftOptions: {
      type: Array,
      required: true,
    },
    rightComponent: {
      type: String,
      required: true,
    },
    rightProps: {
      type: Object,
      default: () => ({}),
    },
    leftProps: {
      type: Object,
      default: () => ({}),
    },
    leftWidth: {
      type: String,
      default: "105px",
    },
    rightWidth: {
      type: String,
      default: "208px",
    },
  },
  data() {
    return {
      leftValue: this?.leftProps?.defaultValue || "",
      rightValue: this.rightProps.defaultValue || "", // 初始化右侧组件的值
    };
  },

  methods: {
    emitQuery() {
      this.$emit("query", {
        left: this.leftValue,
        right: this.rightValue,
      });
    },
    reset() {
      this.leftValue = this?.leftProps?.defaultValue || "";
      this.rightValue = this.rightProps.defaultValue || "";
      this.emitQuery();
    },
  },
  expose: ["reset"],
};
</script>

<style scoped>
.query-condition {
  display: flex;
  align-items: center;
}
.el-form--inline .el-form-item {
  margin-bottom: 4px;
  margin-top: 4px;
  margin-right: 8px;
}
:deep(.el-select__wrapper),
:deep(.el-input__wrapper) {
  border-radius: 0;
}
:deep(.left_wrapper .el-select__wrapper) {
  border: 1px solid #dcdfe6;
  border-right: 0;
  box-shadow: none;
}
:deep(.el-select__placeholder) {
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(1, 1, 1);
}
:deep(.el-select__placeholder.is-transparent) {
  color: var(--el-text-color-placeholder);
}
</style>
