// Tabs容器样式 - 可复用的标签页容器样式
.app-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;

  .tabs {
    flex: 1;
    display: flex;
    flex-direction: column;

    // tabs头部样式 - 与FormSearch对齐
    :deep(.el-tabs__header) {
      margin: 0 0 16px 11px; // 简化margin写法
      flex-shrink: 0;
    }

    // tabs内容区域
    :deep(.el-tabs__content) {
      flex: 1;
      overflow: hidden;
      padding: 0;

      .el-tab-pane {
        height: 100%;
        overflow: hidden;
      }
    }

    // tab导航底部分割线
    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
      background-color: #e4e7ed;
    }

    // tab项样式
    :deep(.el-tabs__item) {
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;

      &.is-active {
        color: #409eff;
        font-weight: 500;
      }
    }
  }
}