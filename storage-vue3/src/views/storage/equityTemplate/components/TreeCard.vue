<template>
  <div class="resource-tree-container">
    <el-tree
      :data="treeData"
      :props="defaultProps"
      :default-expand-all="defaultExpandAll"
      class="custom-tree"
      node-key="id"
      draggable
      highlight-current
    >
      <template #default="{ node, data }">
        <div class="tree-node">
          <div class="node-content" :class="{'alone-node': data.siblingAlone}">
            <!-- 文件夹icon -->
            <el-icon
              v-if="data.children && data.children.length > 0"
              class="folder-icon"
            >
              <!-- <Folder v-if="!node.expanded" /> -->
              <img v-if="!node.expanded" src="@/assets/images/add.png" alt="" />
              <!-- <FolderOpened v-else /> -->
              <img v-else src="@/assets/images/minus.png" alt="" />
            </el-icon>
            <el-icon v-else class="file-icon">
              <em></em>
              <!-- <Document /> -->
            </el-icon>
            <div class="node-label-box">
              <span class="node-label">{{ data.label }}</span>
              <span v-if="data.count" class="node-count">
                ({{ data.count }})
              </span>
            </div>
          </div>
          <div class="node-actions">
            <!-- 编辑 -->
            <span @click="handleClick('edit')">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="17px"
                height="17px"
              >
                <image
                  x="0px"
                  y="0px"
                  width="17px"
                  height="17px"
                  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAw1BMVEX////////////////////////////////////////////////////////////////////////////////////////5/P6n0PAUg9cEetVequT8/f6kz/ACedR2tugGe9Wv1fILftbS5/f4+/4WhNiayu6BvOpBmt9EnN/k8fqHv+vv9/wPgNdNoeGt1PE/md9fquQOf9bK4/afzO/l8fvI4vYAeNQMfta52vNkreVmruX0+f3+/v+Ow+zL4/bw9/xjrOV1iyZyAAAAFXRSTlMADHHA7Vrnf1zo7BBsuuXm+btbXcFgFAAGAAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kHAxMNO0fPGVQAAACzSURBVBjTVZDXFoJADERDE1QQLIG1K4iKii4W7OX/v8qFXSzzlNwzJ8kEgEmSFRVVRZZASCshV0njQBc9uoh67hC9R9qdLhpsRpmDXn/gDUdYqYJZgLEfTEJEEywBpjMyJxGiBTUOFrhcEZ+VNjgCuHEO0AEbcf0DmIfN2VAGki1fUc92Jbt9nBzEWSa7J0qPKTmdKaWXa3YPNG738BE8X5w0/3KhyMVcn+xG8Q5Jbn3/8wY+5hfi4PbILAAAAABJRU5ErkJggg=="
                />
              </svg>
            </span>
            <!-- 新增 -->
            <span @click="handleClick('add')">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="17px"
                height="17px"
              >
                <image
                  x="0px"
                  y="0px"
                  width="17px"
                  height="17px"
                  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAzFBMVEX////////////////////////////////////////////////////////////////////////////////////////v9/zG4fVMoOEHfNUAeNSSxe0CedRGneCn0PDD3/W72/SQxOz7/f6hze82ld1Zp+Pf7vrq9PvB3vXP5vfo8/uEvurO5ffc7PnR5/fp8/u/3fT+/v8ch9l8uumRxewIfNXK4/bz+f1bqOP8/f5Ro+ILftaNw+zt9fyDveoMftYNf9b3+/7A3vSp0fBRtiCHAAAAFXRSTlMADHHA7Vrnf1zo7BBsuuXm+btbXcFgFAAGAAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kHAxMNJ1PORRsAAADQSURBVBjTRZDZVsJgDITTsqpUEPwEAlT2VfbNsoPv/078La3MRU6SM8lkImJg2bE48ZhtSYhEkgeSiUcjFVRfQUwFDJMUS2Utl4omS5sdL1Cp1txvt1atwOubZKCuDZqtJg2tQ0YcaHegq13otMGRd3raDzt97ZGVHAMdjtTHaKg/5CTLWCfTmavubDrRseE4zBfLcGq5mPPha63WG7a/Wzbrla9l7vF2+8OR42G/8/x7JA/e6awXPZ88KES+rre/2zXyZVj/3tPROyz78/mfO1y9Gm6LKmqTAAAAAElFTkSuQmCC"
                />
              </svg>
            </span>
            <!-- 删除 -->
            <span @click="handleClick('delete')">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="17px"
                height="17px"
              >
                <image
                  x="0px"
                  y="0px"
                  width="17px"
                  height="17px"
                  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAvVBMVEX////////////////////////////////////////////////////////////////////////////////////////v9/zG4fVMoOEHfNUAeNSSxe0CedRGneCn0PDD3/W72/SQxOw2ld1Zp+Pq9PvB3vXP5vfo8/uEvurO5ffc7PnR5/fp8/u/3fT+/v8ch9l8uumRxewIfNXK4/b8/f5Ro+ILftaNw+zt9fyDveoMftYNf9b3+/7A3vSp0fB+BRVeAAAAFXRSTlMADHHA7Vrnf1zo7BBsuuXm+btbXcFgFAAGAAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kHAxMMO17UKBUAAADESURBVBjTRVBXFoJADAzYUME+KrEXFLGg0uz3P5a7C+h85CXzJmVCJKDphSKKBV2jDKUyUpRLKVFRVV/FilKIZDC02R4ORGaIGVVgNJ5MZ9PJeATU6mQCc16olgXPAZMsYLnKJq+WgEUNrNnJGIfXaFILG966LOFueYcWNeHx/nCUOOzZExoLJ/+cdZ39E9py1+UaKCK4XuQucU8YxckNtySOQnkPdYDw/uAnP+4h0M19vd6f9yv3JVQ/70b+Dk3v/f/zBZRTGMvlb62sAAAAAElFTkSuQmCC"
                />
              </svg>
            </span>
            <!-- 移动 -->
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="17px"
                height="17px"
              >
                <image
                  x="0px"
                  y="0px"
                  width="17px"
                  height="17px"
                  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAgVBMVEX///////////////////////////////////////////////////////////////////////////////////////8njdtxtOdytOcmjNp+u+ms0/Gt1PF9uulZp+OVx+1Yp+NKn+CKwetJn+CLwuu22PMdh9lnr+Vor+Ych9n6/P6BRb4OAAAAFXRSTlMADHHA7Vrnf1zo7BBsuuXm+btbXcFgFAAGAAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kHAxMMIaO20W8AAACLSURBVBjTbZCJEoIwDETTcisICuutaBVF//8DbdIqg7oznbzJbHMRWSkdhAgDrcgriuEURy6RYFAiDqblCusNQ2prZAzbHfYHhsmUcoyVUyHx2KI9CRU0k3g2MBehkqqvXxWVEq8dOuM9rs7tjv4hNPe9nv5xr995aDGauf67l3V9dk/f51C6Ge7zAkEbExgkmQEGAAAAAElFTkSuQmCC"
                />
              </svg>
            </span>
            <!-- <el-button type="primary" size="small" :icon="Edit" circle />
            <el-button type="info" size="small" :icon="View" circle />
            <el-button type="warning" size="small" :icon="Share" circle />
            <el-button type="danger" size="small" :icon="More" circle /> -->
          </div>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { Edit, View, Share, More } from "@element-plus/icons-vue";

const props = defineProps({
  treeData: {
    type: Array,
    default: () => [],
  },
  defaultExpandAll: {
    type: Boolean,
    default: true,
  },
});

const defaultProps = {
  children: "children",
  label: "label",
};

const emit = defineEmits(["handleClick"]);

const handleClick = (type) => {
  // 防止点击事件向上冒泡
  event.stopPropagation();
  emit("handleClick", type);
};
</script>

<style scoped>
.resource-tree-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100%;
  background-color: #eff4fa;
  overflow: auto;
}

.custom-tree {
  background-color: transparent;
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: #000;
}

:deep(.el-tree-node__content) {
  height: auto;
  /* padding: 5px 0; */
  background-color: transparent;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  /* padding: 4px 8px; */
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

:deep(.alone-node > .el-icon) {
    visibility: hidden;
}

.folder-icon,
.file-icon {
  margin-right: 8px;
  color: #409eff;
}

.file-icon {
  color: #67c23a;
}

.file-icon em {
  font-style: normal;
  width: 2px;
  height: calc(100% + 14px);
  border-left: 1px dashed #b6c5d1;
  position: relative;
  top: -5px;
  left: 3px;
}

.node-label {
  /* color: #303133;
  font-weight: 500; */
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: #000;
}

.node-label-box {
  padding: 5px 10px;
}

:deep(.el-tree-node__content:hover .node-label-box) {
  background-color: #d7edff;
  border: 1px solid #0078d4;
}

:deep(.el-tree-node__content:hover .node-label-box .node-label) {
  color: #0078d4;
}

:deep(.el-tree-node__content:hover .node-label-box .node-count) {
  color: #0078d4;
}

.node-count {
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(0, 0, 0);
  margin-left: 4px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
  margin-left: 10px;
}

.node-actions > span {
  display: flex;
  align-items: center;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

/* 高亮显示特定节点 */
:deep(.el-tree-node__content) {
  position: relative;
  padding-left: 0 !important;
}

:deep(
    .el-tree-node__content:has(
        .tree-node:nth-child(1)
          .node-content:contains("商业资源元数据服务权与授权许可信息")
      )
  ) {
  background-color: #e1f3ff;
  border: 2px solid #409eff;
  border-radius: 4px;
  margin: 2px 0;
}

/* 自定义展开/折叠图标样式 */
:deep(.el-tree-node__expand-icon) {
  color: #409eff;
  font-size: 16px;
  padding: 0;
  display: none;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

/* 缩进样式 */
:deep(.el-tree-node__children) {
  padding-left: 20px;
}

/* 连接线样式 */
:deep(.el-tree-node) {
  position: relative;
}

:deep(.el-tree-node::before) {
  content: "";
  position: absolute;
  left: 9px;
  top: 30px;
  bottom: 0;
  width: 1px;
  border-right: 1px dashed #b6c5d1;
  /* background-color: #b6c5d1; */
}

:deep(.el-tree-node:last-child::before) {
  display: none;
}

:deep(.el-tree-node__content::after) {
  content: "";
  position: absolute;
  left: 9px;
  top: 50%;
  width: 20px;
  height: 1px;
  /* background-color: #b6c5d1; */
  border-right: 1px dashed #b6c5d1;
}

:deep(
    .el-tree-node > .el-tree-node__children > .el-tree-node:last-child::before
  ) {
  height: 50%;
}
</style>
