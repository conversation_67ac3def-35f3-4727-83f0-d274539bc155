<template>
    <!-- 权益信息管理 -->
    <viewBody title="期刊篇级数据">
        <template #content>
            <FormSearch
                v-model="queryParams"
                :formItems="formItems"
                searchBtnName="查询"
                @search="handleQuery"
                @reset="resetQuery"
            >
            </FormSearch>
            <!-- 表格部分 -->
            <TableForm ref="tableFormRef" 
                :columns="columns.filter(col => checkList.includes(col.prop))"
                :tableData="dataSourceList" 
                class="tableFormContent"
                v-loading="loading" 
                :showIndex="true" 
                :total="total"
                :isShowCount="false" 
                :showNoteColumn="false" 
                :showOtherColumn="false" 
                :showEditBtn="false" 
                :isShowSelection="true" 
                :columnsRightWidth="columnsRightWidth" 
                @cellClick="cellClick"
            >
                <!-- 自定义操作按钮 -->
                <template #header>
                    
                </template>
                <template #pagination>
                    <MyPagination
                        v-show="total>0"
                        :total="total"
                        :page="queryParams.pageNum"
                        :pageSize="queryParams.pageSize"
                        @pagination="getList"
                    />
                </template>
            </TableForm>
        </template>
    </viewBody>
</template>
<script setup>
import viewBody from '@/components/view/viewBody.vue';
import FormSearch from '@/views/source/components/FormSearch.vue';
import TableForm from '@/views/source/components/TableForm.vue';
import MyPagination from "@/components/Pagination/new.vue";

const {proxy} = getCurrentInstance();
// 数据源采集
const {
  storage_pub_type,
} = proxy.useDict(
    "storage_pub_type"
);
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    name: '',
    type: '',
})
// 搜索表单配置
const formItems = computed(() => [
    {
        label: '篇级名称',
        prop: 'name',
        component: 'el-input',
        props: {
            placeholder: '请输入篇级名称',
            clearable: true,
            style: { width: '200px' },
        }
    },
    {
        label: '数据源',
        prop: 'type',
        component: 'el-select',
        props: {
            placeholder: '请选择数据源',
            clearable: true,
            style: { width: '200px' },
        },
        children: () => storage_pub_type.value.map(dict => ({
            value: dict.value,
            label: dict.label
        }))
    }
])
const handleQuery = (params) => {
    Object.assign(queryParams, params)
}
const resetQuery = () => {}

const getList = (page) => {
    queryParams.pageNum = page.page
    queryParams.pageSize = page.limit
    console.log(page)
}
// 表格配置
let total = ref(400)
const loading = ref(false)
const tableFormRef = ref(null)
const columnsRightWidth = ref("150")
// 表格列配置
const columns = ref([
    { prop: 'name', label: '字段名称', minWidth: '120' },
    { prop: 'type', label: '字段分类', width: '260' },
    { prop: 'isRequired', label: '是否必填', width: '100' },
])
/**
 * 表格数据
 */
const dataSourceList = ref([
    { id: 1, name: '版权所有', type: '版权与授权许可信息', isRequired: '是' },
    { id: 2, name: '所有者类型', type: '版权与授权许可信息', isRequired: '否' },
    { id: 3, name: '授权方式', type: '版权与授权许可信息', isRequired: '是' },
    { id: 4, name: '数据接口', type: '版权与授权许可信息', isRequired: '否' },
    { id: 5, name: '许可协议', type: '版权与授权许可信息', isRequired: '是' },
    { id: 6, name: '上传合同或许可协议中包含权益内容的关键页截图', type: '版权与授权许可信息', isRequired: '否' },
])

// total.value = dataSourceList.value.length
/**
 *  单元格点击事件
 * @param row  
 * @param column 
 * @param cellValue 
 * @param event 
 */
const drawerRef = ref(null)
const cellClick = (row, column, cellValue, event) => {
    // console.log(row, column, cellValue, event)
}
const checkList = ref(columns.value.map(col => col.prop))

</script>
<style lang="scss" scoped>
:deep(.el-drawer__header) {
  padding: 14px 16px 10px 16px;
  margin: 0;
  border-bottom: 1px solid rgb(232, 232, 232);
}
:deep(.el-drawer__body) {
    padding: 0;
    padding-top: 10px;
}
</style>
<style lang="scss">

</style>