<template>
    <div class="equityTemplate-content">
        <div class="equityTemplate-content-left">
            <el-button 
                :icon="Plus" 
                size="large"
                class="equityTemplate-add-button"
                @click="handleAdd"
            >
                添加
            </el-button>
            <div class="equityTemplate-content-left-card-box">    
                <template v-for="i in 10">
                    <LeftSmallCard></LeftSmallCard>
                </template>
            </div>
        </div>
        <div class="equityTemplate-content-center">
            <TreeCard :treeData="treeData" @handleClick="handleTreeClick"></TreeCard>
        </div>
        <div class="equityTemplate-content-right">
            <RightTable></RightTable>
        </div>
    </div>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'

import LeftSmallCard from './LeftSmallCard.vue'
import RightTable from './RightTable.vue'
import TreeCard from './TreeCard.vue';

const handleAdd = () => {
    console.log("添加");
};

// 树形数据
const treeData = ref([
  {
    id: 1,
    label: "基本信息",
    count: 13,
    children: [
      { id: 11, label: "资源编号" },
      { id: 14, label: "出版机构" },
      {
        id: 15,
        label: "数据来源",
        count: 2,
        children: [
          {
            id: 151,
            label: "商业来源",
            count: 1,
            children: [{ 
                id: 1511, label: "商业订购", siblingAlone: true 
            }],
          },
          { id: 152, label: "商业订购(附赠)" },
        ],
      },
      { id: 16, label: "资源类型" },
    ],
  },
  {
    id: 2,
    label: "商业资源元数据服务权与授权许可信息",
    count: 6,
  },
  {
    id: 3,
    label: "服务权限",
    count: 8,
    children: [
      { id: 31, label: "批量获取数据" },
      { id: 32, label: "本地存储" },
      { id: 33, label: "网络传播" },
    ],
  },
]);
const handleTreeClick = (type) => {
    console.log(type);
}
</script>

<style scoped lang="scss">
.equityTemplate-content {
    display: flex;
    flex-direction: row;
    height: calc(100vh - 37px);
}

.equityTemplate-content-left {
    width: 470px;
    min-width: 470px;
    flex: 0 0 470px;
    border-right: 1px solid rgb(188, 205, 224);
    background-color: rgb(239, 244, 250);
    padding: 10px;
    overflow-y: auto;
}

.equityTemplate-add-button {
    height: 38px;
    border: 1px dashed rgb(221, 229, 235);
    border-radius: 2px;
    background-color: #fff;
    width: 100%;
    margin-bottom: 10px;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 120, 212);  
}

.equityTemplate-content-center {
    width: 470px;
    min-width: 470px;
}

.equityTemplate-content-right {
    flex: 1;
    border-left: 1px solid rgb(221, 229, 235);
    background-color: #fff;
}

</style>