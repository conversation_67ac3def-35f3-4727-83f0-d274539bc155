<template>
    <div class="equityTemplate-card">
        <div class="equityTemplate-card-top">
            <!-- icon -->
            <div class="equityTemplate-card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="23px"
                    height="27px">
                    <image x="0px" y="0px" width="23px" height="27px"
                        xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAbCAMAAACgNuTpAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAw1BMVEUVg9gAeNQRgdcEetUDetUAeNQAeNQAeNQAeNQAeNSjzu/P5vf///+z1/JaqORbqORcqeRdquRfquQUhdpAoecIfdYjjt4Whto3m+SU1v8bidxcs+88neQmkN9vvvQRg9lCoucqkt9+u+l9uumr0/FPouGq0vEXhttKp+kZiNtmufInkN4BedQJftc2muQ7nOQDetUSg9kEetUmj95NqOkcidum0PClz/Dz+f0YhdgPgNeHv+vG4fW/3fTn8vtWpuNvs+fwLmP9AAAACXRSTlOs/qn5+CNQVCQ/EHswAAAAAWJLR0QMgbNRYwAAAAd0SU1FB+kHAwADHRUhrA4AAACrSURBVCjPhdDXEoIwFEXR2NAQsSCiWLAXFHuv+P9fZTSFQETW4x7mciYAJOAPSQBVJFMhgCgvQ9+uFT6KJaqs0a5XDKxqGkRNZ71uYY2mRbR4b9tYp2sTPdb7oZED2qP2DEdBY3Z/MhU5wT2cv8eciea8uwuRy/sycGcVcd/v641oG/HfXexOZy86sH4MvcMp5t2o8+VK3ELf3x/E89+dlPeSeWmQUbIyJfcGoDs4hqvW2y0AAAAASUVORK5CYII=" />
                </svg>
            </div>
            <!-- 文字 -->
            <div class="equityTemplate-card-text">
                <p class="equityTemplate-card-text-tit">商业资源元数据权益信息登记表</p>
                <p class="equityTemplate-card-text-p">包含字段数：65</p>
                <p class="equityTemplate-card-text-p">应用数据源：NSTL、Pubmed</p>
                <p class="equityTemplate-card-text-p">更新时间：2025-03-28 15:00:00</p>
            </div>
        </div>
        <div class="equityTemplate-card-btns">
            <span @click="handleClick('delete')" class="btn">删除</span>
            <span @click="handleClick('reuse')" class="btn">复用</span>
            <span @click="handleClick('edit')" class="btn">编辑</span>
        </div>
    </div>
</template>

<script setup>

const emits = defineEmits(['delete', 'reuse', 'edit']);
const handleClick = (type) => {
    emits(type);
};

</script>

<style lang="scss" scoped>
.equityTemplate-card {
    width: 100%;
    margin-top: 10px;
    border: 1px solid rgb(221, 229, 235);
    border-radius: 2px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
}

.equityTemplate-card-top {
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #ccd8e1;
    padding-bottom: 5px;
}
.equityTemplate-card-icon {
    width: 40px;
    height: 40px;
    border: 1px solid rgb(217, 223, 234);
    background-color: rgb(239, 240, 254);
    border-radius: 4px;
    margin: 20px 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.equityTemplate-card-text p {
    margin: 0;
}
.equityTemplate-card-text .equityTemplate-card-text-tit {
  font-size: 16px;
  font-family: "MicrosoftYaHei";
  color: rgb(0, 120, 212);
  font-weight: bold;
  line-height: 1.875;
  margin-top: 16px;
}
.equityTemplate-card-text .equityTemplate-card-text-p {
  font-size: 14px;
  font-family: "Adobe Heiti Std";
  color: rgb(0, 0, 0);
  line-height: 1.714;
}
.equityTemplate-card-btns {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    padding: 4px 0;
}
.equityTemplate-card-btns .btn {
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(0, 120, 212);
  line-height: 2.143;
  margin: 0 12px;
  cursor: pointer;
}


</style>