<template>
    <!-- 权益信息管理 -->
    <viewBody title="期刊篇级数据">
        <template #content>
            <div class="my_menu_content">
                <span class="my_menu_title_left">
                    疑似数据
                </span>
                <!-- isDrawerZoom -->
                <div class="my_menu_title_right" v-show="isDrawerZoom">
                    <span class="btn">更新入库</span>
                    <span class="btn" @click="closeViewAllBox">返回列表</span>
                </div>
            </div>
            <FormSearch 
                v-model="queryParams" 
                :formItems="formItems" 
                searchBtnName="查询" :showResetBtn="false"
                @search="handleQuery" @reset="resetQuery">
                <template #btn>
                    <el-button type="primary" class="my_primary_btn" @click="handleQuery">
                        高级检索</el-button>
                    <el-button type="primary" class="my_primary_btn" @click="handleQuery">
                        规范化</el-button>
                </template>
            </FormSearch>
            <!-- 表格部分 -->
            <!-- :columns="columns" -->
            <!-- :columns="columns.filter(col => checkList?.value?.includes(col.prop))"  -->
            <TableForm ref="tableFormRef" 
                :columns="columns.filter(col => checkList.includes(col.prop))"
                :tableData="dataSourceList" 
                class="tableFormContent"
                v-loading="loading" 
                :showIndex="false" 
                :total="total"
                :isShowCount="false" 
                :showNoteColumn="false" 
                :showOtherColumn="true" tableotherColumnLabel="操作"
                :showEditBtn="false" 
                :isShowSelection="true" 
                :columnsRightWidth="columnsRightWidth" 
                @cellClick="cellClick"
            >
                <!-- 自定义操作按钮 -->
                <template #header>
                    <div class="table-other-column">
                        <div class="table-other-column-label">
                            <span class="table-other-column-label-left">操作</span>
                        </div>
                        <el-popover
                            class="box-item"
                            placement="bottom-end"
                            :show-arrow="false"
                            popper-class="table-other-column-popover"
                            :offset="5"
                        >
                            <template #reference>
                                <svg 
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    width="14px" height="31px">
                                    <image  x="0px" y="0px" width="14px" height="31px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAfBAMAAAAl7OHcAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAJ1BMVEXr9v/r9v/r9v/r9v/r9v/r9v+o0vMXhNgAeNQWhNin0fMZhdn////ZPMzQAAAABXRSTlNJ5udK5GXAZaYAAAABYktHRAyBs1FjAAAAB3RJTUUH6QYXEjkPPoidWQAAADNJREFUCNdjYAwFAQEGUTAdyBAKAUTQYeWpYDq8oxRKTwXTUbOXDoh6/DTEf0EMRmDaGQBk10uB2zyQCgAAAABJRU5ErkJggg==" />
                                </svg>
                            </template>
                            <template #default>
                                <el-checkbox-group v-model="checkList" @change="handleColumnChange">
                                    <el-checkbox v-for="item in columns" :key="item.prop" :value="item.prop" :label="item.label" />
                                </el-checkbox-group>
                            </template>
                        </el-popover>
                    </div>
                </template>
                <template #otherOperation="{ row }">
                    <span v-if="row.id" 
                        @click="handleTableFormQuery(row)"
                        style="margin-left: 10px;vertical-align: middle;color: #0076d0;font-weight: 500;cursor: pointer;"
                    >
                        详情    
                    </span>
                    <span v-if="row.id" 
                        style="margin-left: 10px;vertical-align: middle;color: #0076d0;font-weight: 500;cursor: pointer;"
                    >
                        日志    
                    </span>
                    <span v-if="row.status === '回退'" 
                        style="margin-left: 10px;vertical-align: middle;color: rgb(205, 43, 43);font-weight: 500;"
                    >
                        回退理由    
                    </span>
                </template>

                <template #pagination>
                    <MyPagination
                        v-show="total>0"
                        :total="total"
                        :page="queryParams.pageNum"
                        :pageSize="queryParams.pageSize"
                        @pagination="getList"
                    />
                </template>

                <template #paginationRight>
                    <div class="pagination-right">
                        <el-button class="my_primary_btn white_btn">规范化</el-button>
                    </div>
                </template>

                <template #drawer>
                    <!-- 右侧的抽屉内容 -->
                    <MyDrawer
                        ref="drawerRef"
                        :is-show-right-form="false"
                        :showClose="false"
                        style="padding-top: 0px;"
                        :class="{ 'my_zoom_drawer': isDrawerZoom }"
                    >
                        <!-- <template #header="{ titleId, titleClass }">
                            <div :id="titleId" :class="titleClass" class="my_drawer_title">
                                <span class="active">列表展示</span>
                            </div>
                        </template> -->
                        <template #form v-if="isDrawerZoom">
                            <ZoomTable :tableColumns="tableColumns" :tableTextArr="tableTextArr2"/>
                        </template>
                        <template #form v-if="!isDrawerZoom">
                            <div class="box-container-top-menu">
                                <div class="menu-container">
                                    <span class="menu-title">待处理数据</span>
                                </div>
                                <div class="menu-container">
                                    <span class="menu-title-btn menu-title-btn-gray">上一条</span>
                                    <em class="menu-title-count">1/5</em>
                                    <span class="menu-title-btn" style="margin-right: 0px;">下一条</span>
                                </div>
                            </div>
                            <div class="box-container-content">
                                <SmallTable :tableTextArr="tableTextArr"/>
                                <div class="box-container-content-footer">
                                    <span class="btn">更新入库</span>
                                    <span class="btn" @click="viewAllBox">全部展开</span>
                                    <span class="btn" @click="closeDrawer">关闭</span>
                                </div>
                            </div>
                        </template>
                    </MyDrawer>
                </template>

            </TableForm>
        </template>
    </viewBody>
</template>
<script setup>
import viewBody from '@/components/view/viewBody.vue';
import FormSearch from '@/views/source/components/FormSearch.vue';
import TableForm from '@/views/source/components/TableForm.vue';
import MyDrawer from '@/views/source/components/MyDrawer.vue';
import MyPagination from "@/components/Pagination/new.vue";
import SmallTable from './LeftTable.vue';
import ZoomTable from './ZoomTable.vue'

const {proxy} = getCurrentInstance();
// 数据源采集
const {
  storage_pub_type,
} = proxy.useDict(
    "storage_pub_type"
);
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    name: '',
    type: '',
})
// 搜索表单配置
const formItems = computed(() => [
    {
        label: '篇级名称',
        prop: 'name',
        component: 'el-input',
        props: {
            placeholder: '请输入篇级名称',
            clearable: true,
            style: { width: '200px' },
        }
    },
    {
        label: '数据源',
        prop: 'type',
        component: 'el-select',
        props: {
            placeholder: '请选择数据源',
            clearable: true,
            style: { width: '200px' },
        },
        children: () => storage_pub_type.value.map(dict => ({
            value: dict.value,
            label: dict.label
        }))
    }
])
const handleQuery = (params) => {
    Object.assign(queryParams, params)
}
const resetQuery = () => {}

const getList = (page) => {
    queryParams.pageNum = page.page
    queryParams.pageSize = page.limit
    console.log(page)
}
// 表格配置
let total = ref(400)
const loading = ref(false)
const small = ref(true)
const tableFormRef = ref(null)
const columnsRightWidth = ref("200")
// 表格列配置
const columns = ref([
    { prop: 'id', label: '篇级ID', width: '120', fixed: true },
    { prop: 'name', label: '篇级题名', minWidth: '120', fixed: true },
    { prop: 'type', label: '期刊题名', minWidth: '100', fixed: true },
    { prop: 'issn', label: 'ISSN' },
    { prop: 'yeaissvol', label: '年/卷/期' },
    { prop: 'status', label: '数据状态', width: '100', type: 'textCircle' },
    { prop: 'describes', label: '是否有引文', width: '100' },
    { prop: 'createTime', label: '更新时间', width: '200' },
])
/**
 * 表格数据
 */
const dataSourceList = ref([
    {
        id: 123, 
        name: '数据源名称333数据源名称333',
        type: '文献类型',
        issn: 'issn-111',
        yeaissvol: '2020/1/1',  
        status: '正常',
        textCircleFailedVal: '回退',
        createTime: '2025-01-01 00:00:00',
        describes: '414',
    }, {
        id: 456,
        name: '数据源名称44',
        type: '文献类型',
        issn: 'issn-222',
        yeaissvol: '2020/1/1',
        status:'回退',
        textCircleFailedVal: '回退',
        createTime: '2025-01-01 00:00:00',
        describes: '999',
    }, 
    { id: 1,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 2,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 3,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 4,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 5,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 6,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 7,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 8,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 9,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 10,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 1,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 12,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
    { id: 13,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00',describes: '999', },
])
// total.value = dataSourceList.value.length
/**
 *  单元格点击事件
 * @param row  
 * @param column 
 * @param cellValue 
 * @param event 
 */
const drawerRef = ref(null)
const cellClick = (row, column, cellValue, event) => {
    // console.log(row, column, cellValue, event)
}
const checkList = ref(columns.value.map(col => col.prop))
const handleColumnChange = () => {
  // 当checkList变化时自动更新显示的列
  console.log(columns.value.filter(col => checkList?.value?.includes(col.prop)))
}
/**
 *  表格行点击事件
 * @param row 
 */
const handleTableFormQuery = (row) => {
    console.log(row)
    drawerRef.value.openDrawer();
}
// 关闭抽屉
const closeDrawer = () => {
    drawerRef.value.closeDrawer();
}
// 展开
const isDrawerZoom = ref(false)
const viewAllBox = () => {
    isDrawerZoom.value = true;
}
const closeViewAllBox = () => {
    isDrawerZoom.value = false;
}

const tableTextArr = ref([{
        leftText: 'Source ld：',
        righttext: 'AAN'
    }, {
        leftText: 'Source Title：',
        righttext: 'Cells Tissues Organs'
    }, {
        leftText: 'ISSN：',
        righttext: '1422-6405'
    }, {
        leftText: 'Publisher Name：',
        righttext: 'S. Karger AG'
    }, {
        leftText: 'Pub Year：',
        righttext: '1998'
    }, {
        leftText: 'Volume：',
        righttext: '1'
    }, {
        leftText: 'Issue：',
        righttext: '1'
    }, {
        leftText: 'Start Page：',
        righttext: '1'
    }, {
        leftText: 'End Page：',
        righttext: '1'
    }, {
        leftText: 'DOI：',
        righttext: '10.1016/j.jbi.2019.01.004'
    }
])
const tableColumns = ref([
    { name: 'Source ld' },
    { name: 'Source Title' },
    { name: 'ISSN' },
    { name: 'Publisher Name' },
    { name: 'Pub Year' },
    { name: 'Volume' },
    { name: 'Issue' },
    { name: 'Start Page' },
    { name: 'End Page' },
    { name: 'DOI' },
])
const tableTextArr2 = ref([
    { "id": 1,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 2,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 3,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 4,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 5,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 6,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
])
</script>
<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../../source/components/base.scss";

.my_menu_content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.my_menu_title_right {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 120, 212);

    & > span {
        border: 1px solid rgb(219, 219, 219);
        border-radius: 2px;
        background-color: rgb(255, 255, 255);
        padding: 3px 10px;
        margin-right: 10px;
        cursor: pointer;
    }
}


:deep(.el-drawer__header) {
  padding: 14px 16px 10px 16px;
  margin: 0;
  border-bottom: 1px solid rgb(232, 232, 232);
}
:deep(.el-drawer__body) {
    padding: 0;
    padding-top: 10px;
}
:deep(.drawer-form) {
    padding: 0px;
}
:deep(.drawer-content-body) {
    height: 100%;
}
.my_primary_btn {
  background-color: #0078d4;
  border-color: #0078d4;
}

:deep(.my_zoom_drawer) {
    width: calc(100% - 10px) !important;
}

.table-other-column {
    display: flex;
    align-items: center;
    justify-content: center;

    .table-other-column-label {
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -18px;
        margin-top: -14px;
        display: flex;
        align-items: center;
        justify-content: center;    
    }

    .table-other-column-label-left {
        margin-top: 2.5px;
    }

    :deep(.el-tooltip__trigger) {
        display: inline-block;
        width: 28px;
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -15.5px;
        height: 31px;
    }

    svg {
        position: relative;
        cursor: pointer;
    }
}
</style>
<style lang="scss">
.table-other-column-popover {
    width: auto!important;
    min-width: 113px !important;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
    border-radius: 4px;

    .el-checkbox-group {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .el-checkbox.el-checkbox--small {
        margin: 0;
    }

    .el-checkbox__label {
        font-size: 14px !important;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 0, 0);
    }

    .el-checkbox.el-checkbox--small .el-checkbox__inner {
        width: 14px!important;
        height: 14px!important;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #000 !important;
        font-weight: 700;
    }
}
</style>