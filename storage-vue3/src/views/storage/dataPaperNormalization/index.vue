<template>
    <div class="app-container">
        <!-- 返回按钮 -->
        <div class="my_menu_content">
            <span class="my_menu_title_left">
                <svg 
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                width="7px" height="12px">
                <path fill-rule="evenodd"  fill="rgb(0, 120, 212)"
                d="M5.134,11.302 L0.688,6.618 C0.442,6.359 0.442,5.939 0.688,5.681 C0.740,5.625 0.802,5.588 0.866,5.556 C0.896,5.489 0.931,5.424 0.984,5.368 L5.430,0.684 C5.676,0.425 6.074,0.425 6.320,0.684 C6.565,0.943 6.565,1.362 6.320,1.621 L2.022,6.149 L6.023,10.365 C6.269,10.624 6.269,11.043 6.023,11.302 C5.778,11.561 5.380,11.561 5.134,11.302 Z"/>
                </svg>疑似数据详情</span>
        </div>
        <!-- 内容区域 -->
        <div class="content-container">
            <!-- 左侧 -->
            <div class="left-container">
                <div class="box-container-top-menu">
                    <div class="menu-container">
                        <span class="menu-title">待处理数据</span>
                        <span class="menu-title-btn menu-title-btn-gray">上一条</span>
                        <em class="menu-title-count">1/60</em>
                        <span class="menu-title-btn">下一条</span>
                    </div>
                    <div class="menu-container">
                        <div class="menu-container-icon">
                            <span class="active"><svg 
                                xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                width="15px" height="16px">
                                <image  x="0px" y="0px" width="15px" height="16px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAQCAMAAAD+iNU2AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAaVBMVEX///8AeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNT///8OUPGdAAAAIXRSTlMAi/DhFMD2sNMw4HAOEAVQgCh+kC3SS4ygMuQggrwsOwPdnBduAAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kHARY0ArqMFoYAAABZSURBVAjXdc7XCoAwEETRtcSSWGLvJf//k7oiQwh63i7MwpLnW4KQRBRDYlISkkC9neWsQEvDSrTSrEK79+6+blj7u++e53r0MLLpez8vsN69GdtOh7ao8wKExwuaS1bqiwAAAABJRU5ErkJggg==" />
                                </svg>
                            </span>
                            <span>
                                <svg 
                                xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                width="18px" height="14px">
                                <image  x="0px" y="0px" width="18px" height="14px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAOCAMAAAAVBLyFAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAsVBMVEX///9obHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHj///9Z97Z2AAAAOXRSTlMAw6o2aw8pk/sUAzXJ6AwF67GE+k79W01VFdPwB2z4muc5ZT14sgbB3wEJIIFvdnslLZ/axzdqHUBgxgkKAAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kHARY0K/g+juoAAACHSURBVBjTXc9FEsMwDAXQn5QpBafglCllRt3/YpU9ajxjLaSvN1rYQFZBCK9ylHdLoWh6qVyp1v5Sb0RAs9VGR8Ui3R6PPg2gE2siCIcjiAU0treTqRl6puaIVaI5L2hpaEXrFGKb7S4Ta/sDHTmdRNjOuNCVw+2eutc+nv5nXvT26fON3PIDEGQLdepGWNgAAAAASUVORK5CYII=" />
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="box-container-content">
                    <LeftTable :tableTextArr="tableTextArr"></LeftTable>
                    <div class="box-container-content-footer">
                        <span class="btn">新增</span>
                        <span class="btn">挂接</span>
                        <span class="btn">删除</span>
                    </div>
                </div>
            </div>
            <!-- 右侧 -->
            <div class="right-container">
                <RightTable></RightTable>
            </div>
        </div>
    </div>
</template>
<script setup>
import LeftTable from "./components/LeftTable.vue"
import RightTable from "./components/RightTable.vue"


const tableTextArr = ref([{
        leftText: 'Source ld：',
        righttext: 'AAN'
    }, {
        leftText: 'Source Title：',
        righttext: 'Cells Tissues Organs'
    }, {
        leftText: 'ISSN：',
        righttext: '1422-6405'
    }, {
        leftText: 'Publisher Name：',
        righttext: 'S. Karger AG',
        className: 'right-text-class' // 通过添加类名，改变样式
    }, {
        leftText: 'Pub Year：',
        righttext: '1998'
    }, {
        leftText: 'Volume：',
        righttext: '1'
    }, {
        leftText: 'Issue：',
        righttext: '1'
    }, {
        leftText: 'Start Page：',
        righttext: '1'
    }, {
        leftText: 'End Page：',
        righttext: '1'
    }, {
        leftText: 'DOI：',
        righttext: '10.1016/j.jbi.2019.01.004'
    }
])
</script>

<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../source/components/base.scss";

.content-container {
    display: flex;
    width: 100%;
    flex-direction: row;

    $left-container-width: 470px;
    $right-container-width: calc(100% - $left-container-width);
    .left-container {
        width: $left-container-width;
        min-width: $left-container-width;
        flex-basis: $left-container-width;
        border-right: 1px solid #bccde0;
    }

    :deep(.box-container-top-menu) {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 40px;
        padding: 0 10px;
        background-color: #fff;
    }
    :deep(.menu-container) {
        font-size: 14px;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 120, 212);

        .menu-title {
            display: inline-block;
            height: 40px;
            line-height: 40px;
            border-bottom: 2px solid #0078d4;
        }

        .menu-title-btn {
            margin: 0 15px;
            padding: 4px 13px;
            border-radius: 4px;
            background-color: #fff;
            border-width: 1px;
            border-color: rgb(219, 219, 219);
            border-style: solid;
            cursor: pointer;
            box-sizing: border-box;
        }
        .menu-title-btn-gray {
            color: rgb(153, 152, 152);
            background-color: rgb(231, 235, 245);
            border-color: rgb(231, 235, 245);
        }

        .menu-title-count {
            font-size: 14px;
            font-family: "MicrosoftYaHei";
            color: rgb(0, 0, 0);
            font-style: normal;
        }

        .menu-container-icon {
            border: 1px solid rgb(219, 219, 219);
            border-radius: 4px;
            background-color: #e7ebf5;
            width: 60px;
            height: 25px;
            display: flex;
            flex-direction: row;
            align-items: center;
            overflow: hidden;

            span {
                min-width: 50%;
                width: 50%;
                height: 100%;
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: #e7ebf5;

                &.active {
                    background-color: #fff;
                }
            }
        }

    }

    :deep(.box-container-content) {
        width: 100%;
        height: 100%;
        padding: 10px;
        background-color: #eff4fa;
    }
    :deep(.box-container-content-footer) {
        text-align: right;
        span.btn {
            border-width: 1px;
            border-color: rgb(219, 219, 219);
            border-style: solid;
            border-radius: 2px;
            background-color: rgb(255, 255, 255);
            font-size: 14px;
            font-family: "MicrosoftYaHei";
            color: rgb(0, 120, 212);
            padding: 3px 8px;
            margin-left: 4px;
            cursor: pointer;
        }


    }
    :deep(.smalltabel-content .smalltabel-content-col-left) {
        width: 109px;
        min-width: 109px;
        text-align: left;
        padding-left: 14px;
    }
    :deep(.smalltabel-content .right-text-class .smalltabel-content-col-right) {
        background-color: #f9e3de;
    }


    .right-container {
        flex: 1;
        width: $right-container-width;

        :deep(.drawer-content-new) {
            width: 470px;
        }
    }
}
:deep(.my_menu_content) {
    height: 40px;
    border-bottom: 1px solid #bccde0;
    padding-left: 17px;
    .my_menu_title_left {
        font-size: 14px;
        line-height: 40px;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 120, 212);
        cursor: pointer;

        svg {
            margin-right: 10px;
        }
    }
}

:deep(.table-container) {
    height: calc(100vh - 37px - 44px - 40px - 40px);
}
</style>