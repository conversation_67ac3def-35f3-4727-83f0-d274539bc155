<template>
    <div class="container">
        <!-- 表格 -->
        <div class="smalltabel-content">
            <el-table
                :data="tableTextArr"
                style="width: 100%"
                border
            >
                <el-table-column
                    v-for="item in tableColumns"
                    :key="item.prop"
                    :prop="item.prop"
                    :label="item.label"
                    :width="item.width"
                    :minWidth="item.minWidth"
                >
                    <template #default="scope">
                        <slot name="operation" :row="scope.row" :prop="item.prop"></slot>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    tableColumns: {
        type: Array,
        default: () => ([]),
    },
    tableTextArr: {
        type: Object,
        default: () => ([]),
    },
})
</script>

<style lang="scss" scoped>
.container {
    border: 1px solid #ced1d2;
    margin-bottom: 16px;
}

.smalltabel-content {
    display: flex;
    flex-direction: column;

    .smalltabel-content-col {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-bottom: 1px solid #e8e8e8;

        &:last-child {
            border-bottom: none;
        }
    }
}
.container :deep(.el-table--small .el-table__header-wrapper th) {
    background-color: #f7f7f7 !important;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 0, 0);
    font-weight: bold;
    line-height: 1.857;
    text-align: center;
    padding: 0;
}
.container :deep(.el-table--small .cell) {
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(0, 0, 0);
  line-height: 2.857;
}
</style>