<template>
    <!-- 权益信息管理 -->
    <viewBody title="期刊篇级数据">
        <template #content>
            <FormSearch
                v-model="queryParams"
                :formItems="formItems"
                searchBtnName="查询"
                @search="handleQuery"
                @reset="resetQuery"
            >
            </FormSearch>
            <!-- 表格部分 -->
            <TableForm ref="tableFormRef" 
                :columns="columns.filter(col => checkList.includes(col.prop))"
                :tableData="dataSourceList" 
                class="tableFormContent"
                v-loading="loading" 
                :showIndex="false" 
                :total="total"
                :isShowCount="false" 
                :showNoteColumn="false" 
                :showOtherColumn="true" tableotherColumnLabel="操作"
                :showEditBtn="false" 
                :isShowSelection="true" 
                :columnsRightWidth="columnsRightWidth" 
                @cellClick="cellClick"
            >
                <!-- 自定义操作按钮 -->
                <template #header>
                    <div class="table-other-column">
                        <div class="table-other-column-label">
                            <span class="table-other-column-label-left">操作</span>
                        </div>
                        <el-popover
                            class="box-item"
                            placement="bottom-end"
                            :show-arrow="false"
                            popper-class="table-other-column-popover"
                            :offset="5"
                        >
                            <template #reference>
                                <svg 
                                    xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                    width="14px" height="31px">
                                    <image  x="0px" y="0px" width="14px" height="31px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAfBAMAAAAl7OHcAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAJ1BMVEXr9v/r9v/r9v/r9v/r9v/r9v+o0vMXhNgAeNQWhNin0fMZhdn////ZPMzQAAAABXRSTlNJ5udK5GXAZaYAAAABYktHRAyBs1FjAAAAB3RJTUUH6QYXEjkPPoidWQAAADNJREFUCNdjYAwFAQEGUTAdyBAKAUTQYeWpYDq8oxRKTwXTUbOXDoh6/DTEf0EMRmDaGQBk10uB2zyQCgAAAABJRU5ErkJggg==" />
                                </svg>
                            </template>
                            <template #default>
                                <el-checkbox-group v-model="checkList" @change="handleColumnChange">
                                    <el-checkbox v-for="item in columns" :key="item.prop" :value="item.prop" :label="item.label" />
                                </el-checkbox-group>
                            </template>
                        </el-popover>
                    </div>
                </template>
                <template #otherOperation="{ row }">
                    <span v-if="row.id" 
                        @click="handleTableFormQuery(row)"
                        style="margin-left: 10px;vertical-align: middle;color: #0076d0;font-weight: 500;cursor: pointer;"
                    >
                        详情    
                    </span>
                    <span v-if="row.id" 
                        style="margin-left: 10px;vertical-align: middle;color: #0076d0;font-weight: 500;cursor: pointer;"
                    >
                        通过    
                    </span>
                    <span v-if="row.id" 
                        style="margin-left: 10px;vertical-align: middle;color: rgb(205, 43, 43);font-weight: 500;"
                    >
                        删除    
                    </span>
                </template>

                <template #pagination>
                    <MyPagination
                        v-show="total>0"
                        :total="total"
                        :page="queryParams.pageNum"
                        :pageSize="queryParams.pageSize"
                        @pagination="getList"
                    />
                </template>

                <template #drawer>
                    <!-- 右侧的抽屉内容 -->
                    <MyDrawer
                        ref="drawerRef"
                        :is-show-right-form="false"
                    >
                        <template #header="{ titleId, titleClass }">
                            <div :id="titleId" :class="titleClass" class="my_drawer_title">
                                <span @click="changeBox('tt1')" :class="{'active': drawChangeName=='tt1'}">校验详情</span>
                            </div>
                        </template>
                        <template #form>
                            <div v-show="drawChangeName=='tt1'">
                                <SmallTable 
                                    :tableColumns="tableColumns"
                                    :tableTextArr="tableTextArr">
                                    <!-- 如果 row[prop] 等于 内容为空，则 字体红色 -->
                                    <template #operation="{ row, prop }">
                                        <span v-if="row[prop] == '内容为空'">
                                            <span style="color: #cd2b2b;">{{row[prop]}}</span>
                                        </span>
                                        <span v-else>
                                            {{row[prop]}}
                                        </span>
                                    </template>
                                </SmallTable>
                            </div>
                        </template>
                    </MyDrawer>
                </template>
            </TableForm>
        </template>
    </viewBody>
</template>
<script setup>
import viewBody from '@/components/view/viewBody.vue';
import FormSearch from '@/views/source/components/FormSearch.vue';
import TableForm from '@/views/source/components/TableForm.vue';
import MyDrawer from '@/views/source/components/MyDrawer.vue';
import MyPagination from "@/components/Pagination/new.vue";
import SmallTable from './SmallTable.vue';

const {proxy} = getCurrentInstance();
// 数据源采集
const {
  storage_pub_type,
} = proxy.useDict(
    "storage_pub_type"
);
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    name: '',
    type: '',
})
// 搜索表单配置
const formItems = computed(() => [
    {
        label: '篇级名称',
        prop: 'name',
        component: 'el-input',
        props: {
            placeholder: '请输入篇级名称',
            clearable: true,
            style: { width: '200px' },
        }
    },
    {
        label: '数据源',
        prop: 'type',
        component: 'el-select',
        props: {
            placeholder: '请选择数据源',
            clearable: true,
            style: { width: '200px' },
        },
        children: () => storage_pub_type.value.map(dict => ({
            value: dict.value,
            label: dict.label
        }))
    }
])
const handleQuery = (params) => {
    Object.assign(queryParams, params)
}
const resetQuery = () => {}

const getList = (page) => {
    queryParams.pageNum = page.page
    queryParams.pageSize = page.limit
    console.log(page)
}
// 表格配置
let total = ref(400)
const loading = ref(false)
const tableFormRef = ref(null)
const columnsRightWidth = ref("150")
// 表格列配置
const columns = ref([
    { prop: 'id', label: '篇级ID', width: '120', fixed: true },
    { prop: 'name', label: '篇级题名', minWidth: '120', fixed: true },
    { prop: 'type', label: '期刊题名', minWidth: '100', fixed: true },
    { prop: 'issn', label: 'ISSN' },
    { prop: 'yeaissvol', label: '年/卷/期' },
    { prop: 'status', label: '校验结果', width: '100', type: 'textCircle', textCircle: { 'success': '通过', 'failed': '不通过'} },
    { prop: 'handleStatus', label: '处理状态', width: '100', type: 'textCircle', textCircle: { 'success': '已处理', 'warning': '待处理', 'failed': '失败'} },
    { prop: 'createTime', label: '更新时间', width: '200' },
])
/**
 * 表格数据
 */
const dataSourceList = ref([
    {
        id: 123, 
        name: '数据源名称333数据源名称333',
        type: '文献类型',
        issn: 'issn-111',
        yeaissvol: '2020/1/1',  
        status: '正常',
        handleStatus: '待处理',
        textCircleFailedVal: '回退',
        createTime: '2025-01-01 00:00:00',
    }, {
        id: 456,
        name: '数据源名称44',
        type: '文献类型',
        issn: 'issn-222',
        yeaissvol: '2020/1/1',
        status:'回退',
        handleStatus: '失败',
        textCircleFailedVal: '回退',
        createTime: '2025-01-01 00:00:00',
    }, 
    { id: 1,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '待处理',createTime: '2025-01-01 00:00:00' },
    { id: 2,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '待处理',createTime: '2025-01-01 00:00:00' },
    { id: 3,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '待处理',createTime: '2025-01-01 00:00:00' },
    { id: 4,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '待处理',createTime: '2025-01-01 00:00:00' },
    { id: 5,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '待处理',createTime: '2025-01-01 00:00:00' },
    { id: 6,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '待处理',createTime: '2025-01-01 00:00:00' },
    { id: 7,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '已处理',createTime: '2025-01-01 00:00:00' },
    { id: 8,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '已处理',createTime: '2025-01-01 00:00:00' },
    { id: 9,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退', handleStatus: '已处理',createTime: '2025-01-01 00:00:00' },
    { id: 10,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00' },
    { id: 1,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00' },
    { id: 12,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00' },
    { id: 13,name: '数据源名称44',type: '文献类型',issn: 'issn-222',yeaissvol: '2020/1/1',status:'回退',createTime: '2025-01-01 00:00:00' },
])
// total.value = dataSourceList.value.length
/**
 *  单元格点击事件
 * @param row  
 * @param column 
 * @param cellValue 
 * @param event 
 */
const drawerRef = ref(null)
const cellClick = (row, column, cellValue, event) => {
    // console.log(row, column, cellValue, event)
}
const drawChangeName = ref('tt1')
const changeBox = (name) => {
    drawChangeName.value = name
}
const checkList = ref(columns.value.map(col => col.prop))
const handleColumnChange = () => {
  // 当checkList变化时自动更新显示的列
  console.log(columns.value.filter(col => checkList?.value?.includes(col.prop)))
}
/**
 *  表格行点击事件
 * @param row 
 */
const handleTableFormQuery = (row) => {
    console.log(row)
    drawerRef.value.openDrawer();
}

const tableColumns = ref([
    { prop: 'leftText', label: '元数据字段项', width: '110' },
    { prop: 'middletext', label: '字段内容', minWidth: '190' },
    { prop: 'righttext', label: '校验结果详情', width: '148' },
])
const tableTextArr = ref([
    { leftText: 'Source Title :', middletext: 'Obesity Facts', righttext: '是必填、非重复' },
    { leftText: 'Volume:', middletext: '17', righttext: '是必填、非重复' }, 
    { leftText: 'lssue:', middletext: '4', righttext: '是必填、非重复' }, 
    { leftText: 'ArticleTitle:', middletext: '', righttext: '内容为空' }, 
    { leftText: 'Language:', middletext: 'eng', righttext: '是必填、非重复' },
    { leftText: 'Elocationld:', middletext: '1234568484641', righttext: '非必填、非重复' },
    { leftText: 'PubYear:', middletext: '2024/12/13', righttext: '是必填、非重复' },
    { leftText: 'Elocation:', middletext: 'US', righttext: '是必填、非重复' },
    { leftText: 'Author-FullName:', middletext: 'J.Schutzbach', righttext: '是必填、非重复' },
    { leftText: 'Keyword :', middletext: 'Glycoproteins;0-glycans', righttext: '是必填、非重复' },
])
</script>
<style lang="scss" scoped>
:deep(.el-drawer__header) {
  padding: 14px 16px 10px 16px;
  margin: 0;
  border-bottom: 1px solid rgb(232, 232, 232);
}
:deep(.el-drawer__body) {
    padding: 0;
    padding-top: 10px;
}
.my_primary_btn {
  background-color: #0078d4;
  border-color: #0078d4;
}

.table-other-column {
    display: flex;
    align-items: center;
    justify-content: center;

    .table-other-column-label {
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -18px;
        margin-top: -14px;
        display: flex;
        align-items: center;
        justify-content: center;    
    }

    .table-other-column-label-left {
        margin-top: 2.5px;
    }

    :deep(.el-tooltip__trigger) {
        display: inline-block;
        width: 28px;
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -15.5px;
        height: 31px;
    }

    svg {
        position: relative;
        cursor: pointer;
    }
}
</style>
<style lang="scss">
.table-other-column-popover {
    width: auto!important;
    min-width: 113px !important;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
    border-radius: 4px;

    .el-checkbox-group {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .el-checkbox.el-checkbox--small {
        margin: 0;
    }

    .el-checkbox__label {
        font-size: 14px !important;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 0, 0);
    }

    .el-checkbox.el-checkbox--small .el-checkbox__inner {
        width: 14px!important;
        height: 14px!important;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #000 !important;
        font-weight: 700;
    }
}
</style>