<template>
    <div class="container">
        <!-- 表格 -->
        <div class="smalltabel-content">
            <div class="smalltabel-content-col" v-for="item in tableTextArr" :key="item.leftText">
                <div class="smalltabel-content-col-left">{{ item.leftText }}</div>
                <div class="smalltabel-content-col-right">{{ item.righttext }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    tableTextArr: {
        type: Object,
        default: () => ([]),
    },
})
</script>

<style lang="scss" scoped>
.container {
    border: 1px solid #ced1d2;
    margin-bottom: 16px;
}

.smalltabel-content {
    display: flex;
    flex-direction: column;

    .smalltabel-content-col {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-bottom: 1px solid #e8e8e8;

        &:last-child {
            border-bottom: none;
        }
    }

    .smalltabel-content-col-left {
        width: 154px;
        max-width: 154px;
        min-width: 154px;
        font-size: 14px;
        padding: 10px 0;
        background-color: #f7f7f7;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 0, 0);
        font-weight: bold;
        text-align: right;
    }

    .smalltabel-content-col-right {
        font-size: 14px;
        padding: 10px 0;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 0, 0);
        flex: 1;
        border-left: 1px solid #e8e8e8;
        padding-left: 6px;
        background-color: #fff;
    }
}
</style>