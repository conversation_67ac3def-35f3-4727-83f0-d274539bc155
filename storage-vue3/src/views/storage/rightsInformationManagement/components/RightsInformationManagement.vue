<template>
  <!-- 权益信息管理 -->
  <viewBody title="权益信息管理">
    <template #content>
      <FormSearch
        v-model="queryParams"
        :formItems="formItems"
        searchBtnName="查询"
        @search="handleQuery"
        @reset="resetQuery"
      >
      </FormSearch>
      <!-- 表格部分 -->
      <TableForm
        ref="tableFormRef"
        :columns="columns"
        :tableData="dataSourceList"
        class="tableFormContent"
        v-loading="loading"
        :show-index="false"
        :total="total"
        :isShowCount="false"
        :show-edit-btn="false"
        :show-note-column="false"
        :isShowSelection="true"
        :sortableItems="sortableItems"
        :columnsRightWidth="columnsRightWidth"
        @cellClick="cellClick"
      >
        <template #pagination>
            <MyPagination
                v-show="total>0"
                :total="total"
                :page="queryParams.pageNum"
                :pageSize="queryParams.pageSize"
                @pagination="getList"
            />
        </template>
        <template #paginationRight>
            <div class="pagination-right">
                <el-button class="my_primary_btn white_btn">数据抽检</el-button>
                <el-button class="my_primary_btn white_btn">回退数据</el-button>
            </div>
        </template>
        <template #drawer>
            <!-- 右侧的抽屉内容 -->
            <MyDrawer ref="drawerRef" :is-show-right-form="false">
                <!-- <template #header="{ titleId, titleClass }">
                <div :id="titleId" :class="titleClass" class="my_drawer_title">
                    <span
                    @click="changeBox('tt1')"
                    :class="{ active: drawChangeName == 'tt1' }"
                    >风险等级判定字段</span
                    >
                    <span
                    @click="changeBox('tt2')"
                    :class="{ active: drawChangeName == 'tt2' }"
                    >应对策略</span
                    >
                </div>
                </template> -->
                <template #header>
                <div class="my_drawer_title">
                    <span
                    @click="changeBox('tt1')"
                    :class="{ active: drawChangeName == 'tt1' }"
                    >风险等级判定字段</span
                    >
                    <span
                    @click="changeBox('tt2')"
                    :class="{ active: drawChangeName == 'tt2' }"
                    >应对策略</span
                    >
                </div>
                </template>
                <template #form>
                <div v-show="drawChangeName == 'tt1'">
                    <SmallTable :tableTextArr="tableTextArr" />
                    <SmallTable
                    tableLeftText="存储风险等级"
                    tableRightIconType="warning"
                    tableRightIconText="中风险"
                    :tableTextArr="tableTextArr"
                    />
                    <SmallTable
                    tableLeftText="存储风险等级"
                    tableRightIconType="error"
                    tableRightIconText="高风险"
                    :tableTextArr="tableTextArr"
                    />
                </div>
                <div v-show="drawChangeName == 'tt2'">
                    <SmallTable
                    tableLeftText="数据源风险等级"
                    tableRightIconType="warning"
                    tableRightIconText="中风险"
                    :tableTextArr="tableTextArr"
                    />
                </div>
                </template>
            </MyDrawer>
        </template>
        <template #footerTable>
            <div class="footer-table">
                <Table
                    :columns="columnsFooter"
                    :tableData="dataSourceFooterList"
                    :show-index="false"
                    :show-edit-btn="false"
                    :show-note-column="false"
                ></Table>
            </div>
        </template>
      </TableForm>
    </template>
  </viewBody>
</template>
<script setup>
import viewBody from "@/components/view/viewBody.vue";
import FormSearch from "@/views/source/components/FormSearch.vue";
import TableForm from "@/views/source/components/TableForm.vue";
import Table from "@/views/source/components/Table.vue";
import MyPagination from "@/components/Pagination/new.vue";
import MyDrawer from "@/views/source/components/MyDrawer.vue";
import SmallTable from "./SmallTable.vue";

const { proxy } = getCurrentInstance();

// 数据源采集
const { storage_pub_type } = proxy.useDict("storage_pub_type");
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  articleLevel: '1',
  articleLevelValue: '',
  articleLevelName: '1',
  articleLevelNameName: '',
  updateTime: '1',
  updateTimeValue: '',
});
const pagerCount = ref(8);
// 搜索表单配置
const formItems = computed(() => [
  {
    label: "篇级",
    prop: "articleLevel",
    component: "el-select",
    props: {
      placeholder: "",
      clearable: false,
      style: { width: "94px" },
    },
    children: () => {
        return [{
            label: '篇级ID', value: '1'
        }, {
            label: '篇级DD', value: '2'
        }]
    },
    position: 'left_wrapper'
  },
  {
    label: "篇级值",
    prop: "articleLevelValue",
    component: "el-input",
    props: {
      placeholder: "请输入",
      clearable: true,
      style: { width: "206px" },
    },
    position: 'right_wrapper'
  },
  {
    label: "篇级题名",
    prop: "articleLevelName",
    component: "el-select",
    props: {
      placeholder: "",
      clearable: false,
      style: { width: "94px" },
    },
    children: () => {
        return [{
            label: '篇级题名', value: '1'
        }, {
            label: '篇级名', value: '2'
        }]
    },
    position: 'left_wrapper'
  },
  {
    label: "篇级题名名称",
    prop: "articleLevelNameName",
    component: "el-select",
    props: {
      placeholder: "请选择",
      clearable: false,
      style: { width: "206px" },
    },
    children: () => {
        return [{
            label: '篇级题名名称1', value: '1'
        }, {
            label: '篇级题名名称2', value: '2'
        }]
    },
    position: 'right_wrapper'
  },
  {
    label: "更新时间",
    prop: "updateTime",
    component: "el-select",
    props: {
      placeholder: "",
      clearable: false,
      style: { width: "94px" },
    },
    children: () => {
        return [{
            label: '更新时间', value: '1'
        }, {
            label: '创新时间', value: '2'
        }]
    },
    position: 'left_wrapper'
  },
  {
    label: "时间",
    prop: "updateTimeValue",
    component: "el-date-picker",
    props: {
      placeholder: "",
      clearable: false,
      style: { width: "206px" },
    },
    position: 'right_wrapper'
  },
]);
// 表单点击查询
const handleQuery = (params) => {
    Object.assign(queryParams, params)
}
// 重置表单
const resetQuery = () => {
};

const getList = (page) => {
    console.log(page);
};
// 表格配置
let total = ref(400);
const loading = ref(false);
const tableFormRef = ref(null);
const columnsRightWidth = ref("200");
// 表格列配置
const columns = ref([
  { prop: "articleLevel", label: "篇级ID", width: "150", fixed: true },
  { prop: "articleLevelName", label: "篇级题名", width: "218", fixed: true },
  { prop: "varietyName", label: "品种名称", minWidth: "215" },
  { prop: "dataSourceName", label: "数据源", minWidth: "85" },
  { prop: "dataSourceType", label: "数据源", minWidth: "73" },
  { prop: "dataSourceCollectRisk", label: "采集风险", minWidth: "100" },
  { prop: "dataSourceStorageRisk", label: "储存风险", minWidth: "100" },
  { prop: "dataSourceRightsItem", label: "服务权项", minWidth: "95" },
  { prop: "dataSourceRightsRisk", label: "服务风险", minWidth: "100" },
  { prop: "createTime", label: "更新时间", width: "168" },
]);
const columnsFooter = ref([
  { prop: "dataSourceId", label: "唯一标识", width: "167", fixed: true, style: 'color: rgb(0, 120, 212);' },
  { prop: "dataSourceName", label: "数据来源", width: "100" },
  { prop: "articleLevelName", label: "篇级题名" },
  { prop: "journalTitle", label: "期刊题名" },
  { prop: "issn", label: "ISSN", width: "137" },
  { prop: "yearVolumeIssue", label: "年/卷/期", width: "122" },
  { prop: "createTime", label: "更新时间", width: "186" },
])
// 可排序字段
const sortableItems = ['dataSourceCollectRisk', 'dataSourceStorageRisk', 'dataSourceRightsRisk', 'createTime']
/**
 * 表格数据
 */
const dataSourceList = ref([
  {
    id: 123,
    articleLevel: "FJA01230718aik9E0",
    articleLevelName: "Glycoproteins and Their Rela tions to Disease",
    varietyName: "JMIR Pediatrics and Parenting",
    dataSourceName: "0000-8888",
    dataSourceType: "Kager",
    dataSourceCollectRisk: "高",
    dataSourceStorageRisk: "中",
    dataSourceRightsItem: "网络传播",
    dataSourceRightsRisk: "高",
    createTime: "2025-01-01 13:26:00",
  },
  {
    id: 234,
    articleLevel: "FJA01230718aik9E0",
    articleLevelName: "Glycoproteins and Their Rela tions to Disease",
    varietyName: "JMIR Pediatrics and Parenting",
    dataSourceName: "0000-8888",
    dataSourceType: "Kager",
    dataSourceCollectRisk: "高",
    dataSourceStorageRisk: "中",
    dataSourceRightsItem: "网络传播",
    dataSourceRightsRisk: "高",
    createTime: "2025-01-01 13:26:00",
  },
  {
    id: 456,
    articleLevel: "FJA01230718aik9E0",
    articleLevelName: "Glycoproteins and Their Rela tions to Disease",
    varietyName: "JMIR Pediatrics and Parenting",
    dataSourceName: "0000-8888",
    dataSourceType: "Kager",
    dataSourceCollectRisk: "高",
    dataSourceStorageRisk: "中",
    dataSourceRightsItem: "网络传播",
    dataSourceRightsRisk: "高",
    createTime: "2025-01-01 13:26:00",
  },
  {
    id: 567,
    articleLevel: "FJA01230718aik9E0",
    articleLevelName: "Glycoproteins and Their Rela tions to Disease",
    varietyName: "JMIR Pediatrics and Parenting",
    dataSourceName: "0000-8888",
    dataSourceType: "Kager",
    dataSourceCollectRisk: "高",
    dataSourceStorageRisk: "中",
    dataSourceRightsItem: "网络传播",
    dataSourceRightsRisk: "高",
    createTime: "2025-01-01 13:26:00",
  },
]);
const dataSourceFooterList = ref([
  {
    id: 1,
    dataSourceId: 'SJA2025NJ13001',
    dataSourceName: "0000-8888",
    articleLevelName: "Glycoproteins and Their Rela tions to Disease",
    journalTitle: "JMIR Pediatrics and Parenting",
    issn: "1535-317X",
    yearVolumeIssue: "2018;37(3):e1513",
    createTime: "2025-01-01 13:26:00",
  },
  {
    id: 2,
    dataSourceId: 'SJA2025NJ13001',
    dataSourceName: "0000-8888",
    articleLevelName: "Glycoproteins and Their Rela tions to Disease",
    journalTitle: "JMIR Pediatrics and Parenting",
    issn: "1535-317X",
    yearVolumeIssue: "2018;37(3):e1513",
    createTime: "2025-01-01 13:26:00",
  },
  {
    id: 3,
    dataSourceId: 'SJA2025NJ13001',
    dataSourceName: "0000-8888",
    articleLevelName: "Glycoproteins and Their Rela tions to Disease",
    journalTitle: "JMIR Pediatrics and Parenting",
    issn: "1535-317X",
    yearVolumeIssue: "2018;37(3):e1513",
    createTime: "2025-01-01 13:26:00",
  }
])
// total.value = dataSourceList.value.length;
/**
 *  单元格点击事件
 * @param row
 * @param column
 * @param cellValue
 * @param event
 */
const drawerRef = ref(null);
const cellClick = (row, column, cellValue, event) => {
//   console.log(row, column, cellValue, event)
  if (column.label === "篇级ID") {
    drawerRef.value.openDrawer();
  }
};
const tableTextArr = ref([
  {
    leftText: "等级含义：",
    righttext: "开展元数据采集时，几乎不会发生权益问题或纠纷纠纷纠纷纠纷",
  },
  {
    leftText: "建议工作方式：",
    righttext:
      "优先按协议方提供数据方式，接收数据。如无接口，则根据许可协议要求进行网络采集，抓取频率须符合网站规定",
  },
  {
    leftText: "风险规避方式：",
    righttext: "无",
  },
  {
    leftText: "其他说明：",
    righttext: "直接纳入公开服务范围",
  },
]);

const drawChangeName = ref("tt1");
const changeBox = (name) => {
  drawChangeName.value = name;
};
</script>
<style lang="scss" scoped>
:deep(.el-drawer__header) {
  padding: 14px 16px 10px 16px;
  margin: 0;
  border-bottom: 1px solid rgb(232, 232, 232);
}
:deep(.el-drawer__body) {
  padding-top: 13px;
}
.my_primary_btn {
  background-color: #0078d4;
  border-color: #0078d4;
}

.white_btn {
  border: 1px solid rgb(219, 219, 219);
  background-color: #fff;
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(33, 146, 233);
}
.pagination-right {
    margin-right: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.my_drawer_title {
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  font-family: "MicrosoftYaHei";
  color: rgb(102, 102, 102);
  display: flex;
  gap: 40px;
  padding-left: 20px;

  span {
    cursor: pointer;
  }

  .active {
    color: #0078d4;
    border-bottom: 2px solid #0078d4;
  }
}

.footer-table {
    border-top: 1px solid #bccde0;
    padding: 10px;
}
</style>
