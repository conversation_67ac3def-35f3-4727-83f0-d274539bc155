<template>
    <div class="container">
        <div class="smalltabel-top-title" :class="'smalltabel-top-title-'+tableRightIconType">
            <div class="smalltabel-top-title-left" :class="'smalltabel-top-title-left-'+tableRightIconType">
                {{ tableLeftText }}
            </div>
            <div class="smalltabel-top-title-right" :class="'smalltabel-top-title-right-'+tableRightIconType">
                {{ tableRightIconText }}
                <template v-if="tableRightIconType == 'warning'">
                    <svg 
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="16px" height="16px">
                        <image  x="0px" y="0px" width="16px" height="16px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAhFBMVEXzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggDzggD2pUf//Pj2pUj4tGb////4s2T3q1P3qlL2oT/2oD71mCz1lyv0jhn0jhj2oD31mzL85Mj2nDZ6XnOsAAAAGXRSTlMAKo7X+NYGlP2YCbu8lSv8j/nz1JAs2Pr0HauPCgAAAAFiS0dEHnIKICsAAAAHdElNRQfpBhQXDB+VcCKOAAAAh0lEQVQY022P6RKCMAyEF4qABwWkVasY7/v938+SZBxndP9k+02b3QJRSWqybJQmEOVFGVjleMLnafholkdQhS8VgK0Hs970DGqLhs2WdnKlgeG5p4MAg5bnkU4CWgVnugiY65Mr3QR0ujTcNdfBc+zj+ZJYr8V67VH9q/7zuSjrFstV5/zg3wDjE4AkQRCsAAAAAElFTkSuQmCC" />
                    </svg>
                </template>
                <template v-if="tableRightIconType == 'error'">
                    <svg 
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    width="16px" height="15px">
                    <image  x="0px" y="0px" width="16px" height="15px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAPCAMAAADarb8dAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAsVBMVEUAAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAADTAAD///9Psq/1AAAAOXRSTlMAEs/MDo6HIvr3HKegNP4uwDaKuUzRB+pEBNRyXgJk+xcyYF0L5rXI4Qh97tB8dRbzMPCVSY3tq6ISzz5iAAAAAWJLR0Q6TgnE+gAAAAd0SU1FB+kGFBcNCXi/pp4AAACOSURBVAjXTY3nEoJAEIMD9oa9YcFeQFFB0bz/i3m3N+LlT/bLZCeAyHELRdgqkWWbK1WyVreCBpWaf25pptfOgw7ZRY/9Hw/IIUZjcmJ46pMzzNWTu5BgSQYrrDdqeqt5tycPungkT2d1hKobXUJc9VIM3O6yGT8CbYmDVJhP3/gLmTnexpghjRKRJxx8vq0qGnM1A/9pAAAAAElFTkSuQmCC" />
                    </svg>
                </template>
                <template v-if="tableRightIconType == 'success'">
                    <svg 
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    width="16px" height="16px">
                    <image  x="0px" y="0px" width="16px" height="16px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAgVBMVEUAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEcAkEdCrXe64c0CkUj2+/mk170Ekkrf8ehbuIml2L4DkUmHy6j9/v6Iy6n3/PmByKSY0rX///9G2B4LAAAAGXRSTlMAKo7X+NYGlP2YCbu8lSv8j/nz1JAs2Pr0HauPCgAAAAFiS0dEKlO+1J4AAAAHdElNRQfpBhQXDDegxYp0AAAAgklEQVQY022P2RKCMBAEB4KABwEkUccbFY///0E3hErh0Q+p7anKZgIIUaySZBJH8KRZzp58Out9zsAilaDgiAzQ5TgoNaog292erKCCH45yKtTk6dw6v1wlqF3AW9cOzqW/cu8e3tkMS58v7zSwn8/ar2LFv+o/nxO0Wa03jbFufgMMhhOQGSzf3QAAAABJRU5ErkJggg==" />
                    </svg>
                </template>
                <slot name="icon"></slot>
            </div>
        </div>
        <!-- 表格 -->
        <div class="smalltabel-content">
            <div class="smalltabel-content-col" v-for="item in tableTextArr" :key="item.leftText">
                <div class="smalltabel-content-col-left">{{ item.leftText }}</div>
                <div class="smalltabel-content-col-right">{{ item.righttext }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    tableRightIconType: {
        type: String,
        default: 'success',
    },
    tableLeftText: {
        type: String,
        default: '采集风险等级',
    },
    tableRightIconText: {
        type: String,
        default: '低风险',
    },
    tableTextArr: {
        type: Object,
        default: () => ([]),
    },
})
</script>

<style lang="scss" scoped>
.container {
    border: 1px solid #ced1d2;
    margin-bottom: 16px;
    background: #fff;
}

.smalltabel-top-title {
    width: 100%;
    height: 40px;
    background-color: #c7f9d6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: #000;
    font-weight: bold;
    padding-left: 12px;
    padding-right: 9px;

    &-warning {
        background-color: #ffe3c2;
    }
    &-error {
        background-color: #ffd2cd;
    }

    .smalltabel-top-title-left {
        font-size: 14px;
        font-family: "MicrosoftYaHei";
        color: #009047;
        font-weight: bold;

        &-warning {
            color: #f16000;
        }
        &-error {
            color: #c60011;
        }
    }

    .smalltabel-top-title-right {
        color: #009047;
        display: flex;
        flex-direction: row;
        align-items: center;

        &-warning {
            color: #f38200;
        }
        &-error {
            color: #dc4e5a;
        }

        svg {
            width: 16px;
            height: 16px;
            margin-left: 9px;
        }
    }
}

.smalltabel-content {
    display: flex;
    flex-direction: column;

    .smalltabel-content-col {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-bottom: 1px solid #e8e8e8;

        &:last-child {
            border-bottom: none;
        }
    }

    .smalltabel-content-col-left {
        width: 125px;
        max-width: 125px;
        min-width: 125px;
        font-size: 14px;
        padding: 10px 0;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 0, 0);
        font-weight: bold;
        text-align: right;
        background-color: #f7f7f7;
    }

    .smalltabel-content-col-right {
        font-size: 14px;
        padding: 10px 0;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 0, 0);
        flex: 1;
        border-left: 1px solid #e8e8e8;
        padding-left: 11px;
        background-color: #fff;
    }
}
</style>