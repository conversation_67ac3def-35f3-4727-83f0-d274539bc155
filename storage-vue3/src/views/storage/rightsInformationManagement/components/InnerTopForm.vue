<template>
  <div class="inner-top-form">
    <!-- <QueryCondition
      :leftOptions="[
        { value: 'name', label: '篇级ID' },
        { value: 'age', label: '篇级ID2' },
      ]"
      :leftProps="{ defaultValue: 'name' }"
      rightComponent="el-input"
      @query="handleQuery2"
    />
    <QueryCondition
      :leftOptions="[
        { value: 'status', label: '篇级题名' },
        { value: 'gender', label: '篇级题名-1' },
      ]"
      :leftProps="{ defaultValue: 'status' }"
      rightComponent="el-select"
      :rightProps="{
        options: [
          { value: 'male', label: '男' },
          { value: 'female', label: '女' },
        ],
      }"
      @query="handleQuery2"
    />
    <QueryCondition
      :leftOptions="[
        { value: 'date', label: '更新时间' },
        { value: 'date2', label: '更新时间-1' },
      ]"
      :leftProps="{ defaultValue: 'date' }"
      rightComponent="el-date-picker"
      :rightProps="{ type: 'date', placeholder: '选择日期' }"
      @query="handleQuery2"
    /> -->
    <!-- <QueryCondition
      :leftOptions="[{ value: 'hobbies', label: '爱好' }]"
      rightComponent="el-checkbox-group"
      :rightProps="{
        options: [
          { value: 'reading', label: '阅读' },
          { value: 'traveling', label: '旅行' },
        ],
        defaultValue: [],
      }"
      @query="handleQuery2"
    /> -->
    <template v-for="(item,index) in QueryConditionData" :key="index">
      <QueryCondition
        :leftOptions="item.leftOptions"
        :leftProps="item.leftProps"
        :rightComponent="item.rightComponent"
        :rightProps="item.rightProps"
        @query="handleQuery2"
        :ref="el => queryConditionRefs[index] = el"
      ></QueryCondition>
    </template>
  </div>
</template>

<script setup>
import QueryCondition from "@/components/QueryCondition/index.vue";

const queryConditionRefs = ref([]);

const QueryConditionData = [{
  leftOptions: [
    { value: 'name', label: '篇级ID' },
    { value: 'age', label: '篇级ID2' },
  ],
  leftProps:{ defaultValue: 'name' },
  rightComponent: 'el-input',
}, {
  leftOptions: [
    { value: 'status', label: '篇级题名' },
    { value: 'gender', label: '篇级题名-1' },
  ],
  leftProps:{ defaultValue: 'status' },
  rightComponent: 'el-select',
  rightProps: {
    options: [
      { value: 'male', label: '男' },
      { value: 'female', label: '女' },
    ],
  },
}, {
  leftOptions: [
    { value: 'date', label: '更新时间' },
    { value: 'date2', label: '更新时间-1' },
  ],
  leftProps:{ defaultValue: 'date' },
  rightComponent: 'el-date-picker',
  rightProps: { type: 'date', placeholder: '选择日期' },
}]

const handleQuery2 = (query) => {
  console.log("查询条件:", query);
};

defineExpose({
  reset() {
    queryConditionRefs.value.forEach(ref => {
      ref.reset();
    })
  }
});




</script>

<style scoped>
.inner-top-form {
    display: inline-block;
}
</style>