<template>
  <!-- 数据源采集 -->
  <viewBody title="数据源采集">
    <template #content>
      <FormSearch
        v-model="queryParams"
        :formItems="formItems"
        :sys_normal_disable="storage_data_type"
        @search="handleQuery"
        @reset="resetQuery"
      ></FormSearch>
      <!-- 表格部分 -->
      <TableForm
        ref="tableFormRef"
        :columns="columns"
        :tableData="dataSourceList"
        v-loading="loading"
        :showIndex="false"
        :showOtherColumn="true" tableotherColumnLabel="操作"
        :total="total"
        :sortableItems="sortableItems"
        :columnsRightWidth="columnsRightWidth"
        @submitForm="submitForm"
      >
        <!-- 自定义操作按钮 -->
        <template #otherOperation="{ row }">
          <span style="margin-left: 10px;vertical-align: middle;color: #0076d0;font-weight: 500;cursor: pointer;"
            @click="handleEdit(row)"
          >
            编辑
          </span>
          <span style="margin-left: 10px;vertical-align: middle;color: #0076d0;font-weight: 500;cursor: pointer;">
            日志
          </span>
          <span 
            v-if="!row.status"
            style="margin-left: 10px;vertical-align: middle;color: rgb(205, 43, 43);font-weight: 500;cursor: pointer;">
            失败原因
          </span>
        </template>
        <!-- 翻页 -->
        <template #pagination>
          <MyPagination
              v-show="total>0"
              :total="total"
              :page="queryParams.pageNum"
              :pageSize="queryParams.pageSize"
              @pagination="getList"
          />
        </template>
        <!-- 右侧抽屉，插入内容 -->
        <template #drawer>
          <MyDrawer ref="drawerRef">
            <template #header="{ titleId, titleClass }">
                <div :id="titleId" :class="titleClass" class="my_drawer_title">
                    <span class="active">{{ rightFormTitle }}</span>
                    <div class="my_drawer_title_right">
                      <span class="btn_add">保存</span>
                      <span class="btn_cancel">取消</span>
                    </div>
                </div>
            </template>
            <template #form>
                <RightDrawForm></RightDrawForm>
            </template>
          </MyDrawer>
        </template>
        <!-- 底部 -->
        <template #footerTable>
            <div class="footer-table" style="height: 50%;overflow: hidden;">
                <Details></Details>
            </div>
        </template>
      </TableForm>
    </template>
  </viewBody>
</template>

<script setup>
import viewBody from "@/components/view/viewBody.vue";
import FormSearch from "../../components/FormSearch.vue";
import TableForm from "../../components/TableForm.vue";
import MyPagination from "@/components/Pagination/new.vue";
import MyDrawer from '@/views/source/components/MyDrawer.vue';
import RightDrawForm from './RightDrawForm.vue';
import Details from './Details.vue';

const { proxy } = getCurrentInstance();

// 数据源采集
const { storage_data_type, storage_pub_type } = proxy.useDict(
  "storage_data_type",
  "storage_pub_type"
);

const drawerRef = ref(null)

// 查询参数
// 名称、类型、采集状态、收割批次
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: "",
  type: "",
  status: "",
  batch: "",
});
let total = ref(0);
const loading = ref(false);
const tableFormRef = ref(null);
const columnsRightWidth = ref("200");
// 搜索表单配置
const formItems = computed(() => [
  {
    label: "数据源名称",
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "请输入数据源名称",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    label: "数据类型",
    prop: "type",
    component: "el-select",
    props: {
      placeholder: "请选择数据类型",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      storage_pub_type.value.map((dict) => ({
        value: dict.value,
        label: dict.label,
      })),
  },
  {
    label: "采集状态",
    prop: "status",
    component: "el-select",
    props: {
      placeholder: "请选择采集状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      storage_pub_type.value.map((dict) => ({
        value: dict.value,
        label: dict.label,
      })),
  },
  {
    label: "收割批次",
    prop: "batch",
    component: "el-select",
    props: {
      placeholder: "请选择收割批次",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      storage_pub_type.value.map((dict) => ({
        value: dict.value,
        label: dict.label,
      })),
  },
]);
// 表格列配置
const columns = ref([
  { prop: "index", label: "编号", width: "120", fixed: true },
  { prop: "name", label: "数据源名称", width: "120", fixed: true },
  { prop: "type", label: "文献类型", width: "100", fixed: true },
  {
    prop: "status",
    label: "采集状态",
    width: "100",
    type: "switch",
    activeText: "启用",
    inactiveText: "停用",
  },
  {
    prop: "sourceType_dict",
    label: "最后一次收割任务状态",
    width: "200",
    type: "textCircle",
  },
  { prop: "createTime", label: "最后一次任务结束时间", width: "200" },
  {
    prop: "describes",
    label: "总任务",
    width: "100",
    style: "color: rgb(0, 120, 212);",
  },
]);
// 可排序字段
const sortableItems = ["index", "createTime"];
// 表格数据
const dataSourceList = ref([
  {
    index: 1,
    name: "数据源名称",
    type: "文献类型",
    status: true,
    sourceType_dict: "成功",
    textCircleFailedVal: "失败",
    createTime: "2025-01-01 00:00:00",
    describes: "212",
  },
  {
    index: 2,
    name: "数据源名称",
    type: "文献类型",
    status: false,
    sourceType_dict: "失败",
    textCircleFailedVal: "失败",
    createTime: "2025-01-01 00:00:00",
    describes: "444",
  },
]);
total.value = dataSourceList.value.length;
// 搜索按钮操作
const handleQuery = (params) => {
  queryParams.pageNum = 1;
  Object.assign(queryParams, params);
};
// 重置查询条件
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    name: "",
    type: "",
    status: "",
    batch: "",
  });
};
// 提交表单
const submitForm = async (formData) => {
  console.log("提交表单数据:", formData);
};
// 查询数据源列表
const getList = async () => {
  loading.value = false;
};

// 新增按钮操作
const rightFormTitle = ref('编辑数据源');
const handleEdit = () => {
  drawerRef.value.openDrawer();
}
</script>

<style lang="scss" scoped>
.footer-table {
  padding: 0;
}
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}
.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

</style>