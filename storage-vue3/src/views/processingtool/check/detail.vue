<template>
  <div class="app-container">
    <el-tabs v-model="tabMode" class="tabs">
      <el-tab-pane label="内容详情" name="0">
        <el-card>
          <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
          >
            <el-form-item v-if="!showDetail" label="路径" prop="path">
              <el-input
                v-model="queryParams.path"
                placeholder="路径"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item
              v-if="!showDetail"
              label="元素名称"
              prop="elementName"
            >
              <el-input
                v-model="queryParams.elementName"
                placeholder="元素名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item v-if="!showDetail" label="元素集" prop="elementSet">
              <el-input
                v-model="queryParams.elementSet"
                placeholder="元素集"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item v-if="!showDetail" label="启用状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="启用状态"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="dict in sysDict.storage_use_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="showDetail"
              label="字段名称"
              prop="attributeName"
            >
              <el-input
                v-model="queryParams.attributeName"
                placeholder="字段名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item v-if="showDetail" label="优先级" prop="priority">
              <el-select
                v-model="queryParams.priority"
                placeholder="优先级"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="dict in sysDict.storage_rule_check_priority"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                v-if="showDetail"
                type="primary"
                plain
                icon="Plus"
                @click="handleNewCheckItem()"
                >新建校验项
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="showDetail"
                type="danger"
                plain
                icon="Delete"
                @click="handleDelete()"
                :disabled="multiple"
                >删除校验项
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="!showDetail"
                type="success"
                plain
                icon="Check"
                @click="handlePass()"
                >通过</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="!showDetail"
                type="danger"
                plain
                icon="Close"
                @click="handleReject()"
                >拒绝</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="Promotion"
                @click="handleSave"
                :disabled="true"
                >保存规则
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="!showDetail"
                type="primary"
                plain
                icon="Plus"
                @click="handleRuleAndPath()"
                >查看全部规则
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="Position"
                @click="handleSend"
                :disabled="multiple"
                >发送规则
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="Download"
                @click="handleExport"
                >导出规则</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="showDetail"
                type="success"
                plain
                icon="Check"
                @click="handleRuleAndPath()"
                >查看路径列表
              </el-button>
            </el-col>
            <right-toolbar
              v-model:showSearch="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>

          <!-- 左右分栏布局 -->
          <el-row :gutter="20" v-if="!showDetail">
            <!-- 左侧表格 -->
            <el-col :span="16">
              <el-table
                v-loading="loading"
                :data="ruleCheckList"
                highlight-current-row
                :max-height="tableHeight"
                @row-click="handleRowClick"
                @selection-change="handleSelectionChange"
                ref="ruleCheckTableRef"
              >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column
                  type="index"
                  align="center"
                  label="序号"
                  width="60"
                />
                <el-table-column
                  label="路径"
                  align="center"
                  prop="path"
                  width="240"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="元素集"
                  align="center"
                  prop="elementSet"
                  width="100"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="元素名称"
                  align="center"
                  prop="elementName"
                  width="100"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="属性值"
                  align="center"
                  prop="attributes"
                  width="100"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="校验项数量"
                  align="center"
                  prop="itemNum"
                  width="100"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="审核结果"
                  align="center"
                  prop="checkStatus"
                  width="100"
                >
                  <template #default="scope">
                    <dict-tag
                      :options="sysDict.storage_rule_check_status"
                      :value="scope.row.checkStatus"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="启用状态"
                  align="center"
                  key="status"
                  prop="status"
                  width="100"
                  :show-overflow-tooltip="true"
                >
                  <template #default="scope">
                    <el-switch
                      v-model="scope.row.status"
                      active-value="Y"
                      inactive-value="N"
                      @change="handleUseStatusChange(scope.row)"
                      @click.stop
                    ></el-switch>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>

            <!-- 右侧操作框 -->
            <el-col :span="8">
              <DetailPanel
                :data="selectData"
                :height="tableHeight"
                title="Title"
              />
            </el-col>
          </el-row>

          <!-- 详情模式下的表格保持原样 -->
          <el-row :gutter="20" v-if="showDetail">
            <el-col :span="24">
              <el-table
                v-if="showDetail"
                v-loading="loading"
                :data="ruleCheckDetailList"
                highlight-current-row
                :max-height="tableHeight"
                @selection-change="handleSelectionChange"
                ref="ruleCheckDetailTableRef"
              >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column type="index" align="center" label="序号" />
                <el-table-column
                  label="字段名称"
                  align="center"
                  prop="fieldName"
                  width="100"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="检验项"
                  align="center"
                  prop="checkItem"
                  width="140"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="条件类型"
                  align="center"
                  prop="checkType"
                  width="140"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="条件值/表达式"
                  align="center"
                  prop="expression"
                  width="140"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="提示语"
                  align="center"
                  prop="prompt"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="优先级"
                  align="center"
                  prop="priority"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="备注"
                  align="center"
                  prop="remark"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="操作"
                  width="120"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template #default="scope">
                    <el-button
                      @click="handleEdit(scope.row)"
                      type="text"
                      size="small"
                      >编辑</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <modalRuleForm :modal="ruleModal" @ok="getList" />
  </div>
</template>

<script setup name="RuleAnalysis">
import { watch, ref, getCurrentInstance } from "vue";
import DetailPanel from "../components/DetailPanel/index.vue";

const { proxy } = getCurrentInstance();
import { useRoute } from "vue-router";
import {
  STORAGE_RULE_CHECK_URL,
  listRuleCheck,
  updateApi,
  updateCheckStatusApi,
  queryRuleCheckSelect,
} from "@/api/processingtool/ruleCheck.js";
// 全部规则新增弹窗
import modalRuleForm from "./modal/ruleModal.vue";
import {
  listRuleCheckDetail,
  deleteRuleCheckDetail,
} from "@/api/processingtool/ruleCheckDetail.js";

const route = useRoute();
const ruleCheckList = ref([]);
const ruleCheckDetailList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const open = ref(false);
const total = ref(0);
const multiple = ref(true);
const ids = ref([]);
const selectRows = ref([]);
const dateRange = ref([]);
const showDetail = ref(false);
// 选中的数据
const selectData = ref({});
// 编辑状态
const isEditing = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ruleId: route.query.ruleId,
    ruleValidId: route.query.ruleValidId,
    orderAsc: true,
    orderByCol: "sort",
  },
  rules: {},
});

const { queryParams } = toRefs(data);

/**数据字典*/
const sysDict = reactive({
  ...proxy.useDict(
    "storage_use_status",
    "storage_rule_check_status",
    "storage_rule_check_priority",
    "storage_rule_check_type"
  ),
});

/**业务字典*/
const bizDict = reactive({ ruleCheckSelectOptions: [] });

const tabMode = ref("0");

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  if (!showDetail.value) {
    const { data } = await listRuleCheck(queryParams.value);
    ruleCheckList.value = data.records;
    total.value = data.total;
  } else {
    const { data } = await listRuleCheckDetail(queryParams.value);
    ruleCheckDetailList.value = data.records;
    total.value = data.total;
  }
  loading.value = false;
}

function handleRuleAndPath() {
  showDetail.value = !showDetail.value;
  resetQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    STORAGE_RULE_CHECK_URL + "/export",
    {
      ...queryParams.value,
    },
    `规则路径数据_${new Date().getTime()}.xlsx`
  );
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id || item.pathId);
  selectRows.value = selection;
  multiple.value = selection.length === 0;
}

// 选中当前行
function handleRowClick(row) {
  console.log("选中的规则数据:", row);
  selectData.value = row;
  // 重置编辑状态
  isEditing.value = false;
  // 更新表格选中状态
  proxy.$refs.ruleCheckTableRef.setCurrentRow(row);
}

// 监听selectData变化
watch(
  selectData,
  (newData) => {
    console.log("selectData changed:", newData);
  },
  { deep: true }
);
// ============================== 业务函数处理 ===============================

// 弹窗数据
const ruleModal = reactive({
  isEdit: false,
  title: "",
  open: false,
  form: {
    fieldName: "",
  },
  dict: {
    sysDict,
    bizDict,
  },
});

// 通用操作方法
async function handleUseStatusChange(row) {
  console.log("应用状态改变", row);
  let text = row.status === "Y" ? "启用" : "停用";
  const confirmRes = await proxy.$modal
    .confirm("确认要" + text + "吗?")
    .catch(() => {});
  if (!confirmRes) {
    undoChange(row);
    return;
  }
  const updateRes = await updateApi(row).catch(() => {
    undoChange(row);
  });
  if (!updateRes) return;
  proxy.$modal.msgSuccess(text + "成功");

  // 后续可能调整字典的数据值，这里暂时不处理
  function undoChange(row) {
    row.status = row.status === "Y" ? "N" : "Y";
  }
}

// 新建规则
async function handleNewCheckItem() {
  console.log("新建校验项-------");
  ruleModal.isEdit = false;
  ruleModal.open = true;
  ruleModal.title = "新建校验项";
  ruleModal.form = {
    ruleId: route.query.ruleId,
    ruleValidId: route.query.ruleValidId,
    pathId: "",
  };
}

function handleEdit(row) {
  console.log("编辑校验项", row);
  ruleModal.isEdit = true;
  ruleModal.open = true;
  ruleModal.title = "新建编辑校验项校验项";
  ruleModal.form = row;
}

// 删除规则
async function handleDelete(row) {
  const confirmRes = await proxy.$modal
    .confirm("确认要删除选中的字段规则吗?")
    .catch(() => {});
  if (!confirmRes) return;
  const localIds = row ? [row.id] : ids.value;
  await deleteRuleCheckDetail(localIds);
  proxy.$modal.msgSuccess("删除成功");
  await getList();
}

// 保存规则
async function handleSave() {
  // 实现保存规则功能
  console.log("保存规则");
  proxy.$modal.msgSuccess("保存成功");
}

// 发送规则
async function handleSend() {
  // 实现发送规则功能
  console.log("发送规则");
  proxy.$modal.msgSuccess("发送成功");
}

async function handlePass(row) {
  console.log("通过");
  const confirmRes = await proxy.$modal
    .confirm("确认要通过当前校验规则吗?")
    .catch(() => {});
  if (!confirmRes) return;
  const localIds = row ? [row.id] : ids.value;
  const data = {
    ruleId: route.query.ruleId,
    ids: localIds,
    checkStatus: "PASS",
  };
  await updateCheckStatusApi(data);
  proxy.$modal.msgSuccess("通过成功");
  await getList();
}

async function handleReject(row) {
  console.log("拒绝");
  const confirmRes = await proxy.$modal
    .confirm("确认要拒绝当前校验规则吗?")
    .catch(() => {});
  if (!confirmRes) return;
  const localIds = row ? [row.id] : ids.value;
  const data = {
    ruleId: route.query.ruleId,
    ids: localIds,
    checkStatus: "REJECT",
  };
  await updateCheckStatusApi(data);
  proxy.$modal.msgError("拒绝成功");
  await getList();
}

/**获取校验规则路径字典信息*/
async function getRuleCheckSelect() {
  console.log("校验规则ID", route.query.ruleId);
  const { data } = await queryRuleCheckSelect({
    ruleId: route.query.ruleId,
    status: "Y",
  });
  console.log("获取校验规则路径字典信息", data);
  bizDict.ruleCheckSelectOptions = data;
}

// 获取添加路径对应规则时的路径选择信息
getRuleCheckSelect();

const tableHeight = ref(null);
onMounted(() => {
  watch(
    showSearch,
    async (value) => {
      if (value) {
        await nextTick(() => {
          tableHeight.value =
            window.innerHeight - proxy.$refs.queryRef.$el.clientHeight - 174;
        });
      } else {
        tableHeight.value = window.innerHeight - 84;
      }
    },
    { immediate: true }
  );
});

getList();
</script>

<style lang="scss" scoped>
// 表格行选中样式优化
:deep(.el-table__row) {
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }

  &.current-row {
    background-color: #ecf5ff;
  }
}
</style>
