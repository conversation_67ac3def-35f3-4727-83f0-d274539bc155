<template>
  <div class="app-container">
    <ViewBody title="著录规范化工具详情">
      <template #content>
        <!-- 搜索菜单 -->
        <FormSearch
          v-model="queryParams"
          :formItems="formItems"
          @search="handleQuery"
          @reset="resetQuery"
        >
        </FormSearch>

        <!-- 表格部分 -->
        <TableForm
          ref="ruleTableFormRef"
          :columns="columns"
          :tableData="dataList"
          :total="total"
          v-loading="loading"
          :isShowSelection="true"
          :isShowSearchQuery="false"
          :showIndex="true"
          @selection-change="handleSelectionChange"
          @handleClick="handleClick"
          @cellClick="handleCellClick"
        >
          <template #pagination>
            <Pagination
              v-if="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </template>
        </TableForm>
      </template>
    </ViewBody>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { useRoute } from "vue-router";
import { listRulNormalization } from "@/api/processingtool/ruleNormalization.js";
import {
  FormSearch,
  TableForm,
  DrawerForm,
  ViewBody,
  Pagination,
} from "@/components/Business";

const { proxy } = getCurrentInstance();
const route = useRoute();

// 响应式数据
const dataList = ref([]);
const loading = ref(true);
const total = ref(0);
const dateRange = ref([]);

const currentIndex = ref(0);

// 表单引用
const drawerRef = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    orderAsc: true,
    orderByCol: "sort",
    path: "",
    elementName: "",
    elementSet: "",
    ruleId: route.query.ruleId,
    ruleValidId: route.query.ruleValidId,
  },
  rules: {},
});

const { queryParams } = toRefs(data);

// 系统字典
const sysDict = reactive({
  ...proxy.useDict("storage_use_status", "storage_analysis_status"),
});

// 搜索表单配置
const formItems = ref([
  {
    prop: "path",
    component: "el-input",
    props: {
      placeholder: "元素路径",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "elementName",
    component: "el-input",
    props: {
      placeholder: "元素名称",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "elementSet",
    component: "el-select",
    props: {
      placeholder: "元素集",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_use_status,
  },
]);

// 表格列配置
const columns = ref([
  { prop: "path", label: "元素路径", minWidth: "240" },
  { prop: "elementSet", label: "元素集", minWidth: "120" },
  { prop: "elementName", label: "元素名称", minWidth: "120" },
  { prop: "attributeName", label: "属性名称", minWidth: "150" },
  { prop: "attributeCount", label: "属性值数量", minWidth: "180" },
  { prop: "attributeValue", label: "属性值", minWidth: "180" },
]);

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  try {
    const { data } = await listRulNormalization(queryParams.value);
    // 处理数据字典映射
    dataList.value = data?.records?.map((row) => {
      return {
        ...row,
        ...mapRuleDictLabels(row),
      };
    });
    total.value = data.total;
  } catch (error) {
    console.error("获取规则分析列表失败:", error);
    proxy.$modal.msgError("获取规则分析列表失败");
  } finally {
    loading.value = false;
  }
}

// 获取字典标签（优化版本）
const getDictLabel = (options, value) => {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
};

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  drawerRef.value.closeDrawer();
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  queryParams.value.path = "";
  queryParams.value.elementName = "";
  queryParams.value.elementSet = "";
  queryParams.value.pageSize = 20;
  queryParams.value.orderAsc = true;
  queryParams.value.orderByCol = "sort";
  queryParams.value.ruleId = route.query.ruleId;
  queryParams.value.ruleValidId = route.query.ruleValidId;
  handleQuery();
}
// 初始化
onMounted(() => {
  console.log("获取路由参数==>", route.query);
  queryParams.value.ruleId = route.query.ruleId;
  queryParams.value.ruleValidId = route.query.ruleValidId;
});

getList();
</script>

<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "@/components/Business/style/base.scss";
</style>
