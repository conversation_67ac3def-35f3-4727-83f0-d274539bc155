<template>
  <div class="form-container">
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120px"
      size="small"
      show-message
      :inline-messages="true"
      label-position="left"
    >
      <el-form-item label="词表名称" prop="wordName">
        <el-input
          :disabled="modal.isEdit || modal.isLook"
          v-model="form.wordName"
          placeholder="词表名称"
        />
      </el-form-item>
      <el-form-item label="属性名称" prop="attributeName">
        <el-input
          :disabled="modal.isEdit || modal.isLook"
          v-model="form.attributeName"
          placeholder="属性名称"
        />
      </el-form-item>
      <el-form-item label="交互类型" prop="interactiveType">
        <el-select
          filterable
          v-model="form.interactiveType"
          placeholder=""
          :disabled="modal.isEdit || modal.isLook"
        >
          <el-option
            v-for="dict in sysDict.storage_interactive_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="词表文件" prop="sampleFile">
        <FileUpload
          :disabled="modal.isLook"
          v-model="form.sampleFile"
          :action="'/file/common/upload'"
          :limit="1"
          :fileSize="20"
          :fileType="['csv', 'xlsx']"
          :fileList="[]"
          :isShowTip="true"
          :data="uploadParams"
          @upload-change="handleUploadChange"
          :uploadTip="customUploadTip"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref, computed, getCurrentInstance } from "vue";
import {
  updateRuleNormalBase,
  replaceFileApi,
  addNormalWordApi,
} from "@/api/processingtool/ruleNormalization.js";

const { proxy } = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  },
});
const emit = defineEmits(["ok"]);
const customUploadTip = ref("样例文件仅用于规则生成，文件及处理结果均不入库");
/**字典*/
const { sysDict } = props.modal.dict;

// 需要进行数组转换的字段
const arrayField = computed(() => {
  return props.modal.arrayField;
});

const form = computed({
  get() {
    return props.modal.form;
  },
  set() {},
});

const rules = reactive({
  wordName: [
    { required: true, message: "词表名称不能为空", trigger: "change" },
  ],
  attributeName: [
    { required: true, message: "属性名称不能为空", trigger: "change" },
  ],
  interactiveType: [
    { required: true, message: "交互类型不能为空", trigger: "change" },
  ],
  sampleFile: [
    { required: true, message: "词表文件不能为空", trigger: "change" },
  ],
});

const uploadParams = ref({
  projectPath: "storage/rule/normalWord",
  isSpecifiedFile: true,
});

function handleUploadChange(fileList) {
  console.log("handleUploadChange", fileList);
  if (!fileList || fileList.length === 0) {
    return;
  }
  form.value.fileList = fileList;
  console.log(form.value.fileList);
}

// 处理数组字段转换为字符串（保存时使用）
const processArrayToString = (data) => {
  const processedData = { ...data };
  if (arrayField.value) {
    arrayField.value.forEach((field) => {
      if (processedData[field] && Array.isArray(processedData[field])) {
        console.log(
          `转换数组字段 ${field}:`,
          processedData[field],
          "-> ",
          processedData[field].join(",")
        );
        // 过滤空值并转换为字符串
        processedData[field] = processedData[field]
          .filter((item) => item && item.trim() !== "")
          .join(",");
      }
    });
  }
  return processedData;
};

// 处理字符串转换为数组（编辑时使用）
const processStringToArray = (data, readonly = false) => {
  if (arrayField.value && !readonly) {
    arrayField.value.forEach((field) => {
      if (data[field] && typeof data[field] === "string") {
        console.log(
          `转换字符串字段 ${field}:`,
          data[field],
          "-> ",
          data[field].split(",")
        );
        // 处理空字符串的情况
        if (data[field].trim() === "") {
          data[field] = [];
        } else {
          data[field] = data[field]
            .split(",")
            .filter((item) => item.trim() !== "");
        }
      }
    });
  }
};

// 获取表单数据
const getFormData = () => {
  // 返回处理后的数据（数组转为字符串）
  console.log("表单数据", form);
  return processArrayToString(form);
};

// 提交表单
async function submitForm() {
  console.log(proxy);
  await proxy.$refs["formRef"].validate();

  console.log("验证通过，提交表单数据:", form.value);
  console.log("提交表单", form.value);

  if (!props.modal.isEdit) {
    await addNormalWordApi(form.value);
  } else {
    await updateRuleNormalBase(form.value);
  }
  proxy.$modal.msgSuccess(!props.modal.isEdit ? "添加成功" : "修改成功");
  props.modal.open = false;
  emit("ok");
}

async function replaceFile() {
  console.log("handleReplaceFile");
  await proxy.$refs["formRef"].validate();
  console.log("验证通过，提交表单数据:", form.value);
  await replaceFileApi(form.value);
  proxy.$modal.msgSuccess("替换成功");
  props.modal.open = false;
  emit("ok");
}
onMounted(() => {
  form.value = {};
});
// 暴露方法给父组件
defineExpose({
  getFormData,
  submitForm,
  replaceFile,
});
</script>

<style lang="scss" scoped>
@import "@/components/Business/style/drawer-form.scss";
:deep(.el-form-item__error) {
  display: none;
}
</style>
