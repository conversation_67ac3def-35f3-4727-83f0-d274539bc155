<template>
  <ViewBody title="规则验证">
    <template #content>
      <!-- 搜索菜单 -->
      <FormSearch
        v-model="queryParams"
        :formItems="formItems"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <template #btn>
          <el-button class="my_primary_btn" type="success" @click="handleAdd"
            >验证规则
          </el-button>
          <el-button type="warning" plain icon="Download" @click="handleExport"
            >导出数据
          </el-button>
        </template>
      </FormSearch>

      <!-- 表格部分 -->
      <TableForm
        ref="tableFormRef"
        :columns="columns"
        :tableData="dataList"
        v-loading="loading"
        :showIndex="true"
        :showOtherColumn="true"
        tableotherColumnLabel="操作"
        :total="total"
        :isShowSearchQuery="false"
        :isShowSelection="true"
        @selection-change="handleSelectionChange"
        @cell-click="handleCellClick"
      >
        <!-- 自定义操作按钮 -->
        <template #otherOperation="{ row }">
          <!-- 使用 flex 布局确保按钮在同一行 -->
          <div class="operation-buttons">
            <span
              v-if="row.status === 'SUCCESS'"
              class="operation-button"
              @click="handleLook(row.ruleId, row.id)"
            >
              查看
            </span>
            <span class="operation-button" @click="handleLog(row)"> 日志 </span>
            <span
              v-if="row.type === 1"
              class="operation-button"
              @click="handleDelete(row)"
            >
              删除
            </span>
            <span
              v-if="row.status === 'FAILURE'"
              class="operation-button"
              @click="handleFailReason(row)"
            >
              失败原因
            </span>
          </div>
        </template>

        <template #pagination>
          <Pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </template>
      </TableForm>
    </template>
  </ViewBody>

  <!-- 验证规则弹窗 -->
  <RuleValidationDialog :modal="vaildationModal" @success="getList" />
</template>

<script setup>
import {
  ref,
  reactive,
  computed,
  nextTick,
  getCurrentInstance,
  toRefs,
} from "vue";
import { useRouter } from "vue-router";
import {
  listRuleNormalValidation,
  deleteRuleNormalValidation,
} from "@/api/processingtool/ruleNormalization.js";
import {
  FormSearch,
  TableForm,
  ViewBody,
  Pagination,
} from "@/components/Business";
import RuleValidationDialog from "./RuleValidationDialog.vue";
import { queryRuleBaseSelect } from "@/api/processingtool/ruleBase.js";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 系统字典
const sysDict = reactive(
  proxy.useDict(
    "storage_use_status",
    "storage_rule_valid_status",
    "storage_tool_status",
    "storage_rule_check_status",
    "storage_rule_normal_phase"
  )
);

// 业务字典
const bizDict = reactive({
  dataOriginOptions: [],
  ruleBaseSelectOptions: [],
});

// 表单引用
const tableFormRef = ref(null);

const dataList = ref([]);
const loading = ref(true);
const total = ref(1);
const multiple = ref(true);
const ids = ref([]);
const selectRows = ref([]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ruleType: "NORMAL",
    searchCreatTime: "",
    name: "",
  },
});

const { queryParams } = toRefs(data);

// 弹窗数据
const vaildationModal = reactive({
  isEdit: false,
  open: false,
  ruleBaseSelectFlag: false,
  form: {
    ruleId: "",
  },
  dict: {
    sysDict: sysDict,
    bizDict: bizDict,
  },
});

// 规则验证搜索表单配置
const formItems = computed(() => [
  {
    label: "规则名称",
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "请输入规则名称",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    label: "应用环节",
    prop: "phase",
    component: "el-select",
    props: {
      placeholder: "应用环节",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_rule_normal_phase,
  },
  {
    label: "处理状态",
    prop: "status",
    component: "el-select",
    props: {
      placeholder: "处理状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_rule_valid_status,
  },
  {
    label: "创建时间",
    prop: "dateRange",
    component: "el-date-picker",
    props: {
      type: "daterange",
      valueFormat: "YYYY-MM-DD",
      rangeSeparator: "-",
      startPlaceholder: "开始日期",
      endPlaceholder: "结束日期",
      style: { width: "308px" },
    },
  },
]);

// 规则验证表格列配置
const columns = ref([
  { prop: "name", label: "规则名称", width: "220", fixed: true },
  { prop: "phase_dict", label: "应用环节", width: "120" },
  { prop: "fileName", label: "文件名称", showOverflowTooltip: true },
  { prop: "fileSize", label: "文件大小", width: "100" },
  { prop: "createBy", label: "操作人", width: "120" },
  { prop: "status_dict", label: "处理状态", width: "120" },
  { prop: "updateTime", label: "更新时间", width: "180" },
]);

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  try {
    const { data } = await listRuleNormalValidation(queryParams.value);
    // 处理数据字典映射
    dataList.value = data.records.map((row) => {
      return {
        ...row,
        ...mapDictLabels(row),
      };
    });
    total.value = data.total;
  } catch (error) {
    console.error("获取规则验证列表失败:", error);
    proxy.$modal.msgError("获取规则验证列表失败");
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  vaildationModal.open = true;
  vaildationModal.ruleBaseSelectFlag = true;
  vaildationModal.form = {
    ruleId: "",
  };
}

/** 导出按钮操作 */
function handleExport() {
  // 这里需要根据实际的导出API来实现
  console.log("导出数据", queryParams.value);
}

/** 查看按钮操作 */
function handleLook(ruleId, ruleValidId) {
  if (ruleId == null || ruleId === "") {
    proxy.$modal.msgError("规则ID为空！");
    return;
  }
  if (ruleValidId == null || ruleValidId === "") {
    proxy.$modal.msgError("规则效验ID为空！");
    return;
  }
  // 跳转规则结果详情页
  console.log("ruleId", ruleId);
  console.log("ruleValidId", ruleValidId);
  router.push({
    name: "ParsingmappingDetail",
    query: {
      ruleId: ruleId,
      ruleValidId: ruleValidId,
    },
  });
}

/** 删除按钮操作 */
async function handleDelete(row) {
  console.log("删除", row);
  if (row.type === 0) {
    proxy.$modal.msgError("默认解析规则文件无法删除！");
    return;
  }
  const confirmRes = await proxy.$modal
    .confirm('是否确认删除规则名称为"' + row.name + '"的数据项？')
    .catch(() => {});
  if (!confirmRes) return;
  await deleteRuleNormalValidation([row.id]);
  proxy.$modal.msgSuccess("删除成功！");
  await getList();
}

/** 日志操作 */
function handleLog(row) {
  console.log("查看日志", row);
}

/** 失败理由操作 */
function handleFailReason(row) {
  console.log("查看失败原因", row);
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  selectRows.value = selection;
  console.log("多选框选中数据", selectRows.value);
  multiple.value = !selection.length;
}

// 处理单元格点击事件（用于开关状态变化）
function handleCellClick(row, column, cellValue, event) {
  // 如果是应用状态列的开关变化
  console.log("单元格点击事件", row, column, cellValue, event);
}

// 映射规则验证字典标签
const mapDictLabels = (row) => {
  return {
    phase_dict: getDictLabel(sysDict.storage_rule_normal_phase, row.phase),
    status_dict: getDictLabel(sysDict.storage_rule_valid_status, row.status),
  };
};

// 获取字典标签（优化版本）
const getDictLabel = (options, value) => {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
};

/**获取解析规则字典信息*/
async function getRuleBaseSelect() {
  // , ruleStatus: 'COMPLETED'
  const { data } = await queryRuleBaseSelect({ ruleType: "NORMAL" });
  console.log("获取解析规则字典信息", data);
  bizDict.ruleBaseSelectOptions = data;
}

// 初始化数据
getRuleBaseSelect();
getList();
</script>

<style lang="scss" scoped>
.operation-buttons {
  display: flex;
  align-items: center;
}

.operation-button {
  margin-left: 10px;
  color: #0076d0;
  font-weight: 500;
  cursor: pointer;
  font-size: 12px;
}
</style>
