<template>
  <div class="form-container">
    <el-form
      ref="fromRef"
      :rules="rules"
      :model="form"
      label-width="120px"
      size="small"
      label-position="left"
      :show-message="false"
    >
      <el-form-item label="属性名称" prop="attributeName">
        {{ form.attributeName }}</el-form-item
      >
      <!-- 属性值数据表格 -->
      <el-form-item label="属性值" prop="attributeValue">
        <div class="filed-add">
          <el-button @click="handleAddFieldValue">新增</el-button>
        </div>
        <el-table
          ref="tableRef"
          :data="ruleNormalizationWordDetailList"
          style="width: 100%"
          empty-message="暂无字段值数据"
          border
          max-height="400"
        >
          <!-- <el-table-column type="selection" width="55" /> -->
          <el-table-column label="属性值名称" min-width="150">
            <template #default="scope">
              <el-input
                style="min-width: 120px"
                v-if="scope.row.isEdit"
                v-model="scope.row.attributeValue"
                placeholder="请输入属性值名称"
                @blur="
                  scope.row.attributeValue =
                    scope.row.attributeValue || scope.row.originalValue || ''
                "
              />
              <span v-else>{{ scope.row.attributeValue }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否忽略">
            <template #default="scope">
              <el-switch
                v-model="scope.row.attributeStatusBoolean"
                inline-prompt
                active-text="忽略"
                inactive-text="忽略"
                @change="handleAttributeStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <!-- 非编辑状态：显示编辑和删除按钮 -->
              <template v-if="!scope.row.isEdit">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="handleEditFieldValue(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="handleDeleteFieldValue(scope.row)"
                  >删除</el-button
                >
              </template>

              <!-- 编辑状态：显示保存和取消按钮 -->
              <template v-else>
                <el-button
                  type="success"
                  link
                  size="small"
                  @click="handleSaveFieldValue(scope.row)"
                  >保存</el-button
                >
                <el-button
                  type="info"
                  link
                  size="small"
                  @click="handleCancelEdit(scope.row)"
                  >取消</el-button
                >
              </template>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="属性值映射" prop="enabledAttributes">
        <div class="filed-add">
          <el-button @click="handleAddMapping">新增</el-button>
        </div>
        <el-table
          ref="mappingTableRef"
          :data="mappingData"
          style="width: 100%"
          empty-message="暂无属性值映射数据"
          max-height="400"
          border
        >
          <el-table-column
            label="序号"
            type="index"
            width="60"
            align="center"
          />
          <el-table-column
            label="原属性"
            prop="sourceAttribute"
            align="center"
            min-width="150"
          >
            <template #default="scope">
              <el-select
                v-model="scope.row.sourceAttribute"
                placeholder="请选择原属性"
                size="small"
                style="width: 100%"
                @change="handleSourceAttributeChange(scope.row, $event)"
                clearable
                filterable
              >
                <el-option
                  v-for="attr in enabledAttributes"
                  :key="attr.value"
                  :label="attr.label"
                  :value="attr.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            label="目标属性"
            prop="targetAttribute"
            align="center"
            min-width="150"
          >
            <template #default="scope">
              <el-select
                v-model="scope.row.targetAttribute"
                placeholder="请选择目标属性"
                size="small"
                style="width: 100%"
                @change="handleTargetAttributeChange(scope.row, $event)"
                clearable
                filterable
              >
                <el-option
                  v-for="attr in enabledAttributes"
                  :key="attr.value"
                  :label="attr.label"
                  :value="attr.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="80" align="center">
            <template #default="scope">
              <el-button
                type="danger"
                link
                size="small"
                @click="handleDeleteMapping(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref, computed, getCurrentInstance } from "vue";
import {
  addRuleNormalizationWordDetailApi,
  listRuleNormalizationWordDetail,
  updateRuleNormalizationWordDetailApi,
  deleteRuleNormalizationWordDetailApi,
} from "@/api/processingtool/ruleNormalization.js";
import { ro } from "element-plus/es/locales.mjs";

const { proxy } = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  },
});
const emit = defineEmits(["ok"]);
/**字典*/
const { sysDict } = props.modal.dict;
const selectedAttributeIndex = ref(0);
// 属性值列表
const ruleNormalizationWordDetailList = ref([]);
// 属性值映射表
const ruleNormalizationWordDetailMappingList = ref([]);

// 计算可用的属性值选项（忽略状态不影响映射选项）
const enabledAttributes = computed(() => {
  return ruleNormalizationWordDetailList.value
    .filter((attr) => attr.attributeValue && attr.attributeValue.trim() !== "")
    .map((attr) => ({
      value: attr.id, // 使用属性值的ID作为value
      label: attr.attributeValue, // 使用属性值作为显示的label
      attributeValue: attr.attributeValue, // 保留原属性值用于显示和保存
    }));
});
// 需要进行数组转换的字段
const arrayField = computed(() => {
  return props.modal.arrayField;
});

const form = computed({
  get() {
    return props.modal.form;
  },
  set() {},
});

// 表格引用
const tableRef = ref(null);
const mappingTableRef = ref(null);
const loading = ref(false);
// 属性值数据
const attributeValueData = ref([]);
// 映射数据
const mappingData = ref([]);
// ------------------------------- 逻辑处理
/** 查询属性值列表 */
async function getRuleNormalizationWordDetailList() {
  try {
    loading.value = true;
    const queryParams = {
      normalizationWordId: props.modal.form.id,
      type: 0,
      orderByCol: "createTime",
      orderAsc: true,
    };
    const { data } = await listRuleNormalizationWordDetail(queryParams);
    ruleNormalizationWordDetailList.value = data || [];

    // 为每个数据项添加计算属性用于Switch组件
    ruleNormalizationWordDetailList.value.forEach((item) => {
      item.attributeStatusBoolean = item.attributeStatus === "Y";
    });

    // 设置选中状态 - 找到checkStatus为1的项
    const selectedIndex = ruleNormalizationWordDetailList.value.findIndex(
      (item) => item.checkStatus === 1
    );
    selectedAttributeIndex.value = selectedIndex !== -1 ? selectedIndex : 0;

    console.log(
      "ruleNormalizationWordDetailList",
      ruleNormalizationWordDetailList.value
    );
  } catch (error) {
    console.error("获取属性值列表失败:", error);
    proxy.$modal.msgError("获取属性值列表失败");
  } finally {
    loading.value = false;
  }
}

/** 查询属性值映射列表 */
async function getRuleNormalizationWordDetailMappingList() {
  try {
    loading.value = true;
    const queryParams = {
      normalizationWordId: props.modal.form.id,
      type: 1,
      orderByCol: "createTime",
      orderAsc: true,
    };
    const { data } = await listRuleNormalizationWordDetail(queryParams);
    ruleNormalizationWordDetailMappingList.value = data || [];
    // 直接使用原始数据映射到组件数据
    mappingData.value = ruleNormalizationWordDetailMappingList.value.map(
      (item) => ({
        id: item.id,
        sourceAttribute: item.originalAttributeId || "", // 原属性ID
        sourceAttributeLabel: item.originalAttribute || "", // 原属性值
        targetAttribute: item.targetAttributeId || "", // 目标属性ID
        targetAttributeLabel: item.targetAttribute || "", // 目标属性值
        originalData: item,
      })
    );
    console.log("mappingData", mappingData.value);
  } catch (error) {
    console.error("获取属性值映射列表失败:", error);
    proxy.$modal.msgError("获取属性值映射列表失败");
  } finally {
    loading.value = false;
  }
}
// 初始化属性值数据
function initAttributeValueData() {
  // 这里应该从表单数据中获取属性值列表
  // 如果表单中没有现成的数据，可以先使用空数组
  attributeValueData.value = tableData.value || [];
}
// 初始化数据
async function initializeData() {
  if (!props.modal.form.id) {
    console.warn("id为空，无法初始化数据");
    return;
  }

  try {
    // 并行调用两个接口
    await Promise.all([
      getRuleNormalizationWordDetailList(),
      getRuleNormalizationWordDetailMappingList(),
    ]);
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
}
watch(
  () => props.modal.form.id,
  async (newData, oldData) => {
    await fetchDetailData();
    console.log("DetailPanel: 数据变化，触发接口调用", newData);
  },
  { immediate: true, deep: true }
);

// 数据获取接口
async function fetchDetailData() {
  try {
    console.log("DetailPanel: id变化，触发接口调用", props.modal.form.id);
    if (props.modal.form.id) {
      // 根据id初始化attributeValues和mappingData
      await initializeData();
    } else {
      // 如果id为空，重置数据
      resetData();
    }
  } catch (error) {
    console.error("获取详细数据失败:", error);
  }
}

// 重置数据
function resetData() {
  mappingData.value = [];
  selectedAttributeIndex.value = 0;
  ruleNormalizationWordDetailList.value = [];
  ruleNormalizationWordDetailMappingList.value = [];
  console.log("数据已重置");
}

// 获取表单数据
const getFormData = () => {
  // 返回处理后的数据（数组转为字符串）
  console.log("表单数据", form);
  return processArrayToString(form);
};

// 处理数组字段转换为字符串（保存时使用）
const processArrayToString = (data) => {
  const processedData = { ...data };
  if (arrayField.value) {
    arrayField.value.forEach((field) => {
      if (processedData[field] && Array.isArray(processedData[field])) {
        console.log(
          `转换数组字段 ${field}:`,
          processedData[field],
          "-> ",
          processedData[field].join(",")
        );
        // 过滤空值并转换为字符串
        processedData[field] = processedData[field]
          .filter((item) => item && item.trim() !== "")
          .join(",");
      }
    });
  }
  return processedData;
};

// -----------------------------    属性值操作
// 新增字段值
async function handleAddFieldValue() {
  try {
    const newAttribute = { normalizationWordId: props.modal.form.id, type: 0 };
    const { data } = await addRuleNormalizationWordDetailApi(newAttribute);
    if (data) {
      await getRuleNormalizationWordDetailList();
    }
    // 滚动到底部
    setTimeout(() => {
      if (tableRef.value) {
        const container = tableRef.value.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      }
    }, 100);
  } catch (error) {
    console.error("添加属性值失败:", error);
    proxy.$modal.msgError("添加属性值失败");
  }
}

// 删除字段值
async function handleDeleteFieldValue(row, index) {
  try {
    console.log(row);
    const confirmRes = await proxy.$modal
      .confirm(
        `确认要删除当前${row.attributeValue ? row.attributeValue : ""}属性值吗?`
      )
      .catch(() => {});
    if (!confirmRes) return;

    await deleteRuleNormalizationWordDetailApi({ id: row.id });

    // 重新加载数据
    await getRuleNormalizationWordDetailList();
    proxy.$modal.msgSuccess("删除成功");
  } catch (error) {
    console.error("删除属性值失败:", error);
    // proxy.$modal.msgError("删除失败");
  }
}

// 编辑字段值
function handleEditFieldValue(row) {
  // 确保其他行都不处于编辑状态
  ruleNormalizationWordDetailList.value.forEach((item) => {
    if (item.id !== row.id) {
      item.isEdit = false;
    }
  });

  // 设置当前行为编辑状态
  row.isEdit = true;
  // 保存原始值，用于取消编辑
  row.originalValue = row.attributeValue;
}

// 保存字段值
async function handleSaveFieldValue(row) {
  try {
    if (!row.attributeValue || row.attributeValue.trim() === "") {
      // proxy.$modal.msgError("字段名称不能为空");
      return;
    }
    const updateData = {
      id: row.id,
      attributeValue: row.attributeValue,
      attributeStatus: row.attributeStatusBoolean ? "Y" : "N",
    };
    await updateRuleNormalizationWordDetailApi(updateData);
    console.log("属性值更新成功:", updateData);
    row.isEdit = false;
  } catch (error) {
    console.error("保存字段值失败:", error);
    row.attributeValue = row.originalValue || "";
  }
}

// 取消编辑字段值
function handleCancelEdit(row) {
  // 如果是编辑现有行，恢复原始值并退出编辑状态
  row.attributeValue = row.originalValue || "";
  row.isEdit = false;
}
// 忽略状态变化处理
async function handleAttributeStatusChange(row) {
  try {
    if (!row.id) {
      console.warn("属性值项缺少ID，无法更新状态");
      return;
    }

    // 同步更新attributeStatus字段
    row.attributeStatus = row.attributeStatusBoolean ? "Y" : "N";

    const updateData = {
      id: row.id,
      attributeValue: row.attributeValue,
      attributeStatus: row.attributeStatus,
    };

    await updateRuleNormalizationWordDetailApi(updateData);
    proxy.$modal.msgSuccess("属性值状态更新成功");
  } catch (error) {
    console.error("更新属性值状态失败:", error);
    proxy.$modal.msgError("更新属性值状态失败");
    // 失败时恢复原始状态
    item.attributeStatusBoolean = item.attributeStatus === "Y";
  }
}

// -------------------------------------- 属性值映射
// 添加映射
async function handleAddMapping() {
  try {
    if (enabledAttributes.value.length === 0) {
      proxy.$modal.msgWarning("请先添加属性值");
      return;
    }

    const newMapping = { normalizationWordId: props.modal.form.id, type: 1 };
    const { data } = await addRuleNormalizationWordDetailApi(newMapping);
    if (data) {
      await getRuleNormalizationWordDetailMappingList();
      proxy.$modal.msgSuccess("添加映射成功");
    }
    // 滚动到底部
    setTimeout(() => {
      if (mappingTableRef.value) {
        const container = mappingTableRef.value.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      }
    }, 100);
  } catch (error) {
    console.error("添加映射失败:", error);
    proxy.$modal.msgError("添加映射失败");
  }
}

// 删除映射
async function handleDeleteMapping(index) {
  try {
    const mappingItem = mappingData.value[index];
    if (!mappingItem || !mappingItem.id) {
      console.warn("映射项缺少ID，无法删除");
      return;
    }

    const confirmRes = await proxy.$modal
      .confirm(`确认要删除当前映射关系吗?`)
      .catch(() => {});
    if (!confirmRes) return;

    await deleteRuleNormalizationWordDetailApi({ id: mappingItem.id });

    // 重新加载数据
    await getRuleNormalizationWordDetailMappingList();
    proxy.$modal.msgSuccess("删除成功");

    console.log(`删除索引 ${index} 的映射关系`);
  } catch (error) {
    console.error("删除映射失败:", error);
  }
}

// 原属性选择变化处理
async function handleSourceAttributeChange(mappingItem, selectedValue) {
  try {
    if (!mappingItem || !mappingItem.id) {
      console.warn("映射项缺少ID，无法更新");
      return;
    }

    // 查找选中的属性信息
    const selectedAttr = enabledAttributes.value.find(
      (attr) => attr.value === selectedValue
    );
    if (selectedAttr) {
      mappingItem.sourceAttributeLabel = selectedAttr.label;
    }

    const updateData = {
      id: mappingItem.id,
      originalAttribute: mappingItem.sourceAttributeLabel,
      originalAttributeId: mappingItem.sourceAttribute,
      targetAttribute: mappingItem.targetAttributeLabel,
      targetAttributeId: mappingItem.targetAttribute,
    };

    await updateRuleNormalizationWordDetailApi(updateData);
    console.log("原属性映射更新成功:", updateData);
  } catch (error) {
    console.error("更新原属性映射失败:", error);
    proxy.$modal.msgError("更新原属性映射失败");
    // 失败时重新加载数据以恢复原始状态
    await getRuleNormalizationWordDetailMappingList();
  }
}

// 目标属性选择变化处理
async function handleTargetAttributeChange(mappingItem, selectedValue) {
  try {
    if (!mappingItem || !mappingItem.id) {
      console.warn("映射项缺少ID，无法更新");
      return;
    }

    // 查找选中的属性信息
    const selectedAttr = enabledAttributes.value.find(
      (attr) => attr.value === selectedValue
    );
    if (selectedAttr) {
      mappingItem.targetAttributeLabel = selectedAttr.label;
    }

    const updateData = {
      id: mappingItem.id,
      originalAttribute: mappingItem.sourceAttributeLabel,
      originalAttributeId: mappingItem.sourceAttribute,
      targetAttribute: mappingItem.targetAttributeLabel,
      targetAttributeId: mappingItem.targetAttribute,
    };

    await updateRuleNormalizationWordDetailApi(updateData);
    console.log("目标属性映射更新成功:", updateData);
  } catch (error) {
    console.error("更新目标属性映射失败:", error);
    proxy.$modal.msgError("更新目标属性映射失败");
    // 失败时重新加载数据以恢复原始状态
    await getRuleNormalizationWordDetailMappingList();
  }
}

// 选中项变化处理
function handleSelectedChange() {
  console.log("选中项变化:", selectedAttributeIndex.value);
}

// 提交表单
async function submitForm() {
  await proxy.$refs["formRef"].validate();
  console.log("验证通过，提交表单数据:", form.value);
  console.log("提交表单", form.value);

  if (!props.modal.isEdit) {
    await addNormalWordApi(form.value);
  } else {
    await updateRuleNormalBase(form.value);
  }
  proxy.$modal.msgSuccess(!props.modal.isEdit ? "添加成功" : "修改成功");
  props.modal.open = false;
  emit("ok");
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  submitForm,
});
</script>

<style lang="scss" scoped>
@import "@/components/Business/style/drawer-form.scss";
@import "@/components/Business/base.scss";
.filed-add {
  height: 32px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
  .el-button {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
    background-color: rgb(0, 120, 212);
  }
}

.el-table {
  :deep(.el-input) {
    width: 100%;
  }
}
:deep(.el-form-item__error) {
  display: none;
}
</style>
