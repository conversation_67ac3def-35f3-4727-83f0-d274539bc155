<template>
  <el-dialog
    v-model="modal.open"
    title="验证规则"
    width="600px"
    destroy-on-close
  >
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item label="规则名称" prop="ruleId" label-width="100px">
        <el-select
          v-model="form.ruleId"
          placeholder="请选择规则名称"
          :disabled="!modal.ruleBaseSelectFlag"
        >
          <el-option
            v-for="item in bizDict.ruleBaseSelectOptions"
            :key="item.value"
            :label="item.text"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="样例文件" prop="sampleFile" label-width="100px">
        <FileUpload
          v-model="form.sampleFile"
          :action="'/file/common/upload'"
          :limit="1"
          :fileSize="20"
          :fileType="['zip', 'xml']"
          :fileList="[]"
          :isShowTip="true"
          :data="uploadParams"
          @upload-change="handleUploadChange"
          :uploadTip="customUploadTip"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div>
        <el-divider />
        <el-button @click="handleCancel">取 消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitLoading"
        >
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance } from "vue";
import FileUpload from "@/components/FileUpload/index.vue";
import { validateRuleApi } from "@/api/processingtool/ruleAnalysisValidation.js";

const { proxy } = getCurrentInstance();

// Props
const props = defineProps({
  modal: {
    type: Object,
    required: true,
  },
});
const emit = defineEmits(["success"]);
/**字典*/
const { bizDict } = props.modal.dict;

const customUploadTip = ref("样例文件仅用于规则生成，文件及处理结果均不入库");

// 响应式数据
const form = computed({
  get() {
    return props.modal.form;
  },
  set() {},
});
const formRef = ref(null);
const submitLoading = ref(false);

const rules = reactive({
  ruleId: [{ required: true, message: "请选择规则名称", trigger: "change" }],
  sampleFile: [
    { required: true, message: "请上传样例文件", trigger: "change" },
  ],
});

const uploadParams = ref({
  projectPath: "storage/rule/validation",
  isSpecifiedFile: true,
});

// 处理文件上传变化
const handleUploadChange = (fileList) => {
  console.log("handleUploadChange", form.value);
  console.log("handleUploadChange", fileList);
  if (!fileList || fileList.length === 0) {
    return;
  }
  form.value.fileList = fileList;
};

// 处理取消
const handleCancel = () => {
  props.modal.open = false;
};

// 处理确认 - 在子组件中完成所有验证逻辑
const handleConfirm = async () => {
  const name = form.value.name;
  try {
    // 表单验证
    await proxy.$refs["formRef"].validate();
    console.log("提交表单", form.value);
    await validateRuleApi(form.value);
    // 成功提示
    proxy.$modal.msgSuccess("验证规则提交成功！");
    // 关闭弹窗
    props.modal.open = false;
    // 通知父组件刷新数据
    emit("success");
  } catch (error) {
    form.value.name = name;
    console.log("验证规则提交失败", error);
    proxy.$modal.msgError("验证规则提交失败");
  } finally {
    submitLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
@import "@/components/Business/style/drawer-form.scss";
:deep(.el-form-item__label) {
  margin-right: 10px;
}
</style>
