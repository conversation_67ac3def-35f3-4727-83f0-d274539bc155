<template>
  <div class="form-container">
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120px"
      size="small"
      label-position="left"
    >
      <el-form-item label="规则名称" prop="name">
        <template #label> 规则名称<em>*</em>： </template>
        <el-input v-model="form.name" placeholder="规则名称" />
        <template #error><em /></template>
      </el-form-item>

      <el-form-item label="应用环节" prop="phase">
        <template #label> 应用环节： </template>
        <el-select filterable v-model="form.phase" placeholder="">
          <el-option
            v-for="dict in sysDict.storage_rule_phase"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
        <template #error><em /></template>
      </el-form-item>

      <el-form-item label="工具交互状态" prop="toolStatus">
        <template #label> 工具交互状态： </template>
        <el-input v-model="form.toolStatusLable" placeholder="" disabled />
        <template #error><em /></template>
      </el-form-item>

      <el-form-item label="应用状态" prop="useStatus">
        <template #label> 应用状态<em>*</em>： </template>
        <el-select filterable v-model="form.useStatus" placeholder="">
          <el-option
            v-for="dict in sysDict.storage_use_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
        <template #error><em /></template>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref, computed, getCurrentInstance } from "vue";
import { updateRuleNormalBase } from "@/api/processingtool/ruleNormalization.js";

const { proxy } = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  },
});
const emit = defineEmits(["ok"]);

/**字典*/
const { sysDict } = props.modal.dict;

// 需要进行数组转换的字段
const arrayField = computed(() => {
  return props.modal.arrayField;
});

const form = computed({
  get() {
    console.log(props.modal.form);
    return props.modal.form;
  },
  set() {},
});

const rules = reactive({
  name: [{ required: true, message: "规则名称不能为空", trigger: "change" }],
  phase: [{ required: true, message: "应用环节不能为空", trigger: "change" }],
  useStatus: [
    { required: true, message: "应用状态不能为空", trigger: "change" },
  ],
});

// 处理数组字段转换为字符串（保存时使用）
const processArrayToString = (data) => {
  const processedData = { ...data };
  if (arrayField.value) {
    arrayField.value.forEach((field) => {
      if (processedData[field] && Array.isArray(processedData[field])) {
        console.log(
          `转换数组字段 ${field}:`,
          processedData[field],
          "-> ",
          processedData[field].join(",")
        );
        // 过滤空值并转换为字符串
        processedData[field] = processedData[field]
          .filter((item) => item && item.trim() !== "")
          .join(",");
      }
    });
  }
  return processedData;
};

// 处理字符串转换为数组（编辑时使用）
const processStringToArray = (data, readonly = false) => {
  if (arrayField.value && !readonly) {
    arrayField.value.forEach((field) => {
      if (data[field] && typeof data[field] === "string") {
        console.log(
          `转换字符串字段 ${field}:`,
          data[field],
          "-> ",
          data[field].split(",")
        );
        // 处理空字符串的情况
        if (data[field].trim() === "") {
          data[field] = [];
        } else {
          data[field] = data[field]
            .split(",")
            .filter((item) => item.trim() !== "");
        }
      }
    });
  }
};

// 获取表单数据
const getFormData = () => {
  // 返回处理后的数据（数组转为字符串）
  console.log("表单数据", form);
  return processArrayToString(form);
};

// 提交表单
async function submitForm() {
  await proxy.$refs["formRef"].validate();
  console.log("验证通过，提交表单数据:", form.value);
  await updateRuleNormalBase(form.value);
  proxy.$modal.msgSuccess("修改成功");
  props.modal.open = false;
  emit("ok");
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  submitForm,
});
</script>

<style lang="scss" scoped>
@import "@/components/Business/style/drawer-form.scss";
:deep(.el-form-item__error) {
  display: none;
}
</style>
