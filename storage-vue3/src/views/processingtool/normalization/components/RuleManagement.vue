<template>
  <div class="app-container">
    <viewBody title="规则管理">
      <template #content>
        <!-- 查询表单 -->
        <FormSearch
          v-model="queryParams"
          :formItems="formItems"
          searchBtnName="查询"
          @search="handleQuery"
          @reset="resetQuery"
        >
        </FormSearch>
        <!--  表格顶部按钮区域-->
        <div class="tableForm-btns-top">
          <el-button plain icon="Download" @click="handleExport" class="btn"
            >导出规则
          </el-button>
          <el-button
            plain
            icon="Delete"
            @click="handleDelete()"
            :disabled="multiple"
            class="btn btn--danger"
            >删除规则
          </el-button>
          <el-button
            plain
            icon="Document"
            @click="handleWordList()"
            class="btn btn--primary"
            >词表管理
          </el-button>
        </div>
        <!-- 表格部分 -->
        <TableForm
          ref="tableFormRef"
          class="tableFormContent"
          v-loading="loading"
          :total="total"
          :columns="columns"
          :sortableItems="sortableItems"
          :tableData="dataSourceList"
          :isShowSearchQuery="false"
          :show-index="true"
          :isShowCount="false"
          :show-note-column="false"
          :isShowSelection="true"
          @selection-change="handleSelectionChange"
          :showEditBtn="false"
          :showOtherColumn="true"
          tableotherColumnLabel="操作"
          :columnsRightWidth="columnsRightWidth"
          @cellClick="handleCellClick"
        >
          <!-- 自定义操作列 -->
          <template #otherOperation="{ row }">
            <span
              v-if="row.id"
              class="operation-btn"
              @click.stop="handleLook(row.id, row.ruleValidId)"
            >
              查看
            </span>
            <span
              style="color: #03b915"
              class="operation-btn"
              @click.stop="handleUpdate(row)"
            >
              编辑
            </span>
            <!-- 更多操作下拉菜单 -->
            <el-dropdown
              @command="(command) => handleMoreAction(command, row)"
              trigger="click"
            >
              <span style="color: #008080" class="operation-btn"> 更多 </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="row.useStatus === 'N'"
                    command="delete"
                    icon="Delete"
                    >删除
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="row.useStatus === 'Y'"
                    command="send"
                    icon="Promotion"
                    >发送
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="
                      row.toolStatus !== 'DATA_RECEIVED_FAIL' &&
                      row.toolStatus !== 'INIT_FAIL'
                    "
                    command="verify"
                    icon="CircleCheck"
                    >验证
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="
                      row.toolStatus === 'DATA_RECEIVED_FAIL' ||
                      row.toolStatus === 'INIT_FAIL'
                    "
                    command="resend"
                    icon="Refresh"
                    >重新发送
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="
                      row.toolStatus === 'DATA_RECEIVED_FAIL' ||
                      row.toolStatus === 'INIT_FAIL'
                    "
                    command="failReason"
                    icon="Warning"
                    >失败理由
                  </el-dropdown-item>
                  <el-dropdown-item command="log" icon="Document"
                    >日志
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <!-- 分页 -->
          <template #pagination>
            <Pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </template>
          <template #drawer>
            <!-- 右侧的抽屉内容 -->
            <DrawerForm ref="drawerRef" :is-show-right-form="false">
              <template #header="{ titleId, titleClass }">
                <div :id="titleId" :class="titleClass" class="my_drawer_title">
                  <span class="active">{{ modal.title }}</span>
                  <div class="my_drawer_title_right">
                    <span class="btn_add" @click="handleSave">保存</span>
                    <span class="btn_cancel" @click="drawerRef?.closeDrawer()"
                      >取消</span
                    >
                  </div>
                </div>
              </template>
              <template #form>
                <RuleForm
                  ref="rightFormRef"
                  :modal="modal"
                  @ok="getList"
                ></RuleForm>
              </template>
            </DrawerForm>
          </template>
        </TableForm>
      </template>
    </viewBody>

    <!-- 验证规则弹窗 -->
    <RuleValidationDialog :modal="vaildationModal" @success="getList" />
  </div>
</template>
<script setup>
import {
  FormSearch,
  TableForm,
  DrawerForm,
  ViewBody,
  Pagination,
} from "@/components/Business";
import RuleForm from "./RuleForm.vue";
import RuleValidationDialog from "./RuleValidationDialog.vue";
import { ref, reactive, computed, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  STORAGE_RULE_NORMAL_BASE_URL,
  deleteRuleNormalBase,
  listRulNormalBase,
  updateRuleNormalBase,
} from "@/api/processingtool/ruleNormalization.js";
import { queryRuleBaseSelect } from "@/api/processingtool/ruleBase.js";
// 获取当前实例
const { proxy } = getCurrentInstance();
const router = useRouter();

/**数据字典*/
const sysDict = reactive({
  ...proxy.useDict("storage_use_status"), // 应用状态
  ...proxy.useDict("storage_rule_phase"), // // 应用环节
  ...proxy.useDict("storage_tool_status"), // 工具交互环节
});
const bizDict = reactive({
  dataOriginOptions: [],
  ruleBaseSelectOptions: [],
});
// 弹窗数据
const modal = reactive({
  isEdit: false,
  title: "新增规则",
  open: false,
  form: {},
  dict: {
    sysDict: sysDict,
    bizDict: bizDict,
  },
  arrayField: [],
});

// 弹窗数据
const vaildationModal = reactive({
  isEdit: false,
  open: false,
  ruleBaseSelectFlag: false,
  form: {
    ruleId: "",
  },
  dict: {
    sysDict: sysDict,
    bizDict: bizDict,
  },
});

// -------------- 查询表单-FormSearch
// 查询表单-查询参数
const queryParams = reactive({
  // 分页参数
  pageNum: 1,
  pageSize: 20,
  // 查询表单绑定的值
  name: "",
  useStatus: "",
  searchCreatTime: "",
  dateRange: [],
});

// 查询表单-配置项
const formItems = computed(() => [
  {
    label: "规则名称",
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "规则名称",
      clearable: true,
      style: { width: "206px" },
    },
  },
  {
    label: "应用状态",
    prop: "useStatus",
    component: "el-select",
    props: {
      placeholder: "应用状态",
      clearable: true,
      style: { width: "206px" },
    },
    children: () => sysDict.storage_use_status,
  },
  {
    label: "创建时间",
    prop: "dateRange",
    component: "el-date-picker",
    props: {
      type: "daterange",
      valueFormat: "YYYY-MM-DD",
      rangeSeparator: "-",
      startPlaceholder: "开始日期",
      endPlaceholder: "结束日期",
      style: { width: "308px" },
    },
  },
]);

// 查询表单-查询方法
const handleQuery = (params) => {
  console.log("查询参数", params);
  queryParams.pageNum = 1;
  // 如果传入了参数，则合并到查询参数中
  if (params) {
    Object.assign(queryParams, params);
  }
  if (queryParams.dateRange && queryParams.dateRange.length == 2) {
    queryParams.searchCreatTime = queryParams.dateRange.join(",");
  }
  getList();
};

// 查询表单-重置方法
const resetQuery = () => {
  queryParams.dateRange = [];
  queryParams.name = "";
  queryParams.useStatus = "";
  queryParams.searchCreatTime = "";

  // 重置后查询
  handleQuery();
};
// -------------- TableForm-Top-Btns
// 导出按钮操作
const handleExport = () => {
  proxy.download(
    STORAGE_RULE_NORMAL_BASE_URL + "/export",
    {
      ...queryParams,
    },
    `规则数据_${new Date().getTime()}.xlsx`
  );
};

// 删除按钮操作
async function handleDelete(row) {
  if (!row) {
    const hasActiveStatusRows = selectRows.value.filter(
      (item) => item.useStatus === "Y"
    );
    if (hasActiveStatusRows.length > 0) {
      proxy.$modal.msgError("选择数据中包含已启用状态数据，无法进行删除！");
      return;
    }
  }
  const localIds = row ? [row.id] : ids.value;
  const selectNameArr = row
    ? row.name
    : selectRows.value.map((item) => item.name);
  const confirmRes = await proxy.$modal
    .confirm('是否确认删除接口名称为"' + selectNameArr + '"的数据项？')
    .catch(() => {});
  if (!confirmRes) return;
  await deleteRuleNormalBase(localIds);
  proxy.$modal.msgSuccess("删除成功！");
  await getList();
}
// 词表管理
const handleWordList = () => {
  router.push({
    name: "NormalizationWord",
  });
};

// -------------- TableForm
// 数据表格-总条数
const total = ref(400);
// 数据表格-加载状态
const loading = ref(false);
// 数据表格-dom实例
const tableFormRef = ref(null);
// 数据表格-自定义列-列宽
const columnsRightWidth = ref("180");
// 是否禁用删除按钮
const multiple = ref(true);
// 选中的行数据ID集合
const ids = ref([]);
// 选中的行数据
const selectRows = ref([]);
// 数据表格-表格列配置
const columns = ref([
  { prop: "name", label: "规则名称" },
  {
    prop: "phaseLable",
    label: "应用环节",
  },
  {
    prop: "toolStatusLable",
    label: "工具交互状态",
    type: "textCircle",
    minWidth: "150",
  },
  {
    prop: "useStatusLable",
    label: "应用状态",
    type: "switch",

    activeText: "启用",
    inactiveText: "停用",
  },
  {
    prop: "createTime",
    label: "规则创建时间",
    minWidth: "120",
  },
  {
    prop: "updateTime",
    label: "规则更新时间",
    minWidth: "120",
  },
]);
// 数据表格-可排序字段(字段值来自表格列配置)
const sortableItems = ["index", "createTime", "updateTime"];

// 数据表格-表格数据
const dataSourceList = ref([]);
// 数据表格-查询方法
async function getList() {
  loading.value = true;
  const { data } = await listRulNormalBase(queryParams);
  dataSourceList.value = data.records;
  // 处理数据,显示字典值
  dataSourceList.value.forEach((item) => {
    item.toolStatusLable;
    const toolItem = sysDict.storage_tool_status.find(
      (dict) => dict.value === item.toolStatus
    );
    if (toolItem) {
      item.toolStatusLable = toolItem.label;
    } else {
      console.warn(`未找到对应字典值：${item.toolStatus}`);
      item.toolStatusLable = "";
    }

    const phaseItem = sysDict.storage_rule_phase.find(
      (dict) => dict.value === item.phase
    );
    if (phaseItem) {
      item.phaseLable = phaseItem.label;
    } else {
      console.warn(`未找到对应字典值：${item.phase}`);
      item.phaseLable = "";
    }

    item.useStatusLable = item.useStatus === "Y" ? true : false;
  });
  total.value = data.total;
  loading.value = false;
}
async function getRuleBaseSelect() {
  const { data } = await queryRuleBaseSelect({ ruleType: "Normal" });
  bizDict.ruleBaseSelectOptions = data;
}

// 数据表格-单元格点击事件
async function handleUseStatusChange(row) {
  console.log("接口应用状态改变", row);
  // 转换开关值为 Y/N 格式
  // const newStatus = row.useStatus ? "Y" : "N";
  const newStatus = row.useStatus === "N" ? "Y" : "N";

  let text = newStatus === "Y" ? "启用" : "停用";
  const confirmRes = await proxy.$modal
    .confirm("确认要" + text + "吗?")
    .catch(() => {});
  if (!confirmRes) {
    row.useStatusLable = !row.useStatusLable;
    return;
  }
  try {
    // 准备更新数据，确保使用 Y/N 格式
    const updateData = { ...row, useStatus: newStatus };
    await updateRuleNormalBase(updateData);
    proxy.$modal.msgSuccess(text + "成功");

    // 更新本地数据
    row.useStatus = newStatus;
  } catch (error) {
    // 更新失败，恢复原状态
    row.useStatus_dict = newStatus === "N";
    console.log(text + "失败", error);
  }
}
// 处理单元格点击事件（用于开关状态变化）
function handleCellClick(row, column, cellValue, event) {
  // 如果是应用状态列的开关变化
  console.log("单元格点击事件", row, column, cellValue, event);
  if (column.property === "useStatusLable") {
    handleUseStatusChange(row);
  }
}
// 数据表格-操作列-编辑点击事件(默认的编辑)
//查看操作
function handleLook(ruleId, ruleValidId) {
  if (ruleId == null || ruleId === "") {
    proxy.$modal.msgError("规则ID为空！");
    return;
  }

  // 跳转规则结果详情页
  console.log("ruleId", ruleId);
  console.log("ruleValidId", ruleValidId);
  router.push({
    name: "NormalizationDetail",
    query: {
      ruleId: ruleId,
      ruleValidId: ruleValidId,
    },
  });
}

// 编辑操作
const handleUpdate = (row) => {
  modal.title = "编辑规则";
  modal.isEdit = true;
  console.log(row);
  modal.form = { ...row };
  // 打开抽屉
  drawerRef?.value.openDrawer();
};

/** 查看按钮操作 */
function handleSend(row) {
  console.log("查看", row);
}
// 更多操作
function handleMoreAction(command, row) {
  console.log("更多操作下拉菜单命令", command);
  console.log("当前行数据", row);
  switch (command) {
    case "delete":
      handleDelete(row);
      break;
    case "send":
      handleSend(row);
      break;
    case "verify":
      handleVerify(row);
      break;
    case "resend":
      handleResend(row);
      break;
    case "failReason":
      handleFailReason(row);
      break;
    case "log":
      handleLog(row);
      break;
    default:
      console.warn("未知的操作命令:", command);
  }
}
/** 规则验证操作 */
function handleVerify(row) {
  console.log("验证", row);
  vaildationModal.open = true;
  vaildationModal.ruleBaseSelectFlag = false;
  vaildationModal.form = {
    ruleId: row.id,
    name: row.name,
  };
}

/** 重新发送操作 */
async function handleResend(row) {
  console.log("查看", row);
  const confirmRes = await proxy.$modal
    .confirm('是否确认重新发送规则名称为"' + row.name + '"的数据项？')
    .catch(() => {});
  if (!confirmRes) return;
  await resendRuleNormalBaseApi(row.id);
  proxy.$modal.msgSuccess("重新发送成功！");
  await getList();
}

/** 查看按钮操作 */
function handleFailReason(row) {
  console.log("查看", row);
}

/** 查看按钮操作 */
function handleLog(row) {
  console.log("查看", row);
}
// -------------- TableForm slot
// -------------- MyDrawer
// 抽屉-dom实例
const drawerRef = ref(null);
const rightFormRef = ref(null);
// 抽屉-自定义表单确定
const handleSave = async () => {
  if (!drawerRef.value) {
    console.warn("抽屉实例不存在");
    return;
  }
  console.log("抽屉-自定义表单确定", rightFormRef.value);
  if (rightFormRef.value && rightFormRef.value.submitForm) {
    // 调用表单提交方法
    await rightFormRef.value.submitForm();
    // 刷新列表
    getList();
  } else {
    console.warn("表单实例不存在或没有submitForm方法");
  }
};
// 抽屉-自定义表单取消
const handleCancel = () => {
  drawerRef.value.closeDrawer();
};

// 表格选择行变化
const handleSelectionChange = (selection) => {
  selectRows.value = selection;
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
};

// 页面加载时获取列表数据
onMounted(() => {
  getList();
  getRuleBaseSelect();
});
</script>
<style scoped lang="scss">
@import "@/components/Business/base.scss";
.tableForm-btns-top {
  height: 44px;
  display: flex;
  align-items: center;
  padding-left: 11px;
}
// 基础按钮样式

.btn {
  height: 32px;
  font-size: 14px;
  font-family: "Microsoft YaHei";
  color: #ffffff;
  border: 1px solid transparent;
  background-color: rgb(189, 189, 189);
  cursor: pointer;
  &--primary {
    background-color: rgb(0, 120, 212);
    &:hover {
      background-color: #2192e9ff;
    }
    &:active {
      background-color: #0069d4ff;
    }
  }

  &--danger {
    background-color: #f56c6c;
    &:hover {
      background-color: #f78989ff;
    }
    &:active {
      background-color: #ef6761ff;
    }

    &.is-disabled,
    &[disabled] {
      background-color: #f78989ff;
      color: #ffffff;
      border-color: transparent;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
}

.operation-btn {
  vertical-align: middle;
  color: #0076d0;
  font-weight: 500;
  cursor: pointer;
}
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}
.el-dropdown {
  font-size: 14px;
  line-height: inherit;
}
</style>
