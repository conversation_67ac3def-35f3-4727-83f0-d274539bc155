<template>
  <div class="app-container">
    <viewBody title="词表管理">
      <template #content>
        <!-- 查询表单 -->
        <FormSearch
          v-model="queryParams"
          :formItems="formItems"
          searchBtnName="查询"
          @search="handleQuery"
          @reset="resetQuery"
        >
        </FormSearch>
        <!--  表格顶部按钮区域-->
        <div class="tableForm-btns-top">
          <el-button
            plain
            icon="Plus"
            class="btn btn--primary"
            @click="handleAddWord"
            >新建词表
          </el-button>
          <el-button plain icon="Download" @click="handleExport" class="btn"
            >导出数据
          </el-button>
        </div>
        <!-- 表格部分 -->
        <TableForm
          ref="tableFormRef"
          v-loading="loading"
          :total="total"
          :columns="columns"
          :sortableItems="sortableItems"
          :tableData="dataSourceList"
          :isShowSearchQuery="false"
          :show-index="true"
          :isShowCount="false"
          :show-note-column="false"
          :isShowSelection="true"
          @selection-change="handleSelectionChange"
          :showEditBtn="false"
          :showOtherColumn="true"
          tableotherColumnLabel="操作"
          :columnsRightWidth="columnsRightWidth"
          @cellClick="cellClick"
        >
          <!-- 自定义操作列 -->
          <template #otherOperation="{ row }">
            <span
              v-if="row.id"
              class="operation-btn"
              @click.stop="handleLook(row.id, row.ruleValidId)"
            >
              查看
            </span>
            <span
              v-if="row.interactiveType === '平台发送'"
              style="color: #f59a23"
              class="operation-btn"
              @click.stop="handleReplace(row)"
            >
              替换
            </span>
            <span
              v-if="row.interactiveType === '平台接收'"
              style="color: #f59a23"
              class="operation-btn"
              @click.stop="handleEdit(row)"
            >
              编辑
            </span>
            <span
              style="color: #8400ff"
              class="operation-btn"
              @click.stop="handleLog(row)"
            >
              日志
            </span>
            <span
              style="color: #d9001b"
              class="operation-btn"
              @click.stop="handleDelete(row)"
            >
              删除
            </span>
          </template>
          <!-- 分页 -->
          <template #pagination>
            <Pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </template>
          <template #drawer>
            <!-- 右侧的抽屉内容 -->
            <DrawerForm ref="drawerRef" :is-show-right-form="false">
              <template #header="{ titleId, titleClass }">
                <div :id="titleId" :class="titleClass" class="my_drawer_title">
                  <span class="active">{{ modal.title }}</span>
                  <div class="my_drawer_title_right">
                    <span
                      class="btn_add"
                      @click="handleSave"
                      v-if="!modal.isEdit && !modal.isLook && !modal.isEditForm"
                      >保存</span
                    >
                    <span
                      class="btn_add"
                      @click="handleReplaceFile"
                      v-if="modal.isEdit && !modal.isLook && !modal.isEditForm"
                      >替换</span
                    >
                    <span class="btn_cancel" @click="handleCancel">取消</span>
                  </div>
                </div>
              </template>
              <template #form>
                <WordForm
                  v-if="!modal.isEditForm"
                  ref="rightFormRef"
                  :modal="modal"
                  @ok="getList"
                ></WordForm>
                <WordEditForm
                  v-if="modal.isEditForm"
                  :form="modal.form"
                  ref="rightEditFormRef"
                  :modal="modal"
                  @ok="getList"
                ></WordEditForm>
              </template>
            </DrawerForm>
          </template>
        </TableForm>
      </template>
    </viewBody>
  </div>
</template>
<script setup>
import {
  FormSearch,
  TableForm,
  DrawerForm,
  ViewBody,
  Pagination,
} from "@/components/Business";
import { ref, reactive, computed, getCurrentInstance, onMounted } from "vue";
import WordForm from "./components/WordForm.vue";
import WordEditForm from "./components/WordEditForm.vue";
import {
  listRuleNormalWord,
  deleteRuleNormalWord,
  STORAGE_RULE_NORMAL_WORD_URL,
} from "@/api/processingtool/ruleNormalization.js";
// 获取当前实例
const { proxy } = getCurrentInstance();

/**数据字典*/
const sysDict = reactive({
  ...proxy.useDict("storage_use_status"), // 应用状态
  ...proxy.useDict("storage_rule_phase"), // // 应用环节
  ...proxy.useDict("storage_tool_status"), // 工具交互环节
  ...proxy.useDict("storage_interactive_type"),
});
const bizDict = reactive({});
// 弹窗数据
const modal = reactive({
  isEdit: false,
  isEditForm: false, // 是否编辑表单
  title: "",
  open: false,
  form: {},
  dict: {
    sysDict: sysDict,
    bizDict: bizDict,
  },
  arrayField: ["processFlow", "analysisRuleId"],
});

// -------------- 查询表单-FormSearch
// 查询表单-查询参数
const queryParams = reactive({
  // 分页参数
  pageNum: 1,
  pageSize: 20,
  // 查询表单绑定的值
  attributeName: "",
  wordName: "",
  searchCreatTime: "",
  dateRange: [],
});

// 查询表单-配置项
const formItems = computed(() => [
  {
    label: "属性名称",
    prop: "attributeName",
    component: "el-input",
    props: {
      placeholder: "属性名称",
      clearable: true,
      style: { width: "206px" },
    },
  },
  {
    label: "词表名称",
    prop: "wordName",
    component: "el-input",
    props: {
      placeholder: "应用状态",
      clearable: true,
      style: { width: "120" },
    },
  },
  {
    label: "创建时间",
    prop: "dateRange",
    component: "el-date-picker",
    props: {
      type: "daterange",
      valueFormat: "YYYY-MM-DD",
      rangeSeparator: "-",
      startPlaceholder: "开始日期",
      endPlaceholder: "结束日期",
      style: { width: "308px" },
    },
  },
]);

// 查询表单-查询方法
const handleQuery = (params) => {
  console.log("查询参数", params);
  queryParams.pageNum = 1;
  // 如果传入了参数，则合并到查询参数中
  if (params) {
    Object.assign(queryParams, params);
  }
  if (queryParams.dateRange && queryParams.dateRange.length == 2) {
    queryParams.searchCreatTime = queryParams.dateRange.join(",");
  }

  // 这里应该调用获取列表数据的方法;
  getList();
};

// 查询表单-重置方法
const resetQuery = () => {
  queryParams.dateRange = [];
  queryParams.attributeName = "";
  queryParams.wordName = "";
  queryParams.searchCreatTime = "";
  // 重置后查询
  handleQuery();
};
// 新增词表
function handleAddWord() {
  if (!drawerRef.value) {
    console.warn("抽屉实例不存在");
    return;
  }

  console.log("新建词表-------");
  modal.isEditForm = false;
  modal.isEdit = false;
  modal.isLook = false;
  modal.open = true;
  modal.title = "新增词表";
  modal.form = {
    interactiveType: "平台发送",
  };
  drawerRef.value.openDrawer();
}
// 导出方法
function handleExport() {
  proxy.download(
    STORAGE_RULE_NORMAL_WORD_URL + "/export",
    {
      ...queryParams.value,
    },
    `规则路径数据_${new Date().getTime()}.xlsx`
  );
}

// -------------- TableForm
// 数据表格-总条数
const total = ref(400);
// 数据表格-加载状态
const loading = ref(false);
// 数据表格-dom实例
const tableFormRef = ref(null);
// 数据表格-自定义列-列宽
const columnsRightWidth = ref("200");
// 是否禁用删除按钮
const multiple = ref(true);
// 选中的行数据ID集合
const ids = ref([]);
// 选中的行数据
const selectRows = ref([]);
// 数据表格-表格列配置
const columns = ref([
  { prop: "attributeName", label: "属性名称", minWidth: "200" },
  { prop: "wordName", label: "词表名称", minWidth: "180" },
  { prop: "attributeCount", label: "属性值数量", width: "100" },
  { prop: "interactiveType", label: "交互类型", width: "100" },
  {
    prop: "toolStatusLable",
    label: "工具交互状态",
    minWidth: "180",
    type: "textCircle",
  },
  { prop: "fileSize", label: "文件大小", width: "100" },
  { prop: "createBy", label: "操作人", width: "80" },
  { prop: "updateTime", label: "更新时间", width: "200" },
]);
// 数据表格-可排序字段(字段值来自表格列配置)
const sortableItems = ["index", "updateTime"];
// 数据表格-表格数据
const dataSourceList = ref([]);
// 数据表格-查询方法
async function getList() {
  loading.value = true;
  const { data } = await listRuleNormalWord(queryParams);
  dataSourceList.value = data.records;
  // 处理数据,显示字典值
  dataSourceList.value.forEach((item) => {
    item.toolStatusLable;
    const toolItem = sysDict.storage_tool_status.find(
      (dict) => dict.value === item.toolStatus
    );
    if (toolItem) {
      item.toolStatusLable = toolItem.label;
    } else {
      console.warn(`未找到对应字典值：${item.toolStatus}`);
      item.toolStatusLable = "";
    }

    const phaseItem = sysDict.storage_rule_phase.find(
      (dict) => dict.value === item.phase
    );
    if (phaseItem) {
      item.phaseLable = phaseItem.label;
    } else {
      console.warn(`未找到对应字典值：${item.phase}`);
      item.phaseLable = "";
    }

    item.useStatusLable = item.useStatus === "Y" ? true : false;
  });
  total.value = data.total;
  loading.value = false;
}

// 数据表格-单元格点击事件
const cellClick = (row, column, cellValue, event) => {};
// 数据表格-操作列
// 查看规则
function handleLook(row) {
  if (!drawerRef.value) {
    console.warn("抽屉实例不存在");
    return;
  }
  console.log("查看规则-------", row);
  modal.isEditForm = false;
  modal.isEdit = false;
  modal.isLook = true;
  modal.open = true;
  modal.title = "词表信息";
  modal.form = {
    interactiveType: "平台发送",
  };
  drawerRef.value.openDrawer();
}

// 替换词表
function handleReplace(row) {
  if (!drawerRef.value) {
    console.warn("抽屉实例不存在");
    return;
  }
  console.log("替换词表-------", row);
  modal.isEditForm = false;
  modal.isEdit = true;
  modal.isLook = false;
  modal.open = true;
  modal.title = "替换词表";
  modal.form = { ...row };
  modal.form.sampleFile = "";
  modal.form.fileList = [];
  drawerRef.value.openDrawer();
}
// 编辑属性
function handleEdit(row) {
  if (!drawerRef.value) {
    console.warn("抽屉实例不存在");
    return;
  }
  console.log("编辑规则-------", row);
  modal.isEdit = true;
  modal.isEditForm = true;
  modal.isLook = false;
  modal.open = true;
  modal.title = "编辑属性";
  console.log("编辑规则-------", row);
  modal.form = { ...row };
  modal.form.sampleFile = "";
  modal.form.fileList = [];
  drawerRef.value.openDrawer();
}

// 日志
function handleLog(row) {
  console.log("日志规则-------", row);
}

// 删除规则
async function handleDelete(row) {
  const confirmRes = await proxy.$modal
    .confirm("确认要删除当前规则吗?")
    .catch(() => {});
  if (!confirmRes) return;
  const localIds = row ? [row.id] : ids.value;
  await deleteRuleNormalWord(localIds);
  proxy.$modal.msgSuccess("删除成功");
  await getList();
}
// -------------- TableForm slot
// -------------- MyDrawer
// 抽屉-dom实例
const drawerRef = ref(null);
const rightFormRef = ref(null);
// 抽屉-自定义表单确定
const handleSave = async () => {
  if (!drawerRef.value) {
    console.warn("抽屉实例不存在");
    return;
  }
  console.log("抽屉-自定义表单确定", rightFormRef.value);
  if (rightFormRef.value && rightFormRef.value.submitForm) {
    // 调用表单提交方法
    await rightFormRef.value.submitForm();
    // 刷新列表
    getList();
  } else {
    console.warn("表单实例不存在或没有submitForm方法");
  }
};
const handleReplaceFile = async () => {
  if (!drawerRef.value) {
    console.warn("抽屉实例不存在");
    return;
  }
  console.log("抽屉-自定义表单确定", rightFormRef.value);
  if (rightFormRef.value && rightFormRef.value.replaceFile) {
    // 调用表单提交方法
    await rightFormRef.value.replaceFile();
    // 刷新列表
    getList();
  } else {
    console.warn("表单实例不存在或没有submitForm方法");
  }
};
// 抽屉-自定义表单取消
const handleCancel = () => {
  drawerRef.value.closeDrawer();
};

// 表格选择行变化
const handleSelectionChange = (selection) => {
  selectRows.value = selection;
  ids.value = selection.map((item) => item.id);
  multiple.value = !selection.length;
};

// 页面加载时获取列表数据
onMounted(() => {
  getList();
});
</script>
<style scoped lang="scss">
@import "@/components/Business/base.scss";
:deep(.table-container) {
  height: calc(100vh - 44px - 68px - 40px - 44px);
}
.tableForm-btns-top {
  height: 44px;
  display: flex;
  align-items: center;
  padding-left: 11px;
}

// 基础按钮样式
.btn {
  height: 32px;
  font-size: 14px;
  font-family: "Microsoft YaHei";
  color: #ffffff;
  border: 1px solid transparent;
  background-color: rgb(189, 189, 189);
  cursor: pointer;
  &--primary {
    background-color: rgb(0, 120, 212);
    &:hover {
      background-color: #2192e9ff;
    }
    &:active {
      background-color: #0069d4ff;
    }
  }

  &--danger {
    background-color: #f56c6c;
    &:hover {
      background-color: #f78989ff;
    }
    &:active {
      background-color: #ef6761ff;
    }

    &.is-disabled,
    &[disabled] {
      background-color: #f78989ff;
      color: #ffffff;
      border-color: transparent;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
}

.operation-btn {
  vertical-align: middle;
  color: #0076d0;
  font-weight: 500;
  cursor: pointer;
}
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  width: 600px;
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}
</style>
