<template>
  <ViewBody title="规则管理">
    <template #content>
      <!-- 搜索菜单 -->
      <FormSearch
        v-model="queryParams"
        :formItems="ruleFormItems"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <template #btn>
          <el-button class="my_primary_btn" type="success" @click="handleAdd"
            >新建规则
          </el-button>
          <el-button type="warning" plain icon="Download" @click="handleExport"
            >导出规则
          </el-button>
          <el-button
            type="danger"
            plain
            icon="Delete"
            @click="handleDelete()"
            :disabled="multiple"
            >删除规则
          </el-button>
        </template>
      </FormSearch>

      <!-- 表格部分 -->
      <TableForm
        ref="ruleTableFormRef"
        :columns="columns"
        :tableData="dataList"
        v-loading="loading"
        :showIndex="true"
        :showOtherColumn="true"
        tableotherColumnLabel="操作"
        :total="total"
        :isShowSearchQuery="false"
        :isShowSelection="true"
        @selection-change="handleSelectionChange"
        @cell-click="handleCellClick"
      >
        <!-- 自定义操作按钮 -->
        <template #otherOperation="{ row }">
          <!-- 使用 flex 布局确保按钮在同一行 -->
          <div class="operation-buttons">
            <span
              v-if="
                row.ruleStatus !== 'EXECUTING' &&
                row.toolStatus !== 'DATA_RECEIVED_FAIL'
              "
              class="operation-button"
              @click="handleLook(row.id, row.ruleValidId)"
            >
              查看
            </span>
            <span class="operation-button" @click="handleUpdate(row)">
              编辑
            </span>
            <!-- 更多操作下拉菜单 -->
            <el-dropdown
              @command="(command) => handleMoreAction(command, row)"
              trigger="click"
              class="operation-dropdown"
            >
              <span class="operation-button">
                更多
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="row.useStatus === 'N'"
                    command="delete"
                    icon="Delete"
                    >删除
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="row.useStatus === 'Y'"
                    command="send"
                    icon="Promotion"
                    >发送
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="
                      row.toolStatus !== 'DATA_RECEIVED_FAIL' &&
                      row.toolStatus !== 'INIT_FAIL'
                    "
                    command="verify"
                    icon="CircleCheck"
                    >验证
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="
                      row.toolStatus === 'DATA_RECEIVED_FAIL' ||
                      row.toolStatus === 'INIT_FAIL'
                    "
                    command="resend"
                    icon="Refresh"
                    >重新发送
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="
                      row.toolStatus === 'DATA_RECEIVED_FAIL' ||
                      row.toolStatus === 'INIT_FAIL'
                    "
                    command="failReason"
                    icon="Warning"
                    >失败理由
                  </el-dropdown-item>
                  <el-dropdown-item command="log" icon="Document"
                    >日志
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>

        <template #pagination>
          <Pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </template>

        <template #drawer>
          <!-- 右侧的抽屉内容 -->
          <DrawerForm ref="drawerRef">
            <template #header="{ titleId, titleClass }">
              <div :id="titleId" :class="titleClass" class="my_drawer_title">
                <span class="active">{{ modal.title }}</span>
                <div class="my_drawer_title_right">
                  <span class="btn_add" @click="handleSave">保存</span>
                  <span class="btn_cancel" @click="drawerRef?.closeDrawer()"
                    >取消</span
                  >
                </div>
              </div>
            </template>
            <template #form>
              <RightDrawForm
                ref="drawerFormRef"
                :modal="modal"
                @ok="getList"
              ></RightDrawForm>
            </template>
          </DrawerForm>
        </template>
      </TableForm>
    </template>
  </ViewBody>

  <!-- 验证规则弹窗 -->
  <RuleValidationDialog :modal="vaildationModal" @success="getList" />
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance, toRefs } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import {
  deleteRuleBase,
  listRuleBase,
  resendRuleBaseApi,
  STORAGE_RULE_BASE_URL,
  updateRuleBase,
  queryRuleBaseSelect,
} from "@/api/processingtool/ruleBase.js";
import {
  FormSearch,
  TableForm,
  DrawerForm,
  ViewBody,
  Pagination,
} from "@/components/Business";
import RightDrawForm from "./RightDrawForm.vue";
import RuleValidationDialog from "./RuleValidationDialog.vue";
import { queryDataOriginSelect } from "@/api/searchbase/dataOrigin.js";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 系统字典
const sysDict = reactive(
  proxy.useDict(
    "storage_pub_type",
    "storage_use_status",
    "storage_rule_valid_status",
    "storage_tool_status",
    "storage_rule_status"
  )
);

// 业务字典
const bizDict = reactive({
  dataOriginOptions: [],
  ruleBaseSelectOptions: [],
});

// 表单引用
const ruleTableFormRef = ref(null);
const drawerRef = ref(null);
const drawerFormRef = ref(null);

const dataList = ref([]);
const loading = ref(true);
const total = ref(1);
const multiple = ref(true);
const ids = ref([]);
const selectRows = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    ruleType: "ANALYSIS",
    searchCreatTime: "",
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

// 弹窗数据
const modal = reactive({
  isEdit: false,
  title: "新增规则",
  open: false,
  form: form,
  dict: {
    sysDict: sysDict,
    bizDict: bizDict,
  },
  arrayField: ["processFlow", "analysisRuleId"],
});

// 弹窗数据
const vaildationModal = reactive({
  isEdit: false,
  open: false,
  ruleBaseSelectFlag: false,
  form: {
    ruleId: "",
  },
  dict: {
    sysDict: sysDict,
    bizDict: bizDict,
  },
});

// 规则管理搜索表单配置
const ruleFormItems = computed(() => {
  console.log("计算ruleFormItems时的bizDict:", bizDict.value);
  console.log("dataOriginOptions:", bizDict.dataOriginOptions);
  return [
    {
      label: "规则名称",
      prop: "name",
      component: "el-input",
      props: {
        placeholder: "请输入规则名称",
        clearable: true,
        style: { width: "200px" },
      },
    },
    {
      label: "数据源",
      prop: "sourceId",
      component: "el-select",
      props: {
        placeholder: "数据源",
        clearable: true,
        style: { width: "200px" },
      },
      children: () =>
        bizDict.dataOriginOptions.map((item) => ({
          label: item.text,
          value: item.value,
        })),
    },
    {
      label: "处理状态",
      prop: "ruleStatus",
      component: "el-select",
      props: {
        placeholder: "处理状态",
        clearable: true,
        style: { width: "200px" },
      },
      children: () => sysDict.storage_rule_status,
    },
    {
      label: "工具交互状态",
      prop: "toolStatus",
      component: "el-select",
      props: {
        placeholder: "工具交互状态",
        clearable: true,
        style: { width: "200px" },
      },
      children: () => sysDict.storage_tool_status,
    },
    {
      label: "应用状态",
      prop: "useStatus",
      component: "el-select",
      props: {
        placeholder: "应用状态",
        clearable: true,
        style: { width: "200px" },
      },
      children: () => sysDict.storage_use_status,
    },
  ];
});

// 规则管理表格列配置
const columns = ref([
  { prop: "sourceId_dict", label: "数据源", width: "120" },
  { prop: "name", label: "规则名称" },
  { prop: "docType_dict", label: "文献类型", width: "100" },
  { prop: "ruleStatus_dict", label: "规则处理状态", width: "120" },
  { prop: "toolStatus_dict", label: "工具交互状态", width: "120" },
  {
    prop: "useStatus_dict",
    label: "应用状态",
    width: "100",
    type: "switch",
    activeText: "启用",
    inactiveText: "停用",
  },
  { prop: "createTime", label: "规则创建时间", width: "180" },
  { prop: "updateTime", label: "规则更新时间", width: "180" },
]);

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  try {
    const { data } = await listRuleBase(queryParams.value);
    // 处理数据字典映射
    dataList.value = data.records.map((row) => {
      return {
        ...row,
        ...mapRuleDictLabels(row),
      };
    });
    total.value = data.total;
  } catch (error) {
    console.error("获取规则列表失败:", error);
    proxy.$modal.msgError("获取规则列表失败");
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  console.log("=== 新增按钮点击 ===");
  modal.title = "新增规则";
  modal.isEdit = false;
  modal.form = {
    useStatus: "N",
  };
  // 打开抽屉
  drawerRef?.value.openDrawer();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  modal.title = "编辑规则";
  modal.isEdit = true;
  modal.form = { ...row };
  // 打开抽屉
  drawerRef?.value.openDrawer();
}

/** 删除按钮操作 */
async function handleDelete(row) {
  console.log("删除", row);
  if (!row) {
    const hasActiveStatusRows = selectRows.value.filter(
      (item) => item.useStatus === "Y"
    );
    console.log("选择数据", hasActiveStatusRows);
    if (hasActiveStatusRows.length > 0) {
      proxy.$modal.msgError("选择数据中包含已启用状态数据，无法进行删除！");
      return;
    }
  }
  const localIds = row ? [row.id] : ids.value;
  const selectNameArr = row
    ? row.name
    : selectRows.value.map((item) => item.name);
  const confirmRes = await proxy.$modal
    .confirm('是否确认删除规则名称为"' + selectNameArr + '"的数据项？')
    .catch(() => {});
  if (!confirmRes) return;
  await deleteRuleBase(localIds);
  proxy.$modal.msgSuccess("删除成功！");
  await getList();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    STORAGE_RULE_BASE_URL + "/export",
    {
      ...queryParams.value,
    },
    `规则数据_${new Date().getTime()}.xlsx`
  );
}

/** 查看按钮操作 */
function handleLook(ruleId, ruleValidId) {
  if (ruleId == null || ruleId === "") {
    proxy.$modal.msgError("规则ID为空！");
    return;
  }
  if (ruleValidId == null || ruleValidId === "") {
    proxy.$modal.msgError("规则效验ID为空！");
    return;
  }
  // 跳转规则结果详情页
  console.log("ruleId", ruleId);
  console.log("ruleValidId", ruleValidId);
  router.push({
    name: "ParsingmappingDetail",
    query: {
      ruleId: ruleId,
      ruleValidId: ruleValidId,
    },
  });
}

/** 发送操作 */
function handleSend(row) {
  console.log("发送", row);
}

/** 规则验证操作 */
function handleVerify(row) {
  console.log("验证", row);
  vaildationModal.open = true;
  vaildationModal.ruleBaseSelectFlag = false;
  vaildationModal.form = {
    ruleId: row.id,
  };
}

/** 重新发送操作 */
async function handleResend(row) {
  console.log("重新发送", row);
  const confirmRes = await proxy.$modal
    .confirm('是否确认重新发送规则名称为"' + row.name + '"的数据项？')
    .catch(() => {});
  if (!confirmRes) return;
  await resendRuleBaseApi(row.id);
  proxy.$modal.msgSuccess("重新发送成功！");
  await getList();
}

/** 失败理由操作 */
function handleFailReason(row) {
  console.log("失败理由", row);
}

/** 日志操作 */
function handleLog(row) {
  console.log("日志", row);
}

// 通用操作方法
async function handleUseStatusChange(row) {
  console.log("接口应用状态改变", row);
  // 转换开关值为 Y/N 格式
  // const newStatus = row.useStatus ? "Y" : "N";
  const newStatus = row.useStatus === "N" ? "Y" : "N";

  let text = newStatus === "Y" ? "启用" : "停用";
  const confirmRes = await proxy.$modal
    .confirm("确认要" + text + "吗?")
    .catch(() => {});
  if (!confirmRes) {
    // 恢复原状态 (不做处理)
    // row.useStatus = oldStatus === "Y";
    return;
  }
  try {
    // 准备更新数据，确保使用 Y/N 格式
    const updateData = { ...row, useStatus: newStatus };
    await updateRuleBase(updateData);
    proxy.$modal.msgSuccess(text + "成功");

    // 更新本地数据
    row.useStatus = newStatus;
  } catch (error) {
    // 更新失败，恢复原状态
    row.useStatus_dict = newStatus === "N";
    console.log(text + "失败", error);
  }
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  selectRows.value = selection;
  console.log("多选框选中数据", selectRows.value);
  multiple.value = !selection.length;
}

// 处理单元格点击事件（用于开关状态变化）
function handleCellClick(row, column, cellValue, event) {
  // 如果是应用状态列的开关变化
  console.log("单元格点击事件", row, column, cellValue, event);
  if (column.property === "useStatus_dict") {
    handleUseStatusChange(row);
  }
}

/** 处理更多操作下拉菜单命令 */
function handleMoreAction(command, row) {
  console.log("更多操作下拉菜单命令", command);
  console.log("当前行数据", row);
  switch (command) {
    case "delete":
      handleDelete(row);
      break;
    case "send":
      handleSend(row);
      break;
    case "verify":
      handleVerify(row);
      break;
    case "resend":
      handleResend(row);
      break;
    case "failReason":
      handleFailReason(row);
      break;
    case "log":
      handleLog(row);
      break;
    default:
      console.warn("未知的操作命令:", command);
  }
}

// 抽屉保存方法
const handleSave = async () => {
  await drawerFormRef?.value.submitForm();
  drawerRef?.value.closeDrawer();
};

// 映射规则管理字典标签
const mapRuleDictLabels = (row) => {
  return {
    sourceId_dict: getDataSourceText(row.sourceId),
    docType_dict: getDictLabel(sysDict.storage_pub_type, row.docType),
    ruleStatus_dict: getDictLabel(sysDict.storage_rule_status, row.ruleStatus),
    toolStatus_dict: getDictLabel(sysDict.storage_tool_status, row.toolStatus),
    // 转换 useStatus 为布尔值供开关组件使用
    useStatus_dict: row.useStatus === "Y",
  };
};

// 获取字典标签（优化版本）
const getDictLabel = (options, value) => {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
};

// 根据数据源ID获取数据源名称
const getDataSourceText = (sourceId) => {
  if (!sourceId || !bizDict.dataOriginOptions) return sourceId;
  const option = bizDict.dataOriginOptions.find(
    (item) => item.value === sourceId
  );
  console.log("根据数据源ID获取数据源名称", bizDict.dataOriginOptions);
  console.log("根据数据源ID获取数据源名称", option);
  return option ? option.text : sourceId;
};

/**获取对应的数据源字典信息*/
async function getDataOriginSelect() {
  const { data } = await queryDataOriginSelect();
  console.log("获取数据源字典信息", data);
  bizDict.dataOriginOptions = data;
}

/**获取解析规则字典信息*/
async function getRuleBaseSelect() {
  // , ruleStatus: 'COMPLETED'
  const { data } = await queryRuleBaseSelect({ ruleType: "ANALYSIS" });
  console.log("获取解析规则字典信息", data);
  bizDict.ruleBaseSelectOptions = data;
}

// 初始化数据
getRuleBaseSelect();
getDataOriginSelect();
getList();
</script>

<style lang="scss" scoped>
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}

.operation-buttons {
  display: flex;
  align-items: center;
}

.operation-button {
  margin-left: 10px;
  color: #0076d0;
  font-weight: 500;
  cursor: pointer;
  font-size: 12px;
}

.operation-dropdown {
  //margin-left: 10px;
  font-size: 12px;
  line-height: 23px;
}
</style>
