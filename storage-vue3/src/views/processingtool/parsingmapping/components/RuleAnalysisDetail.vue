<template>
  <ViewBody title="规则分析详情">
    <template #content>
      <!-- 搜索菜单 -->
      <FormSearch
        v-model="queryParams"
        :formItems="formItems"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <template #btn>
          <el-button type="primary" icon="Plus" @click="handleAdd"
            >新建路径</el-button
          >
          <el-button
            type="success"
            icon="Check"
            @click="handleAccept()"
            :disabled="multiple"
            >接受路径</el-button
          >
          <el-button
            type="warning"
            icon="Close"
            @click="handleIgnore()"
            :disabled="multiple"
            >忽略路径</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            @click="handleDelete()"
            :disabled="multiple"
            >删除路径</el-button
          >
          <el-button
            color="#626aef"
            icon="DocumentSave"
            @click="handleSave"
            :disabled="true"
            >保存规则
          </el-button>
          <el-button
            type="primary"
            icon="Position"
            @click="handleSend"
            :disabled="multiple"
            >发送规则</el-button
          >
          <el-button type="warning" icon="Download" @click="handleExport"
            >导出规则
          </el-button>
        </template>
      </FormSearch>

      <!-- 表格部分 -->
      <TableForm
        ref="ruleTableFormRef"
        :columns="columns"
        :tableData="dataList"
        :total="total"
        v-loading="loading"
        :isShowSelection="true"
        :isShowSearchQuery="false"
        :showIndex="true"
        @selection-change="handleSelectionChange"
        @handleClick="handleClick"
        @cellClick="handleCellClick"
      >
        <template #pagination>
          <Pagination
            v-if="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </template>

        <template #drawer>
          <DrawerForm ref="drawerRef">
            <template #header="{ titleId, titleClass }">
              <div :id="titleId" :class="titleClass" class="my_drawer_title">
                <div class="my_drawer_title_right">
                  <span
                    class="btn_add"
                    :class="{ disabled: selectData.analysisStatus === 'Y' }"
                    @click="handleAccept(selectData)"
                    >接受</span
                  >
                  <span
                    class="btn_cancel"
                    @click="handleIgnore(selectData)"
                    :class="{ disabled: selectData.analysisStatus === 'N' }"
                    >忽略</span
                  >
                  <span class="btn_add" @click="handleEditItem(selectData)"
                    >编辑</span
                  >
                  <span class="key-id-text">关键ID</span>
                  <el-switch
                    v-model="selectData.iskey"
                    active-value="Y"
                    inactive-value="N"
                    @click="handleItemIsKeyChange(selectData)"
                  />
                  <span
                    class="menu-title-btn"
                    :class="{ 'menu-title-btn-gray': currentIndex <= 0 }"
                    @click="handlePrevItem"
                    >上一条</span
                  >
                  <em class="page-info">{{ currentIndex + 1 }}/{{ total }}</em>
                  <span
                    class="menu-title-btn"
                    style="margin-right: 0px"
                    :class="{
                      'menu-title-btn-gray': currentIndex >= total - 1,
                    }"
                    @click="handleNextItem"
                    >下一条</span
                  >
                </div>
              </div>
            </template>

            <template #form>
              <div class="form-container">
                <el-form
                  :model="selectData"
                  label-width="140px"
                  size="small"
                  label-position="left"
                >
                  <el-form-item label="路径ID">
                    <el-input :disabled="true" v-model="selectData.pathId" />
                  </el-form-item>
                  <el-form-item label="原始路径" class="form-item-52">
                    <el-input
                      :disabled="!isEditing"
                      v-model="selectData.srcPath"
                      type="textarea"
                      :rows="2"
                    />
                  </el-form-item>
                  <el-form-item label="映射路径" class="form-item-52">
                    <el-input
                      :disabled="!isEditing"
                      v-model="selectData.imiPath"
                      type="textarea"
                      :rows="2"
                    />
                  </el-form-item>
                  <el-form-item label="IMI标签">
                    <el-input
                      :disabled="!isEditing"
                      v-model="selectData.imiLabel"
                    />
                  </el-form-item>
                  <el-form-item label="IMI标签类型">
                    <el-input
                      :disabled="!isEditing"
                      v-model="selectData.imiType"
                    />
                  </el-form-item>
                  <el-form-item label="出现频次">
                    <el-input
                      :disabled="!isEditing"
                      v-model="selectData.frequency"
                    />
                  </el-form-item>
                  <el-form-item label="标签值样例">
                    <el-input
                      :disabled="!isEditing"
                      v-model="selectData.sampleValue"
                    />
                  </el-form-item>
                  <el-form-item label="样例文件存储路径">
                    <el-input
                      :disabled="!isEditing"
                      v-model="selectData.samplePath"
                    />
                  </el-form-item>
                  <el-form-item label="启用该路径">
                    <el-switch
                      v-model="selectData.status"
                      active-value="Y"
                      inactive-value="N"
                      active-text="启用"
                      inactive-text="停用"
                      @click="handleUseStatusChange(selectData, false)"
                    />
                  </el-form-item>
                  <el-form-item label="状态" prop="analysisStatus">
                    <el-select
                      v-model="selectData.analysisStatus"
                      :disabled="true"
                      placeholder="状态"
                      clearable
                    >
                      <el-option
                        v-for="dict in sysDict.storage_analysis_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="映射生成时间">
                    <span>{{ selectData.createTime }}</span>
                  </el-form-item>
                  <el-form-item label="映射更新时间">
                    <span>{{ selectData.updateTime }}</span>
                  </el-form-item>
                </el-form>
              </div>
              <div class="drawer-footer" v-if="isEditing">
                <span class="menu-title-btn" @click="handleSaveEdit">保存</span>
                <span class="menu-title-btn" @click="cancelEdit">取消</span>
              </div>
            </template>
          </DrawerForm>
        </template>
      </TableForm>
    </template>
  </ViewBody>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { useRoute } from "vue-router";
import {
  addRuleAnalysisApi,
  listRuleAnalysis,
  STORAGE_RULE_ANALYSIS_URL,
  updateAnalysisStatusApi,
  updateApi,
} from "@/api/processingtool/ruleAnalysis.js";
import {
  FormSearch,
  TableForm,
  DrawerForm,
  ViewBody,
  Pagination,
} from "@/components/Business";

const { proxy } = getCurrentInstance();
const route = useRoute();

// 响应式数据
const dataList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const multiple = ref(true);
const ids = ref([]);
const selectRows = ref([]);
const dateRange = ref([]);
const selectData = ref({});
const isEditing = ref(false);
const originalSelectData = ref({});
const currentIndex = ref(0);

// 表单引用
const drawerRef = ref(null);

// 路由参数
const ruleId = ref(route.query.ruleId);
const ruleValidId = ref(route.query.ruleValidId);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    orderAsc: true,
    orderByCol: "sort",
    ruleId: route.query.ruleId,
    ruleValidId: route.query.ruleValidId,
  },
  rules: {},
});

const { queryParams } = toRefs(data);

// 系统字典
const sysDict = reactive({
  ...proxy.useDict("storage_use_status", "storage_analysis_status"),
});

// 搜索表单配置
const formItems = ref([
  {
    prop: "pathId",
    component: "el-input",
    props: {
      placeholder: "路径ID",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "imiLabel",
    component: "el-input",
    props: {
      placeholder: "IMI标签",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "status",
    component: "el-select",
    props: {
      placeholder: "启动状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_use_status,
  },
  {
    prop: "analysisStatus",
    component: "el-select",
    props: {
      placeholder: "处理状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_analysis_status,
  },
]);

// 表格列配置
const columns = ref([
  { prop: "pathId", label: "路径ID", minWidth: "140" },
  { prop: "sort", label: "排序码", minWidth: "80" },
  { prop: "srcPath", label: "原始路径", minWidth: "180" },
  { prop: "imiPath", label: "映射路径", minWidth: "180" },
  { prop: "imiLabel", label: "IMI标签", minWidth: "80" },
  { prop: "imiType", label: "IMI标签类型(属性)", minWidth: "140" },
  { prop: "frequency", label: "出现频次", minWidth: "80" },
  { prop: "sampleValue", label: "标签值样式", minWidth: "100" },
  {
    prop: "status_dict",
    label: "启用该路径",
    width: "100",
    type: "switch",
    activeText: "启用",
    inactiveText: "停用",
  },
  { prop: "analysisStatus_dict", label: "文献类型", width: "100" },
  { prop: "samplePath", label: "样例文件存储路径", minWidth: "160" },
  { prop: "createTime", label: "映射生成时间", width: "120", align: "center" },
  { prop: "updateTime", label: "映射更新时间", width: "120", align: "center" },
]);

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  try {
    const { data } = await listRuleAnalysis(queryParams.value);
    // 处理数据字典映射
    dataList.value = data?.records?.map((row) => {
      return {
        ...row,
        ...mapRuleDictLabels(row),
      };
    });
    total.value = data.total;
  } catch (error) {
    console.error("获取规则分析列表失败:", error);
    proxy.$modal.msgError("获取规则分析列表失败");
  } finally {
    loading.value = false;
  }
}

// 映射规则管理字典标签
const mapRuleDictLabels = (row) => {
  return {
    analysisStatus_dict: getDictLabel(
      sysDict.storage_analysis_status,
      row.analysisStatus
    ),
    // 转换 useStatus 为布尔值供开关组件使用
    status_dict: row.status === "Y",
  };
};

// 获取字典标签（优化版本）
const getDictLabel = (options, value) => {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
};

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  drawerRef.value.closeDrawer();
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  queryParams.value.pageSize = 20;
  queryParams.value.orderAsc = true;
  queryParams.value.orderByCol = "sort";
  queryParams.value.ruleId = route.query.ruleId;
  queryParams.value.ruleValidId = route.query.ruleValidId;
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    STORAGE_RULE_ANALYSIS_URL + "/export",
    {
      ...queryParams.value,
    },
    `规则路径数据_${new Date().getTime()}.xlsx`
  );
}

// 多选框选中数据
function handleSelectionChange(selection) {
  console.log("多选框选中数据", selection);
  ids.value = selection.map((item) => item.id || item.pathId);
  selectRows.value = selection;
  multiple.value = !selection.length;
}

// 选中当前行
function handleClick(row) {
  console.log("选中的规则数据:", row);
}

// 处理单元格点击事件（用于开关状态变化）
function handleCellClick(row, column, cellValue, event) {
  // 如果是应用状态列的开关变化
  console.log("单元格点击事件", row, column, cellValue, event);

  if (column.property === "status_dict") {
    handleUseStatusChange(row, true);
  } else {
    drawerRef.value.openDrawer();
    selectData.value = row;
    currentIndex.value = dataList.value.findIndex(
      (item) => item.pathId === row.pathId
    );
  }
}

// 通用操作方法
async function handleUseStatusChange(row, flag) {
  console.log("应用状态改变", row);
  // 预先计算切换后的状态和提示文本
  let text = "";
  if (flag) {
    const newStatus = row.status === "Y" ? "N" : "Y";
    text = newStatus === "Y" ? "启用" : "停用";
  } else {
    text = row.status === "Y" ? "启用" : "停用";
  }
  const confirmRes = await proxy.$modal
    .confirm("确认要" + text + "吗?")
    .catch(() => {});
  if (!confirmRes) {
    // 用户取消操作，不需要做任何处理，因为状态还没有改变
    return;
  }
  // 更新状态
  if (flag) {
    row.status = newStatus;
  }
  const updateRes = await updateApi(row).catch(() => {
    // 如果更新失败，恢复原状态
    row.status = row.status === "Y" ? "N" : "Y";
  });
  if (!updateRes) return;
  proxy.$modal.msgSuccess(text + "成功");
}

// 新建规则
async function handleAdd() {
  console.log("新建路径");
  const confirmRes = await proxy.$modal
    .confirm("确认要新建路径吗?")
    .catch(() => {});
  if (!confirmRes) return;
  const data = {
    ruleId: ruleId.value,
    ruleValidId: ruleValidId.value,
  };
  await addRuleAnalysisApi(data);
  proxy.$modal.msgSuccess("新建路径成功");
  await getList();
}

// 接受规则
async function handleAccept(row) {
  console.log("接受路径", row);
  const confirmRes = await proxy.$modal
    .confirm("确认要接受选中的路径吗?")
    .catch(() => {});
  if (!confirmRes) return;
  const localIds = row ? [row.id] : ids.value;
  const data = {
    ruleId: ruleId.value,
    ruleValidId: ruleValidId.value,
    ids: localIds,
    status: "Y",
  };
  await updateAnalysisStatusApi(data);
  proxy.$modal.msgSuccess("接受成功");
  if (row) row.analysisStatus = "Y";
  await getList();
}

// 忽略规则
async function handleIgnore(row) {
  const confirmRes = await proxy.$modal
    .confirm("确认要忽略选中的路径吗?")
    .catch(() => {});
  if (!confirmRes) return;
  const localIds = row ? [row.id] : ids.value;
  const data = {
    ruleId: ruleId.value,
    ruleValidId: ruleValidId.value,
    ids: localIds,
    status: "N",
  };
  await updateAnalysisStatusApi(data);
  proxy.$modal.msgSuccess("忽略成功");
  if (row) row.analysisStatus = "N";
  await getList();
}

// 删除规则
async function handleDelete(row) {
  const confirmRes = await proxy.$modal
    .confirm("确认要删除选中的路径吗?")
    .catch(() => {});
  if (!confirmRes) return;
  const localIds = row ? [row.id] : ids.value;
  const data = {
    ruleId: ruleId.value,
    ruleValidId: ruleValidId.value,
    ids: localIds,
    status: "D",
  };
  await updateAnalysisStatusApi(data);
  proxy.$modal.msgSuccess("删除成功");
  await getList();
}

// 保存规则
async function handleSave() {
  console.log("保存规则");
  proxy.$modal.msgSuccess("保存成功");
}

// 发送规则
async function handleSend() {
  console.log("发送规则");
  proxy.$modal.msgSuccess("发送成功");
}

// 处理抽屉中的编辑按钮
function handleEditItem() {
  originalSelectData.value = JSON.parse(JSON.stringify(selectData.value));
  isEditing.value = true;
}

// 抽屉内部保存按钮操作
async function handleSaveEdit() {
  try {
    const submitData = JSON.parse(JSON.stringify(selectData.value));
    if (submitData.status !== undefined) {
      submitData.status = submitData.status ? 1 : 0;
    }
    console.log("转换后的提交数据:", submitData);
    await updateApi(submitData);
    originalSelectData.value = submitData;
    isEditing.value = false;
    proxy.$modal.msgSuccess("保存成功");
    await getList();
  } catch (error) {
    proxy.$modal.msgError("保存失败: " + error.message);
  }
}

// 抽屉内部取消编辑按钮操作
function cancelEdit() {
  selectData.value = JSON.parse(JSON.stringify(originalSelectData.value));
  isEditing.value = false;
}

// 抽屉内部关键ID切换操作
async function handleItemIsKeyChange(row) {
  console.log("切换状态", row);
  // 预先计算切换后的状态和提示文本
  const text = row.iskey === "Y" ? "开启" : "关闭";

  const confirmRes = await proxy.$modal
    .confirm("确认要" + text + "关键ID吗?")
    .catch(() => {});
  if (!confirmRes) {
    // 用户取消操作，不需要做任何处理，因为状态还没有改变
    return;
  }
  // 更新状态
  const updateRes = await updateApi(row).catch(() => {
    // 如果更新失败，恢复原状态
    row.iskey = row.iskey === "Y" ? "N" : "Y";
  });
  if (!updateRes) return;
  proxy.$modal.msgSuccess(text + "成功");
}

// 处理上一条数据
function handlePrevItem() {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    const prevRow = dataList.value[currentIndex.value];
    selectData.value = prevRow;
  }
}

// 处理下一条数据
function handleNextItem() {
  if (currentIndex.value < dataList.value.length - 1) {
    currentIndex.value++;
    const nextRow = dataList.value[currentIndex.value];
    selectData.value = nextRow;
  }
}

// 初始化
onMounted(() => {
  console.log("获取路由参数==>", route.query);
  queryParams.value.ruleId = route.query.ruleId;
  queryParams.value.ruleValidId = route.query.ruleValidId;
});

getList();
</script>

<style lang="scss" scoped>
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }

  .menu-title-btn {
    margin: 0 10px;
    padding: 5px 5px;
    border-radius: 4px;
    background-color: #fff;
    border-width: 1px;
    border-color: rgb(219, 219, 219);
    border-style: solid;
    cursor: pointer;
    box-sizing: border-box;
    color: rgb(0, 120, 212);
  }

  .menu-title-btn-gray {
    margin: 0 10px;
    padding: 5px 5px;
    border-radius: 4px;
    background-color: rgb(231, 235, 245);
    border-bottom-color: rgb(231, 235, 245);
    border-width: 1px;
    border-style: solid;
    cursor: pointer;
    box-sizing: border-box;
    color: rgb(153, 152, 152);
  }

  .page-info {
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 0, 0);
    font-style: normal;
  }

  .key-id-text {
    color: #f56c6c;
    margin: 0 4px;
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}

.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-button--small) {
    padding: 0px !important;
  }

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }

  :deep(.form-item-52 .el-form-item__label) {
    height: 52px !important;
    line-height: 52px !important;
  }

  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label-wrap
        > .el-form-item__label:before
    ),
  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label:before
    ) {
    display: none;
  }

  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  font-size: 14px;

  .menu-title-btn {
    margin-left: 4px;
    padding: 8px 5px;
    border-radius: 4px;
    background-color: #fff;
    border-width: 1px;
    border-color: rgb(219, 219, 219);
    border-style: solid;
    cursor: pointer;
    box-sizing: border-box;
    color: rgb(0, 120, 212);
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}
</style>
