<template>
  <div class="app-container">
    <el-tabs v-model="tabMode" class="tabs" @tab-change="handleTabChange">
      <el-tab-pane label="规则管理" name="0">
        <RuleManagement v-if="tabMode === '0'" />
      </el-tab-pane>
      <el-tab-pane label="规则验证" name="1">
        <RuleValidation v-if="tabMode === '1'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from "vue";
import RuleManagement from "./components/RuleManagement.vue";
import RuleValidation from "./components/RuleValidation.vue";

const tabMode = ref("0");
/** 处理标签页切换 */
function handleTabChange(activeName) {
  console.log("切换到标签页:", activeName);
  tabMode.value = activeName;
}
</script>

<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "@/components/Business/style/base.scss";
// 引入抽取的tabs容器样式
@import "@/components/Business/style/tabs-container.scss";
</style>
