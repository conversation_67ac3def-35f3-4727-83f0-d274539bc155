<template>
  <el-card class="detail-panel" shadow="never" :style="{ height: height + 'px' }">
    <template #header>
      <div class="detail-header">
        <span class="header-title">{{ selectData.attributeName || '元素名称' }}</span>
        <div class="header-actions">
          <el-button type="text" size="small" @click="handleClose()" class="close-btn">
            <el-icon>
              <CloseBold/>
            </el-icon>
            关闭
          </el-button>
        </div>
      </div>
    </template>

    <div class="detail-content" v-if="data">
      <!-- 第一部分：元素名称 -->
      <div class="element-name-section">
        <div class="section-content">
          <div class="element-info">
            <span class="label">元素名称：</span>
            <span class="value">{{ selectData.attributeName || '未选择' }}</span>
          </div>
        </div>
      </div>

      <!-- 第二部分：属性值管理 -->
      <div class="attribute-values-section">
        <div class="section-header">
          <h4 class="section-title">
            <el-icon>
              <Setting/>
            </el-icon>
            属性值
          </h4>
          <el-button type="primary" size="small" @click="handleAddAttribute" icon="Plus" class="add-btn">
            添加属性值
          </el-button>
        </div>
        <div class="attribute-values-content">
          <div class="attribute-list">
            <div
                v-for="(item, index) in ruleNormalizationWordDetailList"
                :key="item.id || index"
                class="attribute-item"
                :class="{ 'selected': selectedAttributeIndex === index }"
            >
              <div class="attribute-item-content">
                <div class="radio-section">
                  <el-radio-group v-model="selectedAttributeIndex" class="radio-group" @change="handleSelectedChange">
                    <el-radio :label="index" class="attribute-radio">
                      <span class="radio-label">{{ index + 1 }}</span>
                    </el-radio>
                  </el-radio-group>
                </div>
                <div class="input-section">
                  <el-input
                      v-model="item.attributeValue"
                      placeholder="请输入属性值"
                      class="attribute-input"
                      @blur="handleAttributeUpdate(item)"
                      clearable
                  />
                </div>
                <div class="switch-section">
                  <el-switch
                      v-model="item.attributeStatusBoolean"
                      class="attribute-switch"
                      @change="handleAttributeStatusChange(item)"
                      active-text="忽略"
                      inactive-text="忽略"
                      inline-prompt
                  />
                </div>
                <div class="action-section">
                  <el-button
                      type="danger"
                      size="small"
                      icon="Delete"
                      @click="handleDeleteAttribute(item, index)"
                      class="delete-btn"
                      text
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <div v-if="ruleNormalizationWordDetailList.length === 0" class="empty-state">
            <el-empty description="暂无属性值" :image-size="60">
<!--              <el-button type="primary" @click="handleAddAttribute">添加属性值</el-button>-->
            </el-empty>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <el-loading-spinner :size="30"/>
            <span style="margin-left: 8px; font-size: 12px; color: #909399;">加载中...</span>
          </div>
        </div>
      </div>

      <!-- 第三部分：属性值映射 -->
      <div class="attribute-mapping-section">
        <div class="section-header">
          <h4 class="section-title">
            <el-icon>
              <Connection/>
            </el-icon>
            属性值映射
          </h4>
          <el-button
              type="primary"
              size="small"
              @click="handleAddMapping"
              icon="Plus"
              :disabled="enabledAttributes.length === 0"
              class="add-btn"
          >
            添加映射
          </el-button>
        </div>
        <div class="mapping-table-content">
          <div v-if="mappingData.length > 0" class="table-wrapper">
            <el-table
                :data="mappingData"
                border
                size="small"
                class="mapping-table"
                :max-height="mappingTableHeight"
                stripe
            >
              <el-table-column
                  label="序号"
                  type="index"
                  width="60"
                  align="center"
                  fixed
              />
              <el-table-column
                  label="原属性"
                  prop="sourceAttribute"
                  align="center"
                  min-width="150"
              >
                <template #default="scope">
                                                        <el-select
                    v-model="scope.row.sourceAttribute"
                    placeholder="请选择原属性"
                    size="small"
                    style="width: 100%"
                    @change="handleSourceAttributeChange(scope.row, $event)"
                    clearable
                    filterable
                  >
                    <el-option
                        v-for="attr in enabledAttributes"
                        :key="attr.value"
                        :label="attr.label"
                        :value="attr.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                  label="目标属性"
                  prop="targetAttribute"
                  align="center"
                  min-width="150"
              >
                <template #default="scope">
                                                        <el-select
                    v-model="scope.row.targetAttribute"
                    placeholder="请选择目标属性"
                    size="small"
                    style="width: 100%"
                    @change="handleTargetAttributeChange(scope.row, $event)"
                    clearable
                    filterable
                  >
                    <el-option
                        v-for="attr in enabledAttributes"
                        :key="attr.value"
                        :label="attr.label"
                        :value="attr.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作"
                  width="80"
                  align="center"
                  fixed="right"
              >
                <template #default="scope">
                  <el-button
                      type="danger"
                      size="small"
                      icon="Delete"
                      @click="handleDeleteMapping(scope.$index)"
                      text
                      circle
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="empty-state">
            <el-empty description="暂无映射关系" :image-size="60">
            </el-empty>
          </div>
        </div>
      </div>
    </div>

    <div class="no-data" v-else>
      <el-empty description="暂无数据" :image-size="80">
      </el-empty>
    </div>
  </el-card>
</template>

<script setup>
import {ref, computed, watch, onMounted, nextTick, getCurrentInstance} from "vue";
import {CloseBold, Setting, Connection} from "@element-plus/icons-vue";
import {
  addRuleNormalizationWordDetailApi,
  listRuleNormalizationWordDetail,
  updateRuleNormalizationWordDetailApi,
  deleteRuleNormalizationWordDetailApi
} from "@/api/processingtool/ruleNormalization.js";

const {proxy} = getCurrentInstance();
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  selectId: {
    type: String,
    default: () => (''),
  },
  selectData: {
    type: Object,
    default: () => ({}),
  },
  height: {
    type: Number,
    default: 500,
  },
  title: {
    type: String,
    default: "title",
  },
});

const emit = defineEmits(["close"]);
const loading = ref(true);
const ruleNormalizationWordDetailList = ref([]);
const ruleNormalizationWordDetailMappingList = ref([]);

// 响应式数据
const data = ref(true);
const selectedAttributeIndex = ref(0);
const mappingTableHeight = ref(300);

// 映射数据
const mappingData = ref([]);

// 计算可用的属性值选项（忽略状态不影响映射选项）
const enabledAttributes = computed(() => {
  return ruleNormalizationWordDetailList.value
    .filter(attr => attr.attributeValue && attr.attributeValue.trim() !== '')
    .map(attr => ({
      value: attr.id,  // 使用属性值的ID作为value
      label: attr.attributeValue,  // 使用属性值作为显示的label
      attributeValue: attr.attributeValue  // 保留原属性值用于显示和保存
    }));
});

// 计算映射表格高度
const computeMappingTableHeight = () => {
  nextTick(() => {
    const availableHeight = props.height - 400; // 减去其他部分的高度
    mappingTableHeight.value = Math.max(200, Math.min(400, availableHeight));
  });
};

// 添加属性值
// 添加属性值
async function handleAddAttribute() {
  try {
    const newAttribute = {normalizationWordId: props.selectId, type: 0};
    const {data} = await addRuleNormalizationWordDetailApi(newAttribute);
    if (data) {
      await getRuleNormalizationWordDetailList();
      // 自动选中新添加的项（通常是最后一个）
      selectedAttributeIndex.value = ruleNormalizationWordDetailList.value.length - 1;
      // proxy.$modal.msgSuccess("添加属性值成功");
    }
  } catch (error) {
    console.error("添加属性值失败:", error);
    proxy.$modal.msgError("添加属性值失败");
  }
}

// 删除属性值
async function handleDeleteAttribute(item, index) {
  try {
    const confirmRes = await proxy.$modal.confirm(`确认要删除当前${item.attributeValue}属性值吗?`).catch(() => {
    });
    if (!confirmRes) return;

    await deleteRuleNormalizationWordDetailApi({id: item.id});

    // 重新加载数据
    await getRuleNormalizationWordDetailList();
    proxy.$modal.msgSuccess("删除成功");

    console.log(`删除索引 ${index} 的属性值，当前选中索引: ${selectedAttributeIndex.value}`);
  } catch (error) {
    console.error("删除属性值失败:", error);
    // proxy.$modal.msgError("删除失败");
  }
}

// 属性值更新处理（输入框失焦时调用）
async function handleAttributeUpdate(item) {
  try {
    if (!item.id) {
      console.warn("属性值项缺少ID，无法更新");
      return;
    }

    const updateData = {
      id: item.id,
      attributeValue: item.attributeValue,
      attributeStatus: item.attributeStatusBoolean ? 'Y' : 'N'
    };

    await updateRuleNormalizationWordDetailApi(updateData);
    console.log("属性值更新成功:", updateData);

  } catch (error) {
    console.error("更新属性值失败:", error);
    proxy.$modal.msgError("更新属性值失败");
    // 失败时重新加载数据以恢复原始状态
    await getRuleNormalizationWordDetailList();
  }
}

// 忽略状态变化处理
async function handleAttributeStatusChange(item) {
  try {
    if (!item.id) {
      console.warn("属性值项缺少ID，无法更新状态");
      return;
    }

    // 同步更新attributeStatus字段
    item.attributeStatus = item.attributeStatusBoolean ? 'Y' : 'N';

    const updateData = {
      id: item.id,
      attributeValue: item.attributeValue,
      attributeStatus: item.attributeStatus
    };

    await updateRuleNormalizationWordDetailApi(updateData);
    console.log("属性值状态更新成功:", updateData);

  } catch (error) {
    console.error("更新属性值状态失败:", error);
    proxy.$modal.msgError("更新属性值状态失败");
    // 失败时恢复原始状态
    item.attributeStatusBoolean = item.attributeStatus === 'Y';
  }
}

// 添加映射
async function handleAddMapping() {
  try {
    if (enabledAttributes.value.length === 0) {
      proxy.$modal.msgWarning("请先添加属性值");
      return;
    }

    const newMapping = {normalizationWordId: props.selectId, type: 1};
    const {data} = await addRuleNormalizationWordDetailApi(newMapping);
    if (data) {
      await getRuleNormalizationWordDetailMappingList();
      // proxy.$modal.msgSuccess("添加映射成功");
    }
  } catch (error) {
    console.error("添加映射失败:", error);
    proxy.$modal.msgError("添加映射失败");
  }
}

// 删除映射
async function handleDeleteMapping(index) {
  try {
    const mappingItem = mappingData.value[index];
    if (!mappingItem || !mappingItem.id) {
      console.warn("映射项缺少ID，无法删除");
      return;
    }

    const confirmRes = await proxy.$modal.confirm(`确认要删除当前映射关系吗?`).catch(() => {
    });
    if (!confirmRes) return;

    await deleteRuleNormalizationWordDetailApi({id: mappingItem.id});

    // 重新加载数据
    await getRuleNormalizationWordDetailMappingList();
    // proxy.$modal.msgSuccess("删除成功");

    console.log(`删除索引 ${index} 的映射关系`);
  } catch (error) {
    console.error("删除映射失败:", error);
    // proxy.$modal.msgError("删除失败");
  }
}

// 原属性选择变化处理
async function handleSourceAttributeChange(mappingItem, selectedValue) {
  try {
    if (!mappingItem || !mappingItem.id) {
      console.warn("映射项缺少ID，无法更新");
      return;
    }

    // 查找选中的属性信息
    const selectedAttr = enabledAttributes.value.find(attr => attr.value === selectedValue);
    if (selectedAttr) {
      mappingItem.sourceAttributeLabel = selectedAttr.label;
    }

    const updateData = {
      id: mappingItem.id,
      originalAttribute: mappingItem.sourceAttributeLabel,
      originalAttributeId: mappingItem.sourceAttribute,
      targetAttribute: mappingItem.targetAttributeLabel,
      targetAttributeId: mappingItem.targetAttribute
    };

    await updateRuleNormalizationWordDetailApi(updateData);
    console.log("原属性映射更新成功:", updateData);

  } catch (error) {
    console.error("更新原属性映射失败:", error);
    proxy.$modal.msgError("更新原属性映射失败");
    // 失败时重新加载数据以恢复原始状态
    await getRuleNormalizationWordDetailMappingList();
  }
}

// 目标属性选择变化处理
async function handleTargetAttributeChange(mappingItem, selectedValue) {
  try {
    if (!mappingItem || !mappingItem.id) {
      console.warn("映射项缺少ID，无法更新");
      return;
    }

    // 查找选中的属性信息
    const selectedAttr = enabledAttributes.value.find(attr => attr.value === selectedValue);
    if (selectedAttr) {
      mappingItem.targetAttributeLabel = selectedAttr.label;
    }

    const updateData = {
      id: mappingItem.id,
      originalAttribute: mappingItem.sourceAttributeLabel,
      originalAttributeId: mappingItem.sourceAttribute,
      targetAttribute: mappingItem.targetAttributeLabel,
      targetAttributeId: mappingItem.targetAttribute
    };

    await updateRuleNormalizationWordDetailApi(updateData);
    console.log("目标属性映射更新成功:", updateData);

  } catch (error) {
    console.error("更新目标属性映射失败:", error);
    proxy.$modal.msgError("更新目标属性映射失败");
    // 失败时重新加载数据以恢复原始状态
    await getRuleNormalizationWordDetailMappingList();
  }
}

// 选中项变化处理
function handleSelectedChange() {
  console.log("选中项变化:", selectedAttributeIndex.value);
}

// 关闭面板
function handleClose() {
  emit("close");
}

/** 查询属性值列表 */
async function getRuleNormalizationWordDetailList() {
  try {
    loading.value = true;
    const queryParams = {
      normalizationWordId: props.selectId,
      type: 0,
      orderByCol: 'createTime',
      orderAsc: true
    };
    const {data} = await listRuleNormalizationWordDetail(queryParams);
    ruleNormalizationWordDetailList.value = data || [];

    // 为每个数据项添加计算属性用于Switch组件
    ruleNormalizationWordDetailList.value.forEach(item => {
      item.attributeStatusBoolean = item.attributeStatus === 'Y';
    });

    // 设置选中状态 - 找到checkStatus为1的项
    const selectedIndex = ruleNormalizationWordDetailList.value.findIndex(item => item.checkStatus === 1);
    selectedAttributeIndex.value = selectedIndex !== -1 ? selectedIndex : 0;

    console.log("ruleNormalizationWordDetailList", ruleNormalizationWordDetailList.value);

  } catch (error) {
    console.error("获取属性值列表失败:", error);
    proxy.$modal.msgError("获取属性值列表失败");
  } finally {
    loading.value = false;
  }
}

/** 查询属性值映射列表 */
async function getRuleNormalizationWordDetailMappingList() {
  try {
    loading.value = true;
    const queryParams = {
      normalizationWordId: props.selectId,
      type: 1,
      orderByCol: 'createTime',
      orderAsc: true
    };
    const {data} = await listRuleNormalizationWordDetail(queryParams);
    ruleNormalizationWordDetailMappingList.value = data || [];

    // 直接使用原始数据映射到组件数据
    mappingData.value = ruleNormalizationWordDetailMappingList.value.map(item => ({
      id: item.id,
      sourceAttribute: item.originalAttributeId || '',  // 原属性ID
      sourceAttributeLabel: item.originalAttribute || '',  // 原属性值
      targetAttribute: item.targetAttributeId || '',  // 目标属性ID
      targetAttributeLabel: item.targetAttribute || '',  // 目标属性值
      originalData: item
    }));

    console.log("ruleNormalizationWordDetailMappingList", ruleNormalizationWordDetailMappingList.value);
    console.log("mappingData", mappingData.value);

  } catch (error) {
    console.error("获取属性值映射列表失败:", error);
    proxy.$modal.msgError("获取属性值映射列表失败");
  } finally {
    loading.value = false;
  }
}

// 监听props.selectData变化
watch(
    () => props.selectId,
    async (newData, oldData) => {
      await fetchDetailData();
      console.log("DetailPanel: 数据变化，触发接口调用", newData);
    },
    {immediate: true, deep: true}
);

// 监听高度变化
watch(
    () => props.height,
    () => {
      computeMappingTableHeight();
    },
    {immediate: true}
);

// 组件挂载时初始化
onMounted(() => {
  computeMappingTableHeight();
});

// 数据获取接口
async function fetchDetailData() {
  try {
    console.log("DetailPanel: selectId变化，触发接口调用", props.selectId);
    if (props.selectId) {
      // 根据selectId初始化attributeValues和mappingData
      await initializeData();
    } else {
      // 如果selectId为空，重置数据
      resetData();
    }
  } catch (error) {
    console.error("获取详细数据失败:", error);
  }
}

// 重置数据
function resetData() {
  mappingData.value = [];
  selectedAttributeIndex.value = 0;
  ruleNormalizationWordDetailList.value = [];
  ruleNormalizationWordDetailMappingList.value = [];
  console.log("数据已重置");
}

// 初始化数据
async function initializeData() {
  if (!props.selectId) {
    console.warn("selectId为空，无法初始化数据");
    return;
  }

  try {
    // 并行调用两个接口
    await Promise.all([
      getRuleNormalizationWordDetailList(),
      getRuleNormalizationWordDetailMappingList()
    ]);
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
}



</script>

<style scoped lang="scss">
.detail-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 8px;

  :deep(.el-card__header) {
    flex-shrink: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-card__body) {
    flex: 1;
    overflow: hidden;
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      font-weight: 600;
      color: #303133;
      font-size: 14px;
      display: flex;
      align-items: center;
    }

    .header-actions {
      .close-btn {
        padding: 3px 6px;
        color: #909399;
        transition: all 0.3s;
        font-size: 12px;

        &:hover {
          background-color: #f5f7fa;
          color: #409eff;
        }

        .el-icon {
          font-size: 12px;
          margin-right: 3px;
        }
      }
    }
  }

  .detail-content {
    flex: 1;
    overflow-y: auto;
    box-sizing: border-box;
    min-height: 0;
    padding: 16px;

    // 滚动条样式优化
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    // 元素名称部分
    .element-name-section {
      margin-bottom: 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 6px;
      overflow: hidden;

      .section-content {
        padding: 12px 16px;

        .element-info {
          color: white;
          font-size: 13px;

          .label {
            font-weight: 500;
            opacity: 0.9;
            font-size: 12px;
          }

          .value {
            font-weight: 600;
            margin-left: 3px;
            font-size: 14px;
          }
        }
      }
    }

    // 通用section样式
    .attribute-values-section,
    .attribute-mapping-section {
      margin-bottom: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background-color: #ffffff;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
      overflow: hidden;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #e4e7ed;

        .section-title {
          font-weight: 600;
          color: #303133;
          font-size: 12px;
          margin: 0;
          display: flex;
          align-items: center;

          .el-icon {
            margin-right: 6px;
            color: #409eff;
            font-size: 14px;
          }
        }

        .add-btn {
          font-size: 11px;
          padding: 4px 8px;
          height: 26px;

          .el-icon {
            font-size: 12px;
          }
        }
      }
    }

    // 属性值管理部分
    .attribute-values-section {
      .attribute-values-content {
        padding: 12px 16px;

        .attribute-list {
          .attribute-item {
            margin-bottom: 8px;
            padding: 8px;
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            transition: all 0.3s;
            background-color: #fafafa;

            &:last-child {
              margin-bottom: 0;
            }

            &.selected {
              border-color: #409eff;
              background-color: #ecf5ff;
              box-shadow: 0 2px 4px rgba(64, 158, 255, 0.12);
            }

            &:hover {
              border-color: #c6e2ff;
              background-color: #f0f9ff;
            }

            .attribute-item-content {
              display: grid;
              grid-template-columns: auto 1fr auto auto;
              gap: 8px;
              align-items: center;

              @media (max-width: 768px) {
                grid-template-columns: 1fr;
                gap: 6px;
              }

              .radio-section {
                .radio-group {
                  .attribute-radio {
                    margin-right: 0;

                    .radio-label {
                      font-weight: 500;
                      color: #606266;
                      font-size: 12px;
                    }

                    :deep(.el-radio__label) {
                      font-size: 12px;
                    }


                  }
                }
              }

              .input-section {
                .attribute-input {
                  :deep(.el-input__wrapper) {
                    border-radius: 4px;
                    min-height: 28px;
                  }

                  :deep(.el-input__inner) {
                    font-size: 13px;
                    font-weight: 500;
                    color: #303133;
                  }

                  :deep(.el-input__placeholder) {
                    font-size: 12px;
                  }
                }
              }

              .switch-section {
                .attribute-switch {
                  :deep(.el-switch__label) {
                    font-size: 10px;
                  }

                  :deep(.el-switch__core) {
                    min-width: 32px;
                    height: 16px;
                  }
                }
              }

              .action-section {
                .delete-btn {
                  transition: all 0.3s;
                  font-size: 11px;
                  padding: 4px 6px;

                  .el-icon {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }

        .empty-state {
          text-align: center;
          padding: 30px 16px;

          :deep(.el-empty__description) {
            font-size: 12px;
          }
        }

        .loading-state {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: #909399;
        }
      }
    }

    // 属性值映射部分
    .attribute-mapping-section {
      .mapping-table-content {
        padding: 12px 16px;

        .table-wrapper {
          border-radius: 4px;
          overflow: hidden;

          .mapping-table {
            :deep(.el-table__header) {
              th {
                background-color: #f8f9fa;
                font-weight: 600;
                color: #303133;
                font-size: 11px;
                padding: 8px 0;
              }
            }

            :deep(.el-table__body) {
              td {
                padding: 8px 0;
              }
            }

            :deep(.el-table--striped) {
              .el-table__body tr.el-table__row--striped td {
                background-color: #fafafa;
              }
            }

            :deep(.el-select) {
              .el-input__wrapper {
                min-height: 26px;
              }

              .el-input__inner {
                font-size: 12px;
                font-weight: 500;
                color: #303133;
              }

              .el-select__placeholder {
                font-size: 11px;
              }
            }

            :deep(.el-button) {
              padding: 2px;
              min-height: 24px;
              width: 24px;
            }
          }
        }

        .empty-state {
          text-align: center;
          padding: 30px 16px;

          :deep(.el-empty__description) {
            font-size: 12px;
          }
        }
      }
    }
  }

  .no-data {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 16px;
    box-sizing: border-box;
    min-height: 0;

    :deep(.el-empty__description) {
      font-size: 12px;
    }

    :deep(.el-button) {
      font-size: 12px;
      padding: 6px 12px;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .detail-content {
      padding: 12px;

      .attribute-values-section,
      .attribute-mapping-section {
        margin-bottom: 16px;

        .section-header {
          padding: 12px 16px;
          flex-direction: column;
          gap: 8px;
          align-items: stretch;

          @media (min-width: 600px) {
            flex-direction: row;
            align-items: center;
          }
        }
      }

      .attribute-values-section {
        .attribute-values-content {
          padding: 12px 16px;
        }
      }

      .attribute-mapping-section {
        .mapping-table-content {
          padding: 12px 16px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .detail-header {
      .header-title {
        font-size: 12px;
      }
    }

    .detail-content {
      padding: 6px;

      .element-name-section {
        .section-content {
          padding: 10px 12px;

          .element-info {
            font-size: 11px;

            .label {
              font-size: 10px;
            }

            .value {
              font-size: 12px;
            }
          }
        }
      }

      .attribute-values-section {
        .section-header {
          .section-title {
            font-size: 11px;
          }

          .add-btn {
            font-size: 10px;
            padding: 3px 6px;
            height: 24px;
          }
        }

        .attribute-item {
          .attribute-item-content {
            grid-template-columns: 1fr;
            gap: 6px;

            .radio-section,
            .switch-section,
            .action-section {
              justify-self: start;
            }
          }
        }
      }

      .attribute-mapping-section {
        .section-header {
          .section-title {
            font-size: 11px;
          }

          .add-btn {
            font-size: 10px;
            padding: 3px 6px;
            height: 24px;
          }
        }
      }
    }
  }

  // 动画效果
  .attribute-item,
  .section-header,
  .mapping-table {
    transition: all 0.3s ease;
  }

  // 按钮hover效果增强
  :deep(.el-button) {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
