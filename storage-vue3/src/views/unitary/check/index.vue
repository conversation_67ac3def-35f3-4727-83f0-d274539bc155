<template>
  <div class="app-container">
    <div class="content-container">
      <!-- 左侧 -->
      <div class="left-container">
        <Left></Left>
      </div>
       <!-- 右侧 -->
       <div class="right-container">
        <Right></Right>
      </div>
    </div>
  </div>
   
</template>

<script setup name="unifyArticle">
import { ref } from 'vue';
import { listArticles } from "@/api/unitary/unitary";
import { useRouter } from 'vue-router';
import MyPagination from "@/views/citationManager/components/Pagination/new.vue";
import FormSearch from "@/views/citationManager/components/FormSearch.vue";
import TableForm from "@/views/citationManager/components/TableForm.vue";
import MyDrawer from "@/views/citationManager/components/MyDrawer.vue";
import RightDrawForm from "./components/RightDrawForm.vue";
import viewBody from "@/views/citationManager/components/viewBody.vue";
import Left from "./components/Left.vue"
import Right from "./components/Right.vue"
const router = useRouter();
const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const articleList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  articleTitle: undefined,
  sourceTitle: undefined,
  dataSource: undefined,
  status: undefined
});

// 搜索表单配置
const formItems = ref([
  {
    prop: 'articleTitle',
    component: 'el-input',
    props: {
      placeholder: '请输入篇级题名',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    prop: 'sourceTitle',
    component: 'el-input',
    props: {
      placeholder: '请输入期刊题名',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    prop: 'dataSource',
    component: 'el-input',
    props: {
      placeholder: '请输入数据源',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    prop: 'status',
    component: 'el-select',
    props: {
      placeholder: '数据状态',
      clearable: true,
      style: {width: '200px'},
    },
    children: () => [
      { value: '0', label: '疑似' },
      { value: '1', label: '新增' },
      { value: '2', label: '挂接' }
    ]
  }
]);

// 添加表格列配置
const columns = ref([
  {prop: 'originalArticleId', label: '篇级ID', width: '150', showOverflowTooltip: true},
  {prop: 'articleTitle', label: '篇级题名', width: '200', showOverflowTooltip: true},
  {prop: 'sourceTitle', label: '期刊题名', width: '180', showOverflowTooltip: true},
  {prop: 'issn', label: 'ISSN', width: '100', showOverflowTooltip: true},
  {prop: 'interval', label: '年/卷/期', width: '100', showOverflowTooltip: true,
    formatter: (row) => {
      const parts = [];
      if (row.pubYear) parts.push(row.pubYear);
      if (row.volume) parts.push(row.volume);
      if (row.issue) parts.push(row.issue);
      return parts.join('/');
    }
  },
  {prop: 'sources', label: '数据源', width: '100'},
  {prop: 'status', label: '归一结果', width: '100'},
  {prop: 'remark', label: '疑似说明', width: '150', showOverflowTooltip: true},
  {prop: 'updateTime', label: '更新时间', width: '160', showOverflowTooltip: true}
]);

/** 查询文章列表 */
async function getList() {
  loading.value = true;
  try {
    const res = await listArticles(queryParams.value);
    // 处理年卷期字段
    articleList.value = res.rows.map(row => {
      const parts = [
        row.pubYear || '-',
        row.volume || '-',
        row.issue || '-'
      ];
      return {
        ...row,
        interval: parts.join('/')
      };
    });
    total.value = res.total;
  } catch (error) {
    console.error('获取列表失败:', error);
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    articleTitle: undefined,
    sourceTitle: undefined,
    dataSource: undefined,
    state: undefined
  };
  handleQuery();
}

/** 处理按钮操作 */
function handleProcess(row) {
  const { articleId, state } = row;

  // 根据不同的归一结果跳转到不同的页面
  switch(state) {
    case '疑似':
      router.push({
        path: '/unitary/single/article/suspectDetail',
        query: { articleId }
      });
      break;
    case '新增':
      router.push({
        path: '/unitary/single/article/addDetail',
        query: { articleId }
      });
      break;
    case '挂接':
      router.push({
        path: '/unitary/single/article/relateDetail',
        query: { articleId }
      });
      break;
    default:
      console.error('未知的归一结果状态:', state);
  }
}

/** 查看日志按钮操作 */
function handleViewLog(row) {
  console.log('查看日志:', row.articleId);
}

/** 初始化 */
getList();
</script>

<style lang="scss" scoped>
.app-container {
  .content-container {
    display: flex;
    width: 100%;
    flex-direction: row;
    .left-container {
      flex: 2;                 // 占 2 份
      min-width: 0;            // 防止内容撑爆
      border-right: 1px solid #bccde0;
    }

    .right-container {
      flex: 1;                 // 占 1 份
      min-width: 0;
    }
    // $left-container-width: 1000px;
    // $right-container-width: calc(100% - $left-container-width);
    // .left-container {
    //     flex: 2;
    //     width: $left-container-width;

    //     :deep(.drawer-content-new) {
    //         width: 470px;
    //     }
    // }
    // .right-container {
    //     width: $right-container-width;
    //     min-width: $right-container-width;
    //     flex-basis: $right-container-width;
    //     border-right: 1px solid #bccde0;
    // }

    :deep(.box-container-top-menu) {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 40px;
        padding: 0 10px;
        background-color: #fff;
    }
    :deep(.menu-container) {
        font-size: 14px;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 120, 212);

        .menu-title {
            display: inline-block;
            height: 40px;
            line-height: 40px;
            border-bottom: 2px solid #0078d4;
        }

        .menu-title-btn {
            margin: 0 15px;
            padding: 4px 13px;
            border-radius: 4px;
            background-color: #fff;
            border-width: 1px;
            border-color: rgb(219, 219, 219);
            border-style: solid;
            cursor: pointer;
            box-sizing: border-box;
        }
        .menu-title-btn-gray {
            color: rgb(153, 152, 152);
            background-color: rgb(231, 235, 245);
            border-color: rgb(231, 235, 245);
        }

        .menu-title-count {
            font-size: 14px;
            font-family: "MicrosoftYaHei";
            color: rgb(0, 0, 0);
            font-style: normal;
        }

        .menu-container-icon {
            border: 1px solid rgb(219, 219, 219);
            border-radius: 4px;
            background-color: #e7ebf5;
            width: 60px;
            height: 25px;
            display: flex;
            flex-direction: row;
            align-items: center;
            overflow: hidden;

            span {
                min-width: 50%;
                width: 50%;
                height: 100%;
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: #e7ebf5;

                &.active {
                    background-color: #fff;
                }
            }
        }

    }

    :deep(.box-container-content) {
        width: 100%;
        height: 100%;
        padding: 10px;
        background-color: #eff4fa;
    }
    :deep(.box-container-content-footer) {
        text-align: right;
        span.btn {
            border-width: 1px;
            border-color: rgb(219, 219, 219);
            border-style: solid;
            border-radius: 2px;
            background-color: rgb(255, 255, 255);
            font-size: 14px;
            font-family: "MicrosoftYaHei";
            color: rgb(0, 120, 212);
            padding: 3px 8px;
            margin-left: 4px;
            cursor: pointer;
        }


    }
    :deep(.smalltabel-content .smalltabel-content-col-left) {
        width: 109px;
        min-width: 109px;
        text-align: left;
        padding-left: 14px;
    }
    :deep(.smalltabel-content .right-text-class .smalltabel-content-col-right) {
        background-color: #f9e3de;
    }
  }
}

/* 操作按钮样式 */
.operation-btn {
  display: inline-block;
  margin: 0 5px;
  color: #0076d0 !important;
  font-weight: 500;
  cursor: pointer;
  text-align: center;

  &:hover {
    text-decoration: underline;
  }
}

:deep(.el-tag) {
  border-radius: 12px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
}
</style>