<template>
    <!-- 权益信息管理 -->
    <viewBody title="期刊篇级数据">
        <template #content>
            <FormSearch :showResetBtn="false":showSearchBtn="false">
                <template #btn>
                    <el-button 
                        type="primary" class="my_primary_btn"
                        icon="Plus" @click="" > 新建规则</el-button>
                    <el-button type="primary" class="my_primary_btn"
                        icon="Download"@click="handleExport">导出规则</el-button>
                    <el-button  type="primary"class="my_primary_btn"
                        icon="Delete"@click="handleDelete()">删除数据</el-button>
                    <el-button  type="primary"class="my_primary_btn"
                        icon="Delete"@click="handleDelete()">融合规则</el-button>
                    <span style="font-size: 13px; color: red;">倒序显示？</span>
                </template>
            </FormSearch>
            <TableForm ref="tableFormRef" 
                :columns="columns"
                :tableData="dataSourceList" 
                class="tableFormContent"
                v-loading="loading" 
                :showIndex="false" 
                :total="total"
                :isShowCount="false" 
                :showEditBtn="false" 
                @cellClick="cellClick"
                :isShowSearchQuery="false"
                :is-show-selection="true"
            >
                
                <!-- <template #pagination>
                    <MyPagination
                        v-show="total>0"
                        :total="total"
                        :page="queryParams.pageNum"
                        :pageSize="queryParams.pageSize"
                        @pagination="getList"
                    />
                </template> -->

                <template #drawer>
                    <!-- 右侧的抽屉内容 -->
                    <MyDrawer ref="drawerRef">
                        <template #header="{}">
                            <div :id="titleId" :class="titleClass" class="my_drawer_title">
                            <span class="active">
                                {{ drawer.title }}
                            </span>
                            <div class="my_drawer_title_right">
                                <span 
                                class="menu-title-btn"
                                :class="{ 'menu-title-btn-gray': currentIndex <= 0 }"
                                @click="handlePrevItem"
                                >上一条</span>
                                <!-- <em class="page-info">{{ currentIndex + 1 }}/{{ total }}</em> -->
                                <em class="page-info">1/5</em>
                                <span 
                                class="menu-title-btn" 
                                style="margin-right: 0px;"
                                :class="{ 'menu-title-btn-gray': currentIndex >= total - 1 }"
                                @click="handleNextItem"
                                >下一条</span>
                                <span 
                                class="btn_add"
                                @click="handleAccept(selectData)">全部展开</span>
                            </div>
                            </div>
                        </template>
                        <template #form v-if="isDrawerZoom">
                            <ZoomTable :tableColumns="tableColumns" :tableTextArr="tableTextArr2"/>
                        </template>
                        <template #form v-if="!isDrawerZoom">
                            <!-- <div class="box-container-top-menu">
                                <div class="menu-container">
                                    <span class="menu-title">待处理数据</span>
                                </div>
                                <div class="menu-container">
                                    <span class="menu-title-btn menu-title-btn-gray">上一条</span>
                                    <em class="menu-title-count">1/5</em>
                                    <span class="menu-title-btn" style="margin-right: 0px;">下一条</span>
                                </div>
                            </div> -->
                            <div class="box-container-content">
                                <SmallTable :tableTextArr="tableTextArr"/>
                                <div class="box-container-content-footer">
                                    <span class="btn">更新入库</span>
                                    <span class="btn" @click="viewAllBox">全部展开</span>
                                    <span class="btn" @click="closeDrawer">关闭</span>
                                </div>
                            </div>
                        </template>
                    </MyDrawer>
                </template>

            </TableForm>
        </template>
    </viewBody>
</template>
<script setup>
import viewBody from "@/views/citationManager/components/viewBody.vue";
import FormSearch from "@/views/citationManager/components/FormSearch.vue";
import TableForm from "@/views/citationManager/components/TableForm.vue";
import MyDrawer from "@/views/citationManager/components/MyDrawer.vue";
import MyPagination from "@/views/citationManager/components/Pagination/new.vue";
import SmallTable from './LeftTable.vue';
import ZoomTable from './ZoomTable.vue'
//抽屉
const drawerOpen = ref(false);
const rightFormRef = ref(null)
const currentEditRow = ref(null)
const drawer = reactive({
  visible: false,
  title: '',
  type: ''
});
const {proxy} = getCurrentInstance();
// 数据源采集
// const {
//   storage_pub_type,
// } = proxy.useDict(
//     "storage_pub_type"
// );
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    name: '',
    type: '',
})
// 搜索表单配置
const formItems = computed(() => [
  {
    prop: "varietyId",
    component: "el-input",
    props: {
      placeholder: "品种ID",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "issn",
    component: "el-input",
    props: {
      placeholder: "ISSN",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "checkStatus",
    component: "el-select",
    props: {
      placeholder: "核查状态",
      clearable: true,
      style: { width: "200px" },
    },
    options: [
      { label: "全部", value: "" },
      { label: "待处理", value: "pending" },
      { label: "已处理", value: "completed" },
    ],
  },
]);
const handleQuery = (params) => {
    Object.assign(queryParams, params)
}
const resetQuery = () => {}

const getList = (page) => {
    queryParams.pageNum = page.page
    queryParams.pageSize = page.limit
    console.log(page)
}
// 表格配置
let total = ref(400)
const loading = ref(false)
const small = ref(true)
const tableFormRef = ref(null)
const columnsRightWidth = ref("200")
// 表格列配置
const columns = ref([
{ prop: 'volIssueId',      label: '卷期ID',      minWidth: '80' },
  { prop: 'volIssue',        label: '卷号/期号',   minWidth: '80' },
  { prop: 'singleStatus',    label: '单源卷期状态', minWidth: '110' },
  { prop: 'multiStatus',     label: '多源卷期状态',minWidth: '110' },
  { prop: 'diffFlag',        label: '卷期差异标识', minWidth: '110', type: 'textCircle' },
]);
/**
 * 表格数据
 */
const dataSourceList = ref([
    { volIssueId: 1,  volIssue: '1/1', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 2,  volIssue: '1/2', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 3,  volIssue: '1/3', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 4,  volIssue: '1/4', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 5,  volIssue: '2/1', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 6,  volIssue: '2/2', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 7,  volIssue: '2/3', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 8,  volIssue: '2/4', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 9,  volIssue: '3/1', singleStatus: '存在', multiStatus: '存在', diffFlag: '一致' },
  { volIssueId: 10, volIssue: '3/2', singleStatus: '缺失', multiStatus: '存在', diffFlag: '不一致' },
  { volIssueId: 11, volIssue: '3/3', singleStatus: '缺失', multiStatus: '存在', diffFlag: '不一致' },
  { volIssueId: 12, volIssue: '3/4', singleStatus: '缺失', multiStatus: '存在', diffFlag: '不一致' },
  { volIssueId: 13, volIssue: '4/1', singleStatus: '缺失', multiStatus: '存在', diffFlag: '不一致' },
  { volIssueId: 14, volIssue: '4/2', singleStatus: '缺失', multiStatus: '存在', diffFlag: '不一致' },
  { volIssueId: 15, volIssue: '4/3', singleStatus: '缺失', multiStatus: '存在', diffFlag: '不一致' },
])
// total.value = dataSourceList.value.length
/**
 *  单元格点击事件
 * @param row  
 * @param column 
 * @param cellValue 
 * @param event 
 */
const drawerRef = ref(null)
const cellClick = (row, column, cellValue, event) => {
    // console.log(row, column, cellValue, event)
}
const checkList = ref(columns.value.map(col => col.prop))
const handleColumnChange = () => {
  // 当checkList变化时自动更新显示的列
  console.log(columns.value.filter(col => checkList?.value?.includes(col.prop)))
}
// 修改打开抽屉的方法
function openDrawer(type, title) {
  drawer.type = type;
  drawer.title = title;
  drawerOpen.value = true;
  // 使用MyDrawer的openDrawer方法
  drawerRef.value.openDrawer();
}
/** 关闭抽屉 */
function closeDrawer() {
  drawer.visible = false;
  drawerOpen.value = false;
  reset();
}
/**
 *  表格行点击事件
 * @param row 
 */
// 当前选中项的索引
const currentIndex = ref(0);
const handleTableFormQuery = (row) => {
    // console.log(row)
    // drawerRef.value.openDrawer();
    drawer.title = '数据详情';
    console.log("选中的规则数据:", row);
    // open.value = true;
    drawerRef.value.openDrawer();
    selectData.value = row;
    // 更新当前索引
    currentIndex.value = ruleAnalysisList.value.findIndex(item => item.pathId === row.pathId);
}
// 关闭抽屉
// const closeDrawer = () => {
//     drawerRef.value.closeDrawer();
// }
// 展开
const isDrawerZoom = ref(false)
const viewAllBox = () => {
    isDrawerZoom.value = true;
}
const closeViewAllBox = () => {
    isDrawerZoom.value = false;
}

const tableTextArr = ref([{
        leftText: 'Source ld：',
        righttext: 'AAN'
    }, {
        leftText: 'Source Title：',
        righttext: 'Cells Tissues Organs'
    }, {
        leftText: 'ISSN：',
        righttext: '1422-6405'
    }, {
        leftText: 'Publisher Name：',
        righttext: 'S. Karger AG'
    }, {
        leftText: 'Pub Year：',
        righttext: '1998'
    }, {
        leftText: 'Volume：',
        righttext: '1'
    }, {
        leftText: 'Issue：',
        righttext: '1'
    }, {
        leftText: 'Start Page：',
        righttext: '1'
    }, {
        leftText: 'End Page：',
        righttext: '1'
    }, {
        leftText: 'DOI：',
        righttext: '10.1016/j.jbi.2019.01.004'
    }
])
const tableColumns = ref([
    { name: 'Source ld' },
    { name: 'Source Title' },
    { name: 'ISSN' },
    { name: 'Publisher Name' },
    { name: 'Pub Year' },
    { name: 'Volume' },
    { name: 'Issue' },
    { name: 'Start Page' },
    { name: 'End Page' },
    { name: 'DOI' },
])
const tableTextArr2 = ref([
    { "id": 1,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 2,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 3,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 4,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 5,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
    { "id": 6,"Source ld": "AAN","Source Title": "Cells Tissues Organs","ISSN": "1422-6405","Publisher Name": "S. Karger AG","Pub Year": "1998","Volume": "1","Issue": "1","Start Page": "1","End Page": "1","DOI": "10.1016/j.jbi.2019.01.004", },
])
</script>
<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../../source/components/base.scss";

.my_menu_content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.my_menu_title_right {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 120, 212);

    & > span {
        border: 1px solid rgb(219, 219, 219);
        border-radius: 2px;
        background-color: rgb(255, 255, 255);
        padding: 3px 10px;
        margin-right: 10px;
        cursor: pointer;
    }
}


:deep(.el-drawer__header) {
  padding: 14px 16px 10px 16px;
  margin: 0;
  border-bottom: 1px solid rgb(232, 232, 232);
}
:deep(.el-drawer__body) {
    padding: 0;
    padding-top: 10px;
}
:deep(.drawer-form) {
    padding: 0px;
}
:deep(.drawer-content-body) {
    height: 100%;
}
.my_primary_btn {
    margin-left:5px;
    padding: 8px;
  background-color: #0078d4;
  border-color: #0078d4;
}

:deep(.my_zoom_drawer) {
    width: calc(100% - 10px) !important;
}
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}
.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }
  
  .btn_add {
    background-color: rgb(0, 120, 212);
  }
  .menu-title-btn{
    margin: 0 10px;
    padding: 5px 5px;
    border-radius: 4px;
    background-color: #fff;
    border-width: 1px;
    border-color: rgb(219, 219, 219);
    border-style: solid;
    cursor: pointer;
    box-sizing: border-box;
    color: rgb(0, 120, 212);
  }
  .menu-title-btn-gray{
    margin: 0 10px;
    padding: 5px 5px;
    border-radius: 4px;
    background-color:rgb(231, 235, 245);
    border-bottom-color:rgb(231, 235, 245);
    border-width: 1px;
    // border-color: rgb(219, 219, 219);
    border-style: solid;
    cursor: pointer;
    box-sizing: border-box;
    color:rgb(153, 152, 152);
  }

  .page-info{
    // display: inline-block;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 0, 0);
    font-style: normal;
  }
}
.table-other-column {
    display: flex;
    align-items: center;
    justify-content: center;

    .table-other-column-label {
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -18px;
        margin-top: -14px;
        display: flex;
        align-items: center;
        justify-content: center;    
    }

    .table-other-column-label-left {
        margin-top: 2.5px;
    }

    :deep(.el-tooltip__trigger) {
        display: inline-block;
        width: 28px;
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -15.5px;
        height: 31px;
    }

    svg {
        position: relative;
        cursor: pointer;
    }
}
</style>
<style lang="scss">
.table-other-column-popover {
    width: auto!important;
    min-width: 113px !important;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
    border-radius: 4px;

    .el-checkbox-group {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .el-checkbox.el-checkbox--small {
        margin: 0;
    }

    .el-checkbox__label {
        font-size: 14px !important;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 0, 0);
    }

    .el-checkbox.el-checkbox--small .el-checkbox__inner {
        width: 14px!important;
        height: 14px!important;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #000 !important;
        font-weight: 700;
    }
}
</style>