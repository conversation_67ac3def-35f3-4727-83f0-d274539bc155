<template>
  <div class="app-container">
    <div class="content-container">
      <!-- 左侧待处理数据 -->
      <div class="left-panel">
        <div class="panel-header">
          <div class="title">待处理数据 1/60</div>
          <div class="actions">
            <el-button type="primary" plain icon="Plus">新增</el-button>
            <el-button type="primary" plain>挂接</el-button>
            <el-button type="danger" plain>删除</el-button>
            <el-button type="primary" plain>下一条</el-button>
          </div>
        </div>
        <div class="info-form">
          <div class="form-item">
            <label>Source Id:</label>
            <span>{{ articleInfo.sourceId }}</span>
          </div>
          <div class="form-item">
            <label>Source Title:</label>
            <span>{{ articleInfo.sourceTitle }}</span>
          </div>
          <div class="form-item">
            <label>ISSN:</label>
            <span>{{ articleInfo.issn }}</span>
          </div>
          <div class="form-item">
            <label>Publisher Name:</label>
            <span>{{ articleInfo.publisherName }}</span>
          </div>
          <div class="form-item">
            <label>Pub Year:</label>
            <span>{{ articleInfo.pubYear }}</span>
          </div>
          <div class="form-item">
            <label>Volume:</label>
            <span>{{ articleInfo.volume }}</span>
          </div>
          <div class="form-item">
            <label>Issue:</label>
            <span>{{ articleInfo.issue }}</span>
          </div>
          <div class="form-item">
            <label>Article Id:</label>
            <span>{{ articleInfo.articleId }}</span>
          </div>
          <div class="form-item">
            <label>ArticleTitle:</label>
            <span class="highlight">{{ articleInfo.articleTitle }}</span>
          </div>
          <div class="form-item">
            <label>FirstPage:</label>
            <span>{{ articleInfo.firstPage }}</span>
          </div>
          <div class="form-item">
            <label>LastPage:</label>
            <span>{{ articleInfo.lastPage }}</span>
          </div>
          <div class="form-item">
            <label>Author-FullName:</label>
            <span class="highlight">{{ articleInfo.authorFullName }}</span>
          </div>
          <div class="form-item">
            <label>Keyword:</label>
            <span>{{ articleInfo.keyword }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧疑似数据列表 -->
      <div class="right-panel">
        <div class="panel-header">
          <div class="title">疑似数据</div>
          <div class="search-bar">
            <el-input v-model="searchParams.articleId" placeholder="篇级ID" clearable />
            <el-input v-model="searchParams.articleTitle" placeholder="篇级题名" clearable />
            <el-input v-model="searchParams.issn" placeholder="ISSN" clearable />
            <el-button type="primary" icon="Search">查询</el-button>
          </div>
        </div>

        <el-table :data="tableData" style="width: 100%" border>
          <el-table-column type="selection" width="40" align="center" />
          <el-table-column label="序号" type="index" width="60" align="center" />
          <el-table-column prop="articleId" label="篇级ID" width="160" />
          <el-table-column prop="articleTitle" label="篇级题名" width="400" show-overflow-tooltip />
          <el-table-column prop="journalTitle" label="期刊题名" width="300" />
          <el-table-column prop="issn" label="ISSN" width="120" align="center" />
          <el-table-column prop="volume" label="年/卷/期" width="100" align="center" />
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { listSuspectArticles, getArticleInfo } from "@/api/unitary/unitary";

const route = useRoute();
const articleId = ref(route.query.articleId);

// 待处理数据
const articleInfo = ref({});

// 搜索参数
const searchParams = ref({
  articleId: '',
  articleTitle: '',
  issn: ''
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10
});

// 疑似数据列表
const suspectList = ref([]);
const total = ref(0);
const tableData = ref([]);

// 获取待处理数据详情
async function getDetail() {
  try {
    const res = await getArticleInfo(articleId.value);
    if (res && res.data) {
      articleInfo.value = res.data;
    }
  } catch (error) {
    console.error('获取待处理数据详情失败:', error);
  }
}

// 获取疑似数据列表
async function getList() {
  try {
    const params = {
      ...queryParams.value,
      ...searchParams.value
    };
    const res = await listSuspectArticles(params);
    if (res && res.rows) {
      tableData.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    console.error('获取疑似数据列表失败:', error);
    tableData.value = [];
    total.value = 0;
  }
}

// 搜索
function handleSearch() {
  queryParams.value.pageNum = 1;
  getList();
}

// 格式化页码范围
function formatPageRange(row) {
  if (!row.firstPage && !row.lastPage) return '-';
  if (row.firstPage === row.lastPage) return row.firstPage;
  return `${row.firstPage}-${row.lastPage}`;
}

onMounted(() => {
  if (articleId.value) {
    getDetail();
    getList();
  }
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  height: 100vh;
  background-color: #f5f7fa;
}

.content-container {
  display: flex;
  gap: 20px;
  height: calc(100% - 40px);
}

.left-panel, .right-panel {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.left-panel {
  flex: 0 0 500px;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;

    .title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
    }

    .actions {
      display: flex;
      gap: 8px;

      .el-button {
        flex: 1;
      }
    }
  }

  .info-form {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .form-item {
      display: flex;
      margin-bottom: 16px;
      font-size: 14px;
      line-height: 32px;

      label {
        width: 120px;
        text-align: right;
        padding-right: 12px;
        color: #606266;
      }

      span {
        flex: 1;
        color: #303133;

        &.highlight {
          color: #f56c6c;
          font-weight: 500;
        }
      }
    }
  }
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;

    .title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
    }

    .search-bar {
      display: flex;
      gap: 12px;

      .el-input {
        width: 200px;
      }
    }
  }

  .el-table {
    flex: 1;
    margin: 20px;
  }

  .pagination-container {
    padding: 20px;
  }
}

:deep(.el-table) {
  .el-table__header th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
    height: 44px;
  }
}
</style>