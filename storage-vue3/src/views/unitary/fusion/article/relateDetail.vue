<template>
  <div class="app-container">
    <!-- 挂接篇级数据表格 -->
    <div class="module-container">
      <div class="module-header">
        <span class="module-title">挂接篇级数据</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = false">收起</el-button>
      </div>
      <div class="module-content">
        <el-table
          :data="relatedArticles"
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          v-loading="relatedLoading"
        >
          <el-table-column label="批次号" prop="batchId" align="center" width="100" />
          <el-table-column label="篇级ID" prop="singleArticleId" align="center" width="160" />
          <el-table-column label="篇级题名" prop="articleTitle" min-width="300" show-overflow-tooltip />
          <el-table-column label="期刊题名" prop="sourceTitle" min-width="200" show-overflow-tooltip />
          <el-table-column label="ISSN" prop="issn" align="center" width="120" />
          <el-table-column label="年/卷/期" prop="interval" align="center" width="100" />
          <el-table-column label="更新时间" prop="updateTime" align="center" width="160" />
          <el-table-column label="操作" align="center" width="160">
            <template #default="scope">
              <el-button link type="primary" @click="viewDetail(scope.row)">查看详情</el-button>
              <el-button link type="primary" @click="deleteRelation(scope.row)">删除挂接</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 基本信息 -->
    <div class="module-container">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = true">收起</el-button>
      </div>
      <div class="module-content">
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">Article Id:</span>
            <span class="info-value">{{ articleInfo.singleArticleId || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Id:</span>
            <span class="info-value">{{ articleInfo.singleJournalId || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Title:</span>
            <span class="info-value">{{ articleInfo.sourceTitle || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ISSN:</span>
            <span class="info-value">{{ articleInfo.issn || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Publisher Name:</span>
            <span class="info-value">{{ articleInfo.publisherName || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Pub Year:</span>
            <span class="info-value">{{ articleInfo.year || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Volume:</span>
            <span class="info-value">{{ articleInfo.volume || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Issue:</span>
            <span class="info-value">{{ articleInfo.issue || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">DOI:</span>
            <span class="info-value">{{ articleInfo.doi || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { listRelatedArticles, getArticleBasicInfo, deleteArticleRelation } from "@/api/unitary/unitary";
import { ElMessage, ElMessageBox } from 'element-plus';

const router = useRouter();
const route = useRoute();

// 模块显示状态控制
const showBasicInfo = ref(true);

// 获取路由参数中的期刊ID
const articleId = ref(route.params.articleId || route.query.articleId);

// 基本信息数据
const articleInfo = ref({});

// 挂接篇级数据列表
const relatedArticles = ref([]);
const relatedLoading = ref(false);

// 分页参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
});

// 获取篇级详情
async function getArticleDetail() {
  try {
    const res = await getArticleBasicInfo(articleId.value);
    if (res && res.data) {
      articleInfo.value = res.data;
    }
  } catch (error) {
    console.error('获取篇级详情失败:', error);
  }
}

// 获取挂接篇级数据
async function getRelatedArticles() {
  if (!articleId.value) return;
  
  relatedLoading.value = true;
  try {
    const res = await listRelatedArticles(articleId.value, queryParams);
    if (res && res.rows) {
      relatedArticles.value = res.rows;
    }
  } catch (error) {
    console.error('获取挂接篇级数据失败:', error);
    relatedArticles.value = [];
  } finally {
    relatedLoading.value = false;
  }
}

// 查看详情
function viewDetail(row) {
  router.push({
    path: '/unitary/single/article/detail',
    query: { articleId: row.singleArticleId }
  });
}

// 删除挂接
async function deleteRelation(row) {
  try {
    await ElMessageBox.confirm('确认要删除该挂接关系吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await deleteArticleRelation(articleId.value, row.singleArticleId);
    ElMessage.success('删除成功');
    getRelatedArticles();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除挂接关系失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

// 页面加载时初始化数据
onMounted(() => {
  if (articleId.value) {
    getArticleDetail();
    getRelatedArticles();
  }
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.module-container {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.module-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.module-content {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-row {
  display: flex;
  align-items: center;
  line-height: 20px;
  font-size: 14px;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 120px;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  color: #303133;
  flex: 1;
}

:deep(.el-button--link) {
  padding: 2px 4px;
}
</style>