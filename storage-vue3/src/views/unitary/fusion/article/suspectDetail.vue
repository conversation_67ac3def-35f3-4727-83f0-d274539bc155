<template>
  <div class="app-container">
    <div class="content-container">
      <!-- 上方内容区域 -->
      <div class="top-container">
        <!-- 左侧待处理数据 -->
        <div class="detail-panel">
          <div class="panel-title">
            <span class="title-text">待处理数据 1/60</span>
            <div class="action-buttons">
              <el-button size="small" type="primary" plain icon="Plus">新增</el-button>
              <el-button size="small" type="primary" plain>挂接</el-button>
              <el-button size="small" type="danger" plain>删除</el-button>
              <el-button size="small" type="primary" plain>下一条</el-button>
            </div>
          </div>

          <div class="data-form">
            <div class="form-item">
              <label>Source Id:</label>
              <span>{{ articleInfo.sourceId }}</span>
            </div>
            <div class="form-item">
              <label>Source Title:</label>
              <span>{{ articleInfo.sourceTitle }}</span>
            </div>
            <div class="form-item">
              <label>ISSN:</label>
              <span>{{ articleInfo.issn }}</span>
            </div>
            <div class="form-item">
              <label>Publisher Name:</label>
              <span>{{ articleInfo.publisherName }}</span>
            </div>
            <div class="form-item">
              <label>Pub Year:</label>
              <span>{{ articleInfo.pubYear }}</span>
            </div>
            <div class="form-item">
              <label>Volume:</label>
              <span>{{ articleInfo.volume }}</span>
            </div>
            <div class="form-item">
              <label>Issue:</label>
              <span>{{ articleInfo.issue }}</span>
            </div>
            <div class="form-item">
              <label>Article Id:</label>
              <span>{{ articleInfo.articleId }}</span>
            </div>
            <div class="form-item">
              <label>ArticleTitle:</label>
              <span class="highlight">{{ articleInfo.articleTitle }}</span>
            </div>
            <div class="form-item">
              <label>FirstPage:</label>
              <span>{{ articleInfo.firstPage }}</span>
            </div>
            <div class="form-item">
              <label>LastPage:</label>
              <span>{{ articleInfo.lastPage }}</span>
            </div>
            <div class="form-item">
              <label>Author-FullName:</label>
              <span class="highlight">{{ articleInfo.authorFullName }}</span>
            </div>
            <div class="form-item">
              <label>Keyword:</label>
              <span>{{ articleInfo.keyword }}</span>
            </div>
          </div>
        </div>

        <!-- 右侧疑似数据详情 -->
        <div class="detail-panel">
          <div class="panel-title">
            <span class="title-text">疑似数据详情 {{ currentPage }}/{{ total }}</span>
            <div class="action-buttons">
              <el-button size="small" type="primary" plain @click="handlePrev" :disabled="!hasPrev">上一条</el-button>
              <el-button size="small" type="primary" plain @click="handleNext" :disabled="!hasNext">下一条</el-button>
              <el-button size="small" type="primary" plain>更新入库</el-button>
            </div>
          </div>

          <div class="data-form">
            <div class="form-item">
              <label>Pub Year:</label>
              <span>{{ selectedRow.pubYear }}</span>
            </div>
            <div class="form-item">
              <label>Volume:</label>
              <span>{{ selectedRow.volume }}</span>
            </div>
            <div class="form-item">
              <label>Issue:</label>
              <span>{{ selectedRow.issue }}</span>
            </div>
            <div class="form-item">
              <label>Article Id:</label>
              <span>{{ selectedRow.articleId }}</span>
            </div>
            <div class="form-item">
              <label>ArticleTitle:</label>
              <span class="highlight">{{ selectedRow.articleTitle }}</span>
            </div>
            <div class="form-item">
              <label>FirstPage:</label>
              <span>{{ selectedRow.firstPage }}</span>
            </div>
            <div class="form-item">
              <label>LastPage:</label>
              <span>{{ selectedRow.lastPage }}</span>
            </div>
            <div class="form-item">
              <label>Author-FullName:</label>
              <span class="highlight">{{ selectedRow.authorFullName }}</span>
            </div>
            <div class="form-item">
              <label>Keyword:</label>
              <span>{{ selectedRow.keyword1 }}</span>
            </div>
            <div class="form-item">
              <label>Keyword:</label>
              <span>{{ selectedRow.keyword2 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 下方疑似数据列表 -->
      <div class="bottom-container">
        <div class="panel-title">
          <span class="title-text">疑似数据列表</span>
          <div class="search-area">
            <el-input v-model="searchParams.articleId" placeholder="篇级ID" clearable />
            <el-input v-model="searchParams.articleTitle" placeholder="篇级题名" clearable />
            <el-input v-model="searchParams.issn" placeholder="ISSN" clearable />
            <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
          </div>
        </div>

        <div class="table-area">
          <el-table 
            :data="tableData" 
            border
            highlight-current-row
            @current-change="handleRowClick"
          >
            <el-table-column type="selection" width="40" align="center" />
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column prop="articleId" label="篇级ID" min-width="120" />
            <el-table-column prop="articleTitle" label="篇级题名" min-width="300" show-overflow-tooltip />
            <el-table-column prop="journalTitle" label="期刊题名" min-width="200" show-overflow-tooltip />
            <el-table-column prop="issn" label="P-ISSN" width="120" align="center" />
            <el-table-column prop="eissn" label="E-ISSN" width="120" align="center" />
            <el-table-column prop="volume" label="年/卷/期" width="100" align="center" />
          </el-table>

          <div class="pagination-container">
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { listSuspectArticles, getArticleInfo } from "@/api/unitary/unitary";

const route = useRoute();
const articleId = ref(route.query.articleId);

// 待处理数据
const articleInfo = ref({});

// 搜索参数
const searchParams = ref({
  articleId: '',
  articleTitle: '',
  issn: ''
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10
});

// 疑似数据列表
const tableData = ref([]);
const total = ref(0);
const selectedRow = ref({});
const currentIndex = ref(-1);

// 计算属性
const currentPage = computed(() => currentIndex.value + 1);
const hasPrev = computed(() => currentIndex.value > 0);
const hasNext = computed(() => currentIndex.value < tableData.value.length - 1);

// 获取待处理数据详情
async function getDetail() {
  try {
    const res = await getArticleInfo(articleId.value);
    if (res && res.data) {
      articleInfo.value = res.data;
    }
  } catch (error) {
    console.error('获取待处理数据详情失败:', error);
  }
}

// 获取疑似数据列表
async function getList() {
  try {
    const params = {
      ...queryParams.value,
      ...searchParams.value
    };
    const res = await listSuspectArticles(params);
    if (res && res.rows) {
      tableData.value = res.rows;
      total.value = res.total;
      // 默认选中第一行
      if (tableData.value.length > 0) {
        selectedRow.value = tableData.value[0];
        currentIndex.value = 0;
      }
    }
  } catch (error) {
    console.error('获取疑似数据列表失败:', error);
    tableData.value = [];
    total.value = 0;
    selectedRow.value = {};
    currentIndex.value = -1;
  }
}

// 搜索
function handleSearch() {
  queryParams.value.pageNum = 1;
  getList();
}

// 处理行点击
function handleRowClick(row) {
  if (!row) return;
  selectedRow.value = row;
  currentIndex.value = tableData.value.findIndex(item => item.articleId === row.articleId);
}

// 上一条
function handlePrev() {
  if (!hasPrev.value) return;
  currentIndex.value--;
  selectedRow.value = tableData.value[currentIndex.value];
}

// 下一条
function handleNext() {
  if (!hasNext.value) return;
  currentIndex.value++;
  selectedRow.value = tableData.value[currentIndex.value];
}

onMounted(() => {
  if (articleId.value) {
    getDetail();
    getList();
  }
});
</script>

<style lang="scss" scoped>
.app-container {
  height: 100vh;
  background-color: #f5f7fa;
  padding: 10px;
}

.content-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 10px;
}

.top-container {
  display: flex;
  gap: 10px;
  height: 350px;
}

.bottom-container {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-panel {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-title {
  border-left: 4px solid #409EFF;
  padding: 10px 15px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ebeef5;

  .title-text {
    display: flex;
    align-items: center;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
  
  .search-area {
    display: flex;
    gap: 10px;
    
    .el-input {
      width: 180px;
    }
  }
}

.data-form {
  padding: 15px;
  overflow-y: auto;
  flex: 1;

  .form-item {
    display: flex;
    margin-bottom: 15px;
    
    label {
      width: 120px;
      text-align: right;
      padding-right: 12px;
      color: #606266;
    }
    
    span {
      flex: 1;
      color: #303133;
      
      &.highlight {
        color: #f56c6c;
      }
    }
  }
}

.table-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px;
  overflow: hidden;
  
  .el-table {
    flex: 1;
  }
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table) {
  .el-table__header th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
    height: 40px;
  }
}
</style>