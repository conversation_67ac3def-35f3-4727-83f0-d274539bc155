<template>
  <div class="app-container">
    <!-- 使用FormSearch组件替换原有搜索表单 -->
    <FormSearch 
      ref="formSearchRef" 
      v-model="queryParams"
      :formItems="formItems"
      @search="handleQuery" 
      @reset="resetQuery"
    />

    <!-- 使用TableForm组件替换原有表格 -->
    <TableForm
      ref="tableFormRef"
      :columns="columns"
      :tableData="articleList"
      :total="total"
      v-loading="loading"
      :showIndex="true"
      :showOtherColumn="true"
      tableotherColumnLabel="操作"
      :isShowCount="true"
      :isShowSearchQuery="false"
    >
      <template #state="{ row }">
        <el-tag type="danger" v-if="row.state === '0' || row.state === '疑似'">疑似</el-tag>
        <el-tag type="success" v-else-if="row.state === '1' || row.state === '新增'">新增</el-tag>
        <el-tag type="warning" v-else-if="row.state === '2' || row.state === '挂接'">挂接</el-tag>
        <span v-else>-</span>
      </template>

      <template #otherOperation="{ row }">
        <span class="operation-btn" @click="handleProcess(row)">处理</span>
        <span class="operation-btn" @click="handleViewLog(row)">日志</span>
      </template>

      <template #pagination>
        <MyPagination
          v-if="total > 0"
          :total="total"
          :page="queryParams.pageNum"
          :limit="queryParams.pageSize"
          @pagination="getList"
        />
      </template>
    </TableForm>
  </div>
</template>

<script setup name="unifyArticle">
import { ref } from 'vue';
import { listArticles } from "@/api/unitary/unitary";
import { useRouter } from 'vue-router';
import FormSearch from '@/views/source/components/FormSearch.vue';
import TableForm from '@/views/source/components/TableForm.vue';
import MyPagination from "@/components/Pagination/new.vue";

const router = useRouter();
const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const articleList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  articleTitle: undefined,
  sourceTitle: undefined,
  dataSource: undefined,
  state: undefined
});

// 搜索表单配置
const formItems = ref([
  {
    prop: 'articleTitle',
    component: 'el-input',
    props: {
      placeholder: '请输入篇级题名',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    prop: 'sourceTitle',
    component: 'el-input',
    props: {
      placeholder: '请输入期刊题名',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    prop: 'dataSource',
    component: 'el-input',
    props: {
      placeholder: '请输入数据源',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    prop: 'state',
    component: 'el-select',
    props: {
      placeholder: '数据状态',
      clearable: true,
      style: {width: '200px'},
    },
    children: () => [
      { value: '0', label: '疑似' },
      { value: '1', label: '新增' },
      { value: '2', label: '挂接' }
    ]
  }
]);

// 表格列配置
const columns = ref([
  {prop: 'articleId', label: '篇级ID', width: '150', showOverflowTooltip: true},
  {prop: 'articleTitle', label: '篇级题名', width: '200', showOverflowTooltip: true},
  {prop: 'sourceTitle', label: '期刊题名', width: '180', showOverflowTooltip: true},
  {prop: 'issn', label: 'ISSN', width: '100', showOverflowTooltip: true},
  {prop: 'interval', label: '年/卷/期', width: '100', showOverflowTooltip: true},
  {prop: 'dataSource', label: '数据源', width: '100'},
  {prop: 'state', label: '归一结果', width: '100'},
  {prop: 'remark', label: '疑似说明', width: '150', showOverflowTooltip: true},
  {prop: 'updateTime', label: '更新时间', width: '160', showOverflowTooltip: true}
]);

/** 查询文章列表 */
async function getList() {
  loading.value = true;
  try {
    const res = await listArticles(queryParams.value);
    articleList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取列表失败:', error);
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    articleTitle: undefined,
    sourceTitle: undefined,
    dataSource: undefined,
    state: undefined
  };
  handleQuery();
}

/** 处理按钮操作 */
function handleProcess(row) {
  const { articleId, state } = row;
  
  // 根据不同的归一结果跳转到不同的页面
  switch(state) {
    case '疑似':
      router.push({
        path: '/unitary/single/article/suspectDetail',
        query: { articleId }
      });
      break;
    case '新增':
      router.push({
        path: '/unitary/single/article/addDetail',
        query: { articleId }
      });
      break;
    case '挂接':
      router.push({
        path: '/unitary/single/article/relateDetail',
        query: { articleId }
      });
      break;
    default:
      console.error('未知的归一结果状态:', state);
  }
}

/** 查看日志按钮操作 */
function handleViewLog(row) {
  console.log('查看日志:', row.articleId);
}

/** 初始化 */
getList();
</script>

<style lang="scss" scoped>
.app-container {
  //padding: 20px;
  //background-color: #f5f7fa;
}

/* 操作按钮样式 */
.operation-btn {
  display: inline-block;
  margin: 0 5px;
  color: #0076d0 !important;
  font-weight: 500;
  cursor: pointer;
  text-align: center;

  &:hover {
    text-decoration: underline;
  }
}

:deep(.el-tag) {
  border-radius: 12px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
}
</style>